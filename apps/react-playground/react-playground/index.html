<!DOCTYPE html><html lang="en"><head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/svg+xml" href="/subs/react-playground/vite.svg">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React PlayGround</title>
    <script crossorigin="">import('/subs/react-playground/js/index-BfvTmYEU.js').finally(() => {
            
    const qiankunLifeCycle = window.moudleQiankunAppLifeCycles && window.moudleQiankunAppLifeCycles['react-playground'];
    if (qiankunLifeCycle) {
      window.proxy.vitemount((props) => qiankunLifeCycle.mount(props));
      window.proxy.viteunmount((props) => qiankunLifeCycle.unmount(props));
      window.proxy.vitebootstrap(() => qiankunLifeCycle.bootstrap());
      window.proxy.viteupdate((props) => qiankunLifeCycle.update(props));
    }
  
          })</script>
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/@<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/react-dom@<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/allotment@1.20.4_react-dom@<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/@<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/@monaco-editor_react@4.7.0-rc.0_monaco-editor@0.52.2_react-dom@<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/@typescript_ata@<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/vite-plugin-qiankun@1.0.15_typescript@<EMAIL>">
    <link rel="stylesheet" crossorigin="" href="/subs/react-playground/css/allotment@1.20.4_react-dom@<EMAIL>">
    <link rel="stylesheet" crossorigin="" href="/subs/react-playground/css/<EMAIL>">
    <link rel="stylesheet" crossorigin="" href="/subs/react-playground/css/index-B7td_v4_.css">
  </head>
  <body>
    <div id="root"></div>
  

<script>
  const createDeffer = (hookName) => {
    const d = new Promise((resolve, reject) => {
      window.proxy && (window.proxy[`vite${hookName}`] = resolve)
    })
    return props => d.then(fn => fn(props));
  }
  const bootstrap = createDeffer('bootstrap');
  const mount = createDeffer('mount');
  const unmount = createDeffer('unmount');
  const update = createDeffer('update');

  ;(global => {
    global.qiankunName = 'react-playground';
    global['react-playground'] = {
      bootstrap,
      mount,
      unmount,
      update
    };
  })(window);
</script></body></html>