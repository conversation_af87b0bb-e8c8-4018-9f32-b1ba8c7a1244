import{g as c}from"./babel-standalone-mTViEsrM.js";var s={exports:{}};/*!
	Copyright (c) 2018 <PERSON>.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var a;function p(){return a||(a=1,(function(o){(function(){var f={}.hasOwnProperty;function e(){for(var t="",r=0;r<arguments.length;r++){var n=arguments[r];n&&(t=i(t,u(n)))}return t}function u(t){if(typeof t=="string"||typeof t=="number")return t;if(typeof t!="object")return"";if(Array.isArray(t))return e.apply(null,t);if(t.toString!==Object.prototype.toString&&!t.toString.toString().includes("[native code]"))return t.toString();var r="";for(var n in t)f.call(t,n)&&t[n]&&(r=i(r,n));return r}function i(t,r){return r?t?t+" "+r:t+r:t}o.exports?(e.default=e,o.exports=e):window.classNames=e})()})(s)),s.exports}var l=p();const x=c(l);export{x as c};
