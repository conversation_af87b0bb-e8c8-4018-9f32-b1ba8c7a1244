import{r as C2}from"./<EMAIL>";import{c as Y2}from"./<EMAIL>";import{_ as q2,l as W0,F as V2}from"./monaco-editor-DFXwtQzY.js";import{c as Th}from"./<EMAIL>";import{d as Ah,i as L2}from"./<EMAIL>";import{g as G2,b as X2}from"./babel-standalone-mTViEsrM.js";import{h as k0,t as Q2}from"./typescript-BMfN2Q0e.js";import{s as xh,u as Z2,a as Oh,z as K2}from"./<EMAIL>";import{J as J2}from"./<EMAIL>";import{F as $2}from"./<EMAIL>";import{s as W2}from"./@typescript_ata@<EMAIL>";import"./<EMAIL>";import"./<EMAIL>";import"./index-D4n58RHW.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const _ of document.querySelectorAll('link[rel="modulepreload"]'))f(_);new MutationObserver(_=>{for(const M of _)if(M.type==="childList")for(const j of M.addedNodes)j.tagName==="LINK"&&j.rel==="modulepreload"&&f(j)}).observe(document,{childList:!0,subtree:!0});function o(_){const M={};return _.integrity&&(M.integrity=_.integrity),_.referrerPolicy&&(M.referrerPolicy=_.referrerPolicy),_.crossOrigin==="use-credentials"?M.credentials="include":_.crossOrigin==="anonymous"?M.credentials="omit":M.credentials="same-origin",M}function f(_){if(_.ep)return;_.ep=!0;const M=o(_);fetch(_.href,M)}})();var cs={exports:{}},Uu={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var F0;function k2(){if(F0)return Uu;F0=1;var d=Symbol.for("react.transitional.element"),s=Symbol.for("react.fragment");function o(f,_,M){var j=null;if(M!==void 0&&(j=""+M),_.key!==void 0&&(j=""+_.key),"key"in _){M={};for(var U in _)U!=="key"&&(M[U]=_[U])}else M=_;return _=M.ref,{$$typeof:d,type:f,key:j,ref:_!==void 0?_:null,props:M}}return Uu.Fragment=s,Uu.jsx=o,Uu.jsxs=o,Uu}var I0;function F2(){return I0||(I0=1,cs.exports=k2()),cs.exports}var P=F2(),fs={exports:{}},ju={},ss={exports:{}},ot={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var P0;function I2(){if(P0)return ot;P0=1;var d=Symbol.for("react.transitional.element"),s=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),f=Symbol.for("react.strict_mode"),_=Symbol.for("react.profiler"),M=Symbol.for("react.consumer"),j=Symbol.for("react.context"),U=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),z=Symbol.for("react.lazy"),O=Symbol.iterator;function H(g){return g===null||typeof g!="object"?null:(g=O&&g[O]||g["@@iterator"],typeof g=="function"?g:null)}var B={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},C=Object.assign,Y={};function G(g,N,Q){this.props=g,this.context=N,this.refs=Y,this.updater=Q||B}G.prototype.isReactComponent={},G.prototype.setState=function(g,N){if(typeof g!="object"&&typeof g!="function"&&g!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,g,N,"setState")},G.prototype.forceUpdate=function(g){this.updater.enqueueForceUpdate(this,g,"forceUpdate")};function X(){}X.prototype=G.prototype;function $(g,N,Q){this.props=g,this.context=N,this.refs=Y,this.updater=Q||B}var k=$.prototype=new X;k.constructor=$,C(k,G.prototype),k.isPureReactComponent=!0;var ut=Array.isArray,tt={H:null,A:null,T:null,S:null,V:null},Ct=Object.prototype.hasOwnProperty;function w(g,N,Q,L,et,yt){return Q=yt.ref,{$$typeof:d,type:g,key:N,ref:Q!==void 0?Q:null,props:yt}}function st(g,N){return w(g.type,N,void 0,void 0,void 0,g.props)}function Vt(g){return typeof g=="object"&&g!==null&&g.$$typeof===d}function Dt(g){var N={"=":"=0",":":"=2"};return"$"+g.replace(/[=:]/g,function(Q){return N[Q]})}var oe=/\/+/g;function rt(g,N){return typeof g=="object"&&g!==null&&g.key!=null?Dt(""+g.key):N.toString(36)}function ht(){}function At(g){switch(g.status){case"fulfilled":return g.value;case"rejected":throw g.reason;default:switch(typeof g.status=="string"?g.then(ht,ht):(g.status="pending",g.then(function(N){g.status==="pending"&&(g.status="fulfilled",g.value=N)},function(N){g.status==="pending"&&(g.status="rejected",g.reason=N)})),g.status){case"fulfilled":return g.value;case"rejected":throw g.reason}}throw g}function ft(g,N,Q,L,et){var yt=typeof g;(yt==="undefined"||yt==="boolean")&&(g=null);var lt=!1;if(g===null)lt=!0;else switch(yt){case"bigint":case"string":case"number":lt=!0;break;case"object":switch(g.$$typeof){case d:case s:lt=!0;break;case z:return lt=g._init,ft(lt(g._payload),N,Q,L,et)}}if(lt)return et=et(g),lt=L===""?"."+rt(g,0):L,ut(et)?(Q="",lt!=null&&(Q=lt.replace(oe,"$&/")+"/"),ft(et,N,Q,"",function(it){return it})):et!=null&&(Vt(et)&&(et=st(et,Q+(et.key==null||g&&g.key===et.key?"":(""+et.key).replace(oe,"$&/")+"/")+lt)),N.push(et)),1;lt=0;var kt=L===""?".":L+":";if(ut(g))for(var Z=0;Z<g.length;Z++)L=g[Z],yt=kt+rt(L,Z),lt+=ft(L,N,Q,yt,et);else if(Z=H(g),typeof Z=="function")for(g=Z.call(g),Z=0;!(L=g.next()).done;)L=L.value,yt=kt+rt(L,Z++),lt+=ft(L,N,Q,yt,et);else if(yt==="object"){if(typeof g.then=="function")return ft(At(g),N,Q,L,et);throw N=String(g),Error("Objects are not valid as a React child (found: "+(N==="[object Object]"?"object with keys {"+Object.keys(g).join(", ")+"}":N)+"). If you meant to render a collection of children, use an array instead.")}return lt}function V(g,N,Q){if(g==null)return g;var L=[],et=0;return ft(g,L,"","",function(yt){return N.call(Q,yt,et++)}),L}function F(g){if(g._status===-1){var N=g._result;N=N(),N.then(function(Q){(g._status===0||g._status===-1)&&(g._status=1,g._result=Q)},function(Q){(g._status===0||g._status===-1)&&(g._status=2,g._result=Q)}),g._status===-1&&(g._status=0,g._result=N)}if(g._status===1)return g._result.default;throw g._result}var xt=typeof reportError=="function"?reportError:function(g){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var N=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof g=="object"&&g!==null&&typeof g.message=="string"?String(g.message):String(g),error:g});if(!window.dispatchEvent(N))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",g);return}console.error(g)};function Oe(){}return ot.Children={map:V,forEach:function(g,N,Q){V(g,function(){N.apply(this,arguments)},Q)},count:function(g){var N=0;return V(g,function(){N++}),N},toArray:function(g){return V(g,function(N){return N})||[]},only:function(g){if(!Vt(g))throw Error("React.Children.only expected to receive a single React element child.");return g}},ot.Component=G,ot.Fragment=o,ot.Profiler=_,ot.PureComponent=$,ot.StrictMode=f,ot.Suspense=y,ot.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=tt,ot.__COMPILER_RUNTIME={__proto__:null,c:function(g){return tt.H.useMemoCache(g)}},ot.cache=function(g){return function(){return g.apply(null,arguments)}},ot.cloneElement=function(g,N,Q){if(g==null)throw Error("The argument must be a React element, but you passed "+g+".");var L=C({},g.props),et=g.key,yt=void 0;if(N!=null)for(lt in N.ref!==void 0&&(yt=void 0),N.key!==void 0&&(et=""+N.key),N)!Ct.call(N,lt)||lt==="key"||lt==="__self"||lt==="__source"||lt==="ref"&&N.ref===void 0||(L[lt]=N[lt]);var lt=arguments.length-2;if(lt===1)L.children=Q;else if(1<lt){for(var kt=Array(lt),Z=0;Z<lt;Z++)kt[Z]=arguments[Z+2];L.children=kt}return w(g.type,et,void 0,void 0,yt,L)},ot.createContext=function(g){return g={$$typeof:j,_currentValue:g,_currentValue2:g,_threadCount:0,Provider:null,Consumer:null},g.Provider=g,g.Consumer={$$typeof:M,_context:g},g},ot.createElement=function(g,N,Q){var L,et={},yt=null;if(N!=null)for(L in N.key!==void 0&&(yt=""+N.key),N)Ct.call(N,L)&&L!=="key"&&L!=="__self"&&L!=="__source"&&(et[L]=N[L]);var lt=arguments.length-2;if(lt===1)et.children=Q;else if(1<lt){for(var kt=Array(lt),Z=0;Z<lt;Z++)kt[Z]=arguments[Z+2];et.children=kt}if(g&&g.defaultProps)for(L in lt=g.defaultProps,lt)et[L]===void 0&&(et[L]=lt[L]);return w(g,yt,void 0,void 0,null,et)},ot.createRef=function(){return{current:null}},ot.forwardRef=function(g){return{$$typeof:U,render:g}},ot.isValidElement=Vt,ot.lazy=function(g){return{$$typeof:z,_payload:{_status:-1,_result:g},_init:F}},ot.memo=function(g,N){return{$$typeof:h,type:g,compare:N===void 0?null:N}},ot.startTransition=function(g){var N=tt.T,Q={};tt.T=Q;try{var L=g(),et=tt.S;et!==null&&et(Q,L),typeof L=="object"&&L!==null&&typeof L.then=="function"&&L.then(Oe,xt)}catch(yt){xt(yt)}finally{tt.T=N}},ot.unstable_useCacheRefresh=function(){return tt.H.useCacheRefresh()},ot.use=function(g){return tt.H.use(g)},ot.useActionState=function(g,N,Q){return tt.H.useActionState(g,N,Q)},ot.useCallback=function(g,N){return tt.H.useCallback(g,N)},ot.useContext=function(g){return tt.H.useContext(g)},ot.useDebugValue=function(){},ot.useDeferredValue=function(g,N){return tt.H.useDeferredValue(g,N)},ot.useEffect=function(g,N,Q){var L=tt.H;if(typeof Q=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return L.useEffect(g,N)},ot.useId=function(){return tt.H.useId()},ot.useImperativeHandle=function(g,N,Q){return tt.H.useImperativeHandle(g,N,Q)},ot.useInsertionEffect=function(g,N){return tt.H.useInsertionEffect(g,N)},ot.useLayoutEffect=function(g,N){return tt.H.useLayoutEffect(g,N)},ot.useMemo=function(g,N){return tt.H.useMemo(g,N)},ot.useOptimistic=function(g,N){return tt.H.useOptimistic(g,N)},ot.useReducer=function(g,N,Q){return tt.H.useReducer(g,N,Q)},ot.useRef=function(g){return tt.H.useRef(g)},ot.useState=function(g){return tt.H.useState(g)},ot.useSyncExternalStore=function(g,N,Q){return tt.H.useSyncExternalStore(g,N,Q)},ot.useTransition=function(){return tt.H.useTransition()},ot.version="19.1.1",ot}var th;function zs(){return th||(th=1,ss.exports=I2()),ss.exports}var rs={exports:{}},ge={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var eh;function P2(){if(eh)return ge;eh=1;var d=zs();function s(y){var h="https://react.dev/errors/"+y;if(1<arguments.length){h+="?args[]="+encodeURIComponent(arguments[1]);for(var z=2;z<arguments.length;z++)h+="&args[]="+encodeURIComponent(arguments[z])}return"Minified React error #"+y+"; visit "+h+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var f={d:{f:o,r:function(){throw Error(s(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},_=Symbol.for("react.portal");function M(y,h,z){var O=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:_,key:O==null?null:""+O,children:y,containerInfo:h,implementation:z}}var j=d.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function U(y,h){if(y==="font")return"";if(typeof h=="string")return h==="use-credentials"?h:""}return ge.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=f,ge.createPortal=function(y,h){var z=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!h||h.nodeType!==1&&h.nodeType!==9&&h.nodeType!==11)throw Error(s(299));return M(y,h,null,z)},ge.flushSync=function(y){var h=j.T,z=f.p;try{if(j.T=null,f.p=2,y)return y()}finally{j.T=h,f.p=z,f.d.f()}},ge.preconnect=function(y,h){typeof y=="string"&&(h?(h=h.crossOrigin,h=typeof h=="string"?h==="use-credentials"?h:"":void 0):h=null,f.d.C(y,h))},ge.prefetchDNS=function(y){typeof y=="string"&&f.d.D(y)},ge.preinit=function(y,h){if(typeof y=="string"&&h&&typeof h.as=="string"){var z=h.as,O=U(z,h.crossOrigin),H=typeof h.integrity=="string"?h.integrity:void 0,B=typeof h.fetchPriority=="string"?h.fetchPriority:void 0;z==="style"?f.d.S(y,typeof h.precedence=="string"?h.precedence:void 0,{crossOrigin:O,integrity:H,fetchPriority:B}):z==="script"&&f.d.X(y,{crossOrigin:O,integrity:H,fetchPriority:B,nonce:typeof h.nonce=="string"?h.nonce:void 0})}},ge.preinitModule=function(y,h){if(typeof y=="string")if(typeof h=="object"&&h!==null){if(h.as==null||h.as==="script"){var z=U(h.as,h.crossOrigin);f.d.M(y,{crossOrigin:z,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0})}}else h==null&&f.d.M(y)},ge.preload=function(y,h){if(typeof y=="string"&&typeof h=="object"&&h!==null&&typeof h.as=="string"){var z=h.as,O=U(z,h.crossOrigin);f.d.L(y,z,{crossOrigin:O,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0,type:typeof h.type=="string"?h.type:void 0,fetchPriority:typeof h.fetchPriority=="string"?h.fetchPriority:void 0,referrerPolicy:typeof h.referrerPolicy=="string"?h.referrerPolicy:void 0,imageSrcSet:typeof h.imageSrcSet=="string"?h.imageSrcSet:void 0,imageSizes:typeof h.imageSizes=="string"?h.imageSizes:void 0,media:typeof h.media=="string"?h.media:void 0})}},ge.preloadModule=function(y,h){if(typeof y=="string")if(h){var z=U(h.as,h.crossOrigin);f.d.m(y,{as:typeof h.as=="string"&&h.as!=="script"?h.as:void 0,crossOrigin:z,integrity:typeof h.integrity=="string"?h.integrity:void 0})}else f.d.m(y)},ge.requestFormReset=function(y){f.d.r(y)},ge.unstable_batchedUpdates=function(y,h){return y(h)},ge.useFormState=function(y,h,z){return j.H.useFormState(y,h,z)},ge.useFormStatus=function(){return j.H.useHostTransitionStatus()},ge.version="19.1.1",ge}var lh;function tv(){if(lh)return rs.exports;lh=1;function d(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(d)}catch(s){console.error(s)}}return d(),rs.exports=P2(),rs.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ah;function ev(){if(ah)return ju;ah=1;var d=C2(),s=zs(),o=tv();function f(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function _(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function M(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function j(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function U(t){if(M(t)!==t)throw Error(f(188))}function y(t){var e=t.alternate;if(!e){if(e=M(t),e===null)throw Error(f(188));return e!==t?null:t}for(var l=t,a=e;;){var n=l.return;if(n===null)break;var u=n.alternate;if(u===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===l)return U(n),t;if(u===a)return U(n),e;u=u.sibling}throw Error(f(188))}if(l.return!==a.return)l=n,a=u;else{for(var i=!1,c=n.child;c;){if(c===l){i=!0,l=n,a=u;break}if(c===a){i=!0,a=n,l=u;break}c=c.sibling}if(!i){for(c=u.child;c;){if(c===l){i=!0,l=u,a=n;break}if(c===a){i=!0,a=u,l=n;break}c=c.sibling}if(!i)throw Error(f(189))}}if(l.alternate!==a)throw Error(f(190))}if(l.tag!==3)throw Error(f(188));return l.stateNode.current===l?t:e}function h(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=h(t),e!==null)return e;t=t.sibling}return null}var z=Object.assign,O=Symbol.for("react.element"),H=Symbol.for("react.transitional.element"),B=Symbol.for("react.portal"),C=Symbol.for("react.fragment"),Y=Symbol.for("react.strict_mode"),G=Symbol.for("react.profiler"),X=Symbol.for("react.provider"),$=Symbol.for("react.consumer"),k=Symbol.for("react.context"),ut=Symbol.for("react.forward_ref"),tt=Symbol.for("react.suspense"),Ct=Symbol.for("react.suspense_list"),w=Symbol.for("react.memo"),st=Symbol.for("react.lazy"),Vt=Symbol.for("react.activity"),Dt=Symbol.for("react.memo_cache_sentinel"),oe=Symbol.iterator;function rt(t){return t===null||typeof t!="object"?null:(t=oe&&t[oe]||t["@@iterator"],typeof t=="function"?t:null)}var ht=Symbol.for("react.client.reference");function At(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===ht?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case C:return"Fragment";case G:return"Profiler";case Y:return"StrictMode";case tt:return"Suspense";case Ct:return"SuspenseList";case Vt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case B:return"Portal";case k:return(t.displayName||"Context")+".Provider";case $:return(t._context.displayName||"Context")+".Consumer";case ut:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case w:return e=t.displayName||null,e!==null?e:At(t.type)||"Memo";case st:e=t._payload,t=t._init;try{return At(t(e))}catch{}}return null}var ft=Array.isArray,V=s.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,F=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,xt={pending:!1,data:null,method:null,action:null},Oe=[],g=-1;function N(t){return{current:t}}function Q(t){0>g||(t.current=Oe[g],Oe[g]=null,g--)}function L(t,e){g++,Oe[g]=t.current,t.current=e}var et=N(null),yt=N(null),lt=N(null),kt=N(null);function Z(t,e){switch(L(lt,e),L(yt,t),L(et,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?E0(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=E0(e),t=T0(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Q(et),L(et,t)}function it(){Q(et),Q(yt),Q(lt)}function jt(t){t.memoizedState!==null&&L(kt,t);var e=et.current,l=T0(e,t.type);e!==l&&(L(yt,t),L(et,l))}function Ht(t){yt.current===t&&(Q(et),Q(yt)),kt.current===t&&(Q(kt),Ou._currentValue=xt)}var Xa=Object.prototype.hasOwnProperty,Bl=d.unstable_scheduleCallback,Qa=d.unstable_cancelCallback,Yu=d.unstable_shouldYield,ac=d.unstable_requestPaint,le=d.unstable_now,qu=d.unstable_getCurrentPriorityLevel,Vu=d.unstable_ImmediatePriority,Lu=d.unstable_UserBlockingPriority,Cl=d.unstable_NormalPriority,pa=d.unstable_LowPriority,Za=d.unstable_IdlePriority,Cn=d.log,Yn=d.unstable_setDisableYieldValue,tl=null,pe=null;function il(t){if(typeof Cn=="function"&&Yn(t),pe&&typeof pe.setStrictMode=="function")try{pe.setStrictMode(tl,t)}catch{}}var Se=Math.clz32?Math.clz32:Gu,nc=Math.log,uc=Math.LN2;function Gu(t){return t>>>=0,t===0?32:31-(nc(t)/uc|0)|0}var Yl=256,Ye=4194304;function ce(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function qe(t,e,l){var a=t.pendingLanes;if(a===0)return 0;var n=0,u=t.suspendedLanes,i=t.pingedLanes;t=t.warmLanes;var c=a&134217727;return c!==0?(a=c&~u,a!==0?n=ce(a):(i&=c,i!==0?n=ce(i):l||(l=c&~t,l!==0&&(n=ce(l))))):(c=a&~u,c!==0?n=ce(c):i!==0?n=ce(i):l||(l=a&~t,l!==0&&(n=ce(l)))),n===0?0:e!==0&&e!==n&&(e&u)===0&&(u=n&-n,l=e&-e,u>=l||u===32&&(l&4194048)!==0)?e:n}function cl(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function fl(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Xu(){var t=Yl;return Yl<<=1,(Yl&4194048)===0&&(Yl=256),t}function Sa(){var t=Ye;return Ye<<=1,(Ye&62914560)===0&&(Ye=4194304),t}function gl(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function ql(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Qu(t,e,l,a,n,u){var i=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var c=t.entanglements,r=t.expirationTimes,b=t.hiddenUpdates;for(l=i&~l;0<l;){var x=31-Se(l),R=1<<x;c[x]=0,r[x]=-1;var E=b[x];if(E!==null)for(b[x]=null,x=0;x<E.length;x++){var T=E[x];T!==null&&(T.lane&=-536870913)}l&=~R}a!==0&&Zu(t,a,0),u!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=u&~(i&~e))}function Zu(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-Se(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|l&4194090}function Ku(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var a=31-Se(l),n=1<<a;n&e|t[a]&e&&(t[a]|=e),l&=~n}}function Ka(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function Ja(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function ba(){var t=F.p;return t!==0?t:(t=window.event,t===void 0?32:X0(t.type))}function Vl(t,e){var l=F.p;try{return F.p=t,e()}finally{F.p=l}}var sl=Math.random().toString(36).slice(2),Yt="__reactFiber$"+sl,he="__reactProps$"+sl,_e="__reactContainer$"+sl,$a="__reactEvents$"+sl,ic="__reactListeners$"+sl,Wa="__reactHandles$"+sl,ka="__reactResources$"+sl,Ll="__reactMarker$"+sl;function Fa(t){delete t[Yt],delete t[he],delete t[$a],delete t[ic],delete t[Wa]}function pl(t){var e=t[Yt];if(e)return e;for(var l=t.parentNode;l;){if(e=l[_e]||l[Yt]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=M0(t);t!==null;){if(l=t[Yt])return l;t=M0(t)}return e}t=l,l=t.parentNode}return null}function Ve(t){if(t=t[Yt]||t[_e]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Gl(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(f(33))}function Xl(t){var e=t[ka];return e||(e=t[ka]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function v(t){t[Ll]=!0}var A=new Set,q={};function K(t,e){bt(t,e),bt(t+"Capture",e)}function bt(t,e){for(q[t]=e,t=0;t<e.length;t++)A.add(e[t])}var vt=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),qt={},Qt={};function Me(t){return Xa.call(Qt,t)?!0:Xa.call(qt,t)?!1:vt.test(t)?Qt[t]=!0:(qt[t]=!0,!1)}function Lt(t,e,l){if(Me(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function Gt(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function Ft(t,e,l,a){if(a===null)t.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+a)}}var De,ae;function de(t){if(De===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);De=e&&e[1]||"",ae=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+De+t+ae}var rl=!1;function Ql(t,e){if(!t||rl)return"";rl=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var R=function(){throw Error()};if(Object.defineProperty(R.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(R,[])}catch(T){var E=T}Reflect.construct(t,[],R)}else{try{R.call()}catch(T){E=T}t.call(R.prototype)}}else{try{throw Error()}catch(T){E=T}(R=t())&&typeof R.catch=="function"&&R.catch(function(){})}}catch(T){if(T&&E&&typeof T.stack=="string")return[T.stack,E.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),i=u[0],c=u[1];if(i&&c){var r=i.split(`
`),b=c.split(`
`);for(n=a=0;a<r.length&&!r[a].includes("DetermineComponentFrameRoot");)a++;for(;n<b.length&&!b[n].includes("DetermineComponentFrameRoot");)n++;if(a===r.length||n===b.length)for(a=r.length-1,n=b.length-1;1<=a&&0<=n&&r[a]!==b[n];)n--;for(;1<=a&&0<=n;a--,n--)if(r[a]!==b[n]){if(a!==1||n!==1)do if(a--,n--,0>n||r[a]!==b[n]){var x=`
`+r[a].replace(" at new "," at ");return t.displayName&&x.includes("<anonymous>")&&(x=x.replace("<anonymous>",t.displayName)),x}while(1<=a&&0<=n);break}}}finally{rl=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?de(l):""}function Zl(t){switch(t.tag){case 26:case 27:case 5:return de(t.type);case 16:return de("Lazy");case 13:return de("Suspense");case 19:return de("SuspenseList");case 0:case 15:return Ql(t.type,!1);case 11:return Ql(t.type.render,!1);case 1:return Ql(t.type,!0);case 31:return de("Activity");default:return""}}function Kl(t){try{var e="";do e+=Zl(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function be(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Ju(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function cc(t){var e=Ju(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,u=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(i){a=""+i,u.call(this,i)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(i){a=""+i},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function zt(t){t._valueTracker||(t._valueTracker=cc(t))}function _t(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),a="";return t&&(a=Ju(t)?t.checked?"true":"false":t.value),t=a,t!==l?(e.setValue(t),!0):!1}function Sl(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var ol=/[\n"\\]/g;function ve(t){return t.replace(ol,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function me(t,e,l,a,n,u,i,c){t.name="",i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"?t.type=i:t.removeAttribute("type"),e!=null?i==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+be(e)):t.value!==""+be(e)&&(t.value=""+be(e)):i!=="submit"&&i!=="reset"||t.removeAttribute("value"),e!=null?Ge(t,i,be(e)):l!=null?Ge(t,i,be(l)):a!=null&&t.removeAttribute("value"),n==null&&u!=null&&(t.defaultChecked=!!u),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"?t.name=""+be(c):t.removeAttribute("name")}function Le(t,e,l,a,n,u,i,c){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;l=l!=null?""+be(l):"",e=e!=null?""+be(e):l,c||e===t.value||(t.value=e),t.defaultValue=e}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=c?t.checked:!!a,t.defaultChecked=!!a,i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(t.name=i)}function Ge(t,e,l){e==="number"&&Sl(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function Xe(t,e,l,a){if(t=t.options,e){e={};for(var n=0;n<l.length;n++)e["$"+l[n]]=!0;for(l=0;l<t.length;l++)n=e.hasOwnProperty("$"+t[l].value),t[l].selected!==n&&(t[l].selected=n),n&&a&&(t[l].defaultSelected=!0)}else{for(l=""+be(l),e=null,n=0;n<t.length;n++){if(t[n].value===l){t[n].selected=!0,a&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function za(t,e,l){if(e!=null&&(e=""+be(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+be(l):""}function Ia(t,e,l,a){if(e==null){if(a!=null){if(l!=null)throw Error(f(92));if(ft(a)){if(1<a.length)throw Error(f(93));a=a[0]}l=a}l==null&&(l=""),e=l}l=be(e),t.defaultValue=l,a=t.textContent,a===l&&a!==""&&a!==null&&(t.value=a)}function el(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var bl=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function qn(t,e,l){var a=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,l):typeof l!="number"||l===0||bl.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function _a(t,e,l){if(e!=null&&typeof e!="object")throw Error(f(62));if(t=t.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var n in e)a=e[n],e.hasOwnProperty(n)&&l[n]!==a&&qn(t,n,a)}else for(var u in e)e.hasOwnProperty(u)&&qn(t,u,e[u])}function Ea(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Pa=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),tn=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ta(t){return tn.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Jl=null;function $l(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var en=null,ln=null;function Ts(t){var e=Ve(t);if(e&&(t=e.stateNode)){var l=t[he]||null;t:switch(t=e.stateNode,e.type){case"input":if(me(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+ve(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var a=l[e];if(a!==t&&a.form===t.form){var n=a[he]||null;if(!n)throw Error(f(90));me(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<l.length;e++)a=l[e],a.form===t.form&&_t(a)}break t;case"textarea":za(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&Xe(t,!!l.multiple,e,!1)}}}var fc=!1;function As(t,e,l){if(fc)return t(e,l);fc=!0;try{var a=t(e);return a}finally{if(fc=!1,(en!==null||ln!==null)&&(Ui(),en&&(e=en,t=ln,ln=en=null,Ts(e),t)))for(e=0;e<t.length;e++)Ts(t[e])}}function Vn(t,e){var l=t.stateNode;if(l===null)return null;var a=l[he]||null;if(a===null)return null;l=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(f(231,e,typeof l));return l}var zl=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),sc=!1;if(zl)try{var Ln={};Object.defineProperty(Ln,"passive",{get:function(){sc=!0}}),window.addEventListener("test",Ln,Ln),window.removeEventListener("test",Ln,Ln)}catch{sc=!1}var Wl=null,rc=null,$u=null;function xs(){if($u)return $u;var t,e=rc,l=e.length,a,n="value"in Wl?Wl.value:Wl.textContent,u=n.length;for(t=0;t<l&&e[t]===n[t];t++);var i=l-t;for(a=1;a<=i&&e[l-a]===n[u-a];a++);return $u=n.slice(t,1<a?1-a:void 0)}function Wu(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function ku(){return!0}function Os(){return!1}function Ee(t){function e(l,a,n,u,i){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=u,this.target=i,this.currentTarget=null;for(var c in t)t.hasOwnProperty(c)&&(l=t[c],this[c]=l?l(u):u[c]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?ku:Os,this.isPropagationStopped=Os,this}return z(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=ku)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=ku)},persist:function(){},isPersistent:ku}),e}var Aa={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Fu=Ee(Aa),Gn=z({},Aa,{view:0,detail:0}),Ch=Ee(Gn),oc,hc,Xn,Iu=z({},Gn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:vc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Xn&&(Xn&&t.type==="mousemove"?(oc=t.screenX-Xn.screenX,hc=t.screenY-Xn.screenY):hc=oc=0,Xn=t),oc)},movementY:function(t){return"movementY"in t?t.movementY:hc}}),Ms=Ee(Iu),Yh=z({},Iu,{dataTransfer:0}),qh=Ee(Yh),Vh=z({},Gn,{relatedTarget:0}),dc=Ee(Vh),Lh=z({},Aa,{animationName:0,elapsedTime:0,pseudoElement:0}),Gh=Ee(Lh),Xh=z({},Aa,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Qh=Ee(Xh),Zh=z({},Aa,{data:0}),Ds=Ee(Zh),Kh={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Jh={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},$h={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Wh(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=$h[t])?!!e[t]:!1}function vc(){return Wh}var kh=z({},Gn,{key:function(t){if(t.key){var e=Kh[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Wu(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Jh[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:vc,charCode:function(t){return t.type==="keypress"?Wu(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Wu(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Fh=Ee(kh),Ih=z({},Iu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ns=Ee(Ih),Ph=z({},Gn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:vc}),td=Ee(Ph),ed=z({},Aa,{propertyName:0,elapsedTime:0,pseudoElement:0}),ld=Ee(ed),ad=z({},Iu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),nd=Ee(ad),ud=z({},Aa,{newState:0,oldState:0}),id=Ee(ud),cd=[9,13,27,32],mc=zl&&"CompositionEvent"in window,Qn=null;zl&&"documentMode"in document&&(Qn=document.documentMode);var fd=zl&&"TextEvent"in window&&!Qn,Rs=zl&&(!mc||Qn&&8<Qn&&11>=Qn),Us=" ",js=!1;function ws(t,e){switch(t){case"keyup":return cd.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Hs(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var an=!1;function sd(t,e){switch(t){case"compositionend":return Hs(e);case"keypress":return e.which!==32?null:(js=!0,Us);case"textInput":return t=e.data,t===Us&&js?null:t;default:return null}}function rd(t,e){if(an)return t==="compositionend"||!mc&&ws(t,e)?(t=xs(),$u=rc=Wl=null,an=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Rs&&e.locale!=="ko"?null:e.data;default:return null}}var od={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Bs(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!od[t.type]:e==="textarea"}function Cs(t,e,l,a){en?ln?ln.push(a):ln=[a]:en=a,e=Yi(e,"onChange"),0<e.length&&(l=new Fu("onChange","change",null,l,a),t.push({event:l,listeners:e}))}var Zn=null,Kn=null;function hd(t){p0(t,0)}function Pu(t){var e=Gl(t);if(_t(e))return t}function Ys(t,e){if(t==="change")return e}var qs=!1;if(zl){var yc;if(zl){var gc="oninput"in document;if(!gc){var Vs=document.createElement("div");Vs.setAttribute("oninput","return;"),gc=typeof Vs.oninput=="function"}yc=gc}else yc=!1;qs=yc&&(!document.documentMode||9<document.documentMode)}function Ls(){Zn&&(Zn.detachEvent("onpropertychange",Gs),Kn=Zn=null)}function Gs(t){if(t.propertyName==="value"&&Pu(Kn)){var e=[];Cs(e,Kn,t,$l(t)),As(hd,e)}}function dd(t,e,l){t==="focusin"?(Ls(),Zn=e,Kn=l,Zn.attachEvent("onpropertychange",Gs)):t==="focusout"&&Ls()}function vd(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Pu(Kn)}function md(t,e){if(t==="click")return Pu(e)}function yd(t,e){if(t==="input"||t==="change")return Pu(e)}function gd(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Ne=typeof Object.is=="function"?Object.is:gd;function Jn(t,e){if(Ne(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),a=Object.keys(e);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!Xa.call(e,n)||!Ne(t[n],e[n]))return!1}return!0}function Xs(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Qs(t,e){var l=Xs(t);t=0;for(var a;l;){if(l.nodeType===3){if(a=t+l.textContent.length,t<=e&&a>=e)return{node:l,offset:e-t};t=a}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=Xs(l)}}function Zs(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Zs(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Ks(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Sl(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=Sl(t.document)}return e}function pc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var pd=zl&&"documentMode"in document&&11>=document.documentMode,nn=null,Sc=null,$n=null,bc=!1;function Js(t,e,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;bc||nn==null||nn!==Sl(a)||(a=nn,"selectionStart"in a&&pc(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),$n&&Jn($n,a)||($n=a,a=Yi(Sc,"onSelect"),0<a.length&&(e=new Fu("onSelect","select",null,e,l),t.push({event:e,listeners:a}),e.target=nn)))}function xa(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var un={animationend:xa("Animation","AnimationEnd"),animationiteration:xa("Animation","AnimationIteration"),animationstart:xa("Animation","AnimationStart"),transitionrun:xa("Transition","TransitionRun"),transitionstart:xa("Transition","TransitionStart"),transitioncancel:xa("Transition","TransitionCancel"),transitionend:xa("Transition","TransitionEnd")},zc={},$s={};zl&&($s=document.createElement("div").style,"AnimationEvent"in window||(delete un.animationend.animation,delete un.animationiteration.animation,delete un.animationstart.animation),"TransitionEvent"in window||delete un.transitionend.transition);function Oa(t){if(zc[t])return zc[t];if(!un[t])return t;var e=un[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in $s)return zc[t]=e[l];return t}var Ws=Oa("animationend"),ks=Oa("animationiteration"),Fs=Oa("animationstart"),Sd=Oa("transitionrun"),bd=Oa("transitionstart"),zd=Oa("transitioncancel"),Is=Oa("transitionend"),Ps=new Map,_c="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");_c.push("scrollEnd");function ll(t,e){Ps.set(t,e),K(e,[t])}var tr=new WeakMap;function Qe(t,e){if(typeof t=="object"&&t!==null){var l=tr.get(t);return l!==void 0?l:(e={value:t,source:e,stack:Kl(e)},tr.set(t,e),e)}return{value:t,source:e,stack:Kl(e)}}var Ze=[],cn=0,Ec=0;function ti(){for(var t=cn,e=Ec=cn=0;e<t;){var l=Ze[e];Ze[e++]=null;var a=Ze[e];Ze[e++]=null;var n=Ze[e];Ze[e++]=null;var u=Ze[e];if(Ze[e++]=null,a!==null&&n!==null){var i=a.pending;i===null?n.next=n:(n.next=i.next,i.next=n),a.pending=n}u!==0&&er(l,n,u)}}function ei(t,e,l,a){Ze[cn++]=t,Ze[cn++]=e,Ze[cn++]=l,Ze[cn++]=a,Ec|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function Tc(t,e,l,a){return ei(t,e,l,a),li(t)}function fn(t,e){return ei(t,null,null,e),li(t)}function er(t,e,l){t.lanes|=l;var a=t.alternate;a!==null&&(a.lanes|=l);for(var n=!1,u=t.return;u!==null;)u.childLanes|=l,a=u.alternate,a!==null&&(a.childLanes|=l),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(n=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,n&&e!==null&&(n=31-Se(l),t=u.hiddenUpdates,a=t[n],a===null?t[n]=[e]:a.push(e),e.lane=l|536870912),u):null}function li(t){if(50<Su)throw Su=0,Rf=null,Error(f(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var sn={};function _d(t,e,l,a){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Re(t,e,l,a){return new _d(t,e,l,a)}function Ac(t){return t=t.prototype,!(!t||!t.isReactComponent)}function _l(t,e){var l=t.alternate;return l===null?(l=Re(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function lr(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function ai(t,e,l,a,n,u){var i=0;if(a=t,typeof t=="function")Ac(t)&&(i=1);else if(typeof t=="string")i=T2(t,l,et.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Vt:return t=Re(31,l,e,n),t.elementType=Vt,t.lanes=u,t;case C:return Ma(l.children,n,u,e);case Y:i=8,n|=24;break;case G:return t=Re(12,l,e,n|2),t.elementType=G,t.lanes=u,t;case tt:return t=Re(13,l,e,n),t.elementType=tt,t.lanes=u,t;case Ct:return t=Re(19,l,e,n),t.elementType=Ct,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case X:case k:i=10;break t;case $:i=9;break t;case ut:i=11;break t;case w:i=14;break t;case st:i=16,a=null;break t}i=29,l=Error(f(130,t===null?"null":typeof t,"")),a=null}return e=Re(i,l,e,n),e.elementType=t,e.type=a,e.lanes=u,e}function Ma(t,e,l,a){return t=Re(7,t,a,e),t.lanes=l,t}function xc(t,e,l){return t=Re(6,t,null,e),t.lanes=l,t}function Oc(t,e,l){return e=Re(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var rn=[],on=0,ni=null,ui=0,Ke=[],Je=0,Da=null,El=1,Tl="";function Na(t,e){rn[on++]=ui,rn[on++]=ni,ni=t,ui=e}function ar(t,e,l){Ke[Je++]=El,Ke[Je++]=Tl,Ke[Je++]=Da,Da=t;var a=El;t=Tl;var n=32-Se(a)-1;a&=~(1<<n),l+=1;var u=32-Se(e)+n;if(30<u){var i=n-n%5;u=(a&(1<<i)-1).toString(32),a>>=i,n-=i,El=1<<32-Se(e)+n|l<<n|a,Tl=u+t}else El=1<<u|l<<n|a,Tl=t}function Mc(t){t.return!==null&&(Na(t,1),ar(t,1,0))}function Dc(t){for(;t===ni;)ni=rn[--on],rn[on]=null,ui=rn[--on],rn[on]=null;for(;t===Da;)Da=Ke[--Je],Ke[Je]=null,Tl=Ke[--Je],Ke[Je]=null,El=Ke[--Je],Ke[Je]=null}var ze=null,Kt=null,Tt=!1,Ra=null,hl=!1,Nc=Error(f(519));function Ua(t){var e=Error(f(418,""));throw Fn(Qe(e,t)),Nc}function nr(t){var e=t.stateNode,l=t.type,a=t.memoizedProps;switch(e[Yt]=t,e[he]=a,l){case"dialog":pt("cancel",e),pt("close",e);break;case"iframe":case"object":case"embed":pt("load",e);break;case"video":case"audio":for(l=0;l<zu.length;l++)pt(zu[l],e);break;case"source":pt("error",e);break;case"img":case"image":case"link":pt("error",e),pt("load",e);break;case"details":pt("toggle",e);break;case"input":pt("invalid",e),Le(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),zt(e);break;case"select":pt("invalid",e);break;case"textarea":pt("invalid",e),Ia(e,a.value,a.defaultValue,a.children),zt(e)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||a.suppressHydrationWarning===!0||_0(e.textContent,l)?(a.popover!=null&&(pt("beforetoggle",e),pt("toggle",e)),a.onScroll!=null&&pt("scroll",e),a.onScrollEnd!=null&&pt("scrollend",e),a.onClick!=null&&(e.onclick=qi),e=!0):e=!1,e||Ua(t)}function ur(t){for(ze=t.return;ze;)switch(ze.tag){case 5:case 13:hl=!1;return;case 27:case 3:hl=!0;return;default:ze=ze.return}}function Wn(t){if(t!==ze)return!1;if(!Tt)return ur(t),Tt=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||Jf(t.type,t.memoizedProps)),l=!l),l&&Kt&&Ua(t),ur(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(f(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){Kt=nl(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}Kt=null}}else e===27?(e=Kt,oa(t.type)?(t=Ff,Ff=null,Kt=t):Kt=e):Kt=ze?nl(t.stateNode.nextSibling):null;return!0}function kn(){Kt=ze=null,Tt=!1}function ir(){var t=Ra;return t!==null&&(xe===null?xe=t:xe.push.apply(xe,t),Ra=null),t}function Fn(t){Ra===null?Ra=[t]:Ra.push(t)}var Rc=N(null),ja=null,Al=null;function kl(t,e,l){L(Rc,e._currentValue),e._currentValue=l}function xl(t){t._currentValue=Rc.current,Q(Rc)}function Uc(t,e,l){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===l)break;t=t.return}}function jc(t,e,l,a){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var u=n.dependencies;if(u!==null){var i=n.child;u=u.firstContext;t:for(;u!==null;){var c=u;u=n;for(var r=0;r<e.length;r++)if(c.context===e[r]){u.lanes|=l,c=u.alternate,c!==null&&(c.lanes|=l),Uc(u.return,l,t),a||(i=null);break t}u=c.next}}else if(n.tag===18){if(i=n.return,i===null)throw Error(f(341));i.lanes|=l,u=i.alternate,u!==null&&(u.lanes|=l),Uc(i,l,t),i=null}else i=n.child;if(i!==null)i.return=n;else for(i=n;i!==null;){if(i===t){i=null;break}if(n=i.sibling,n!==null){n.return=i.return,i=n;break}i=i.return}n=i}}function In(t,e,l,a){t=null;for(var n=e,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var i=n.alternate;if(i===null)throw Error(f(387));if(i=i.memoizedProps,i!==null){var c=n.type;Ne(n.pendingProps.value,i.value)||(t!==null?t.push(c):t=[c])}}else if(n===kt.current){if(i=n.alternate,i===null)throw Error(f(387));i.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(Ou):t=[Ou])}n=n.return}t!==null&&jc(e,t,l,a),e.flags|=262144}function ii(t){for(t=t.firstContext;t!==null;){if(!Ne(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function wa(t){ja=t,Al=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function ye(t){return cr(ja,t)}function ci(t,e){return ja===null&&wa(t),cr(t,e)}function cr(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},Al===null){if(t===null)throw Error(f(308));Al=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Al=Al.next=e;return l}var Ed=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},Td=d.unstable_scheduleCallback,Ad=d.unstable_NormalPriority,te={$$typeof:k,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function wc(){return{controller:new Ed,data:new Map,refCount:0}}function Pn(t){t.refCount--,t.refCount===0&&Td(Ad,function(){t.controller.abort()})}var tu=null,Hc=0,hn=0,dn=null;function xd(t,e){if(tu===null){var l=tu=[];Hc=0,hn=Yf(),dn={status:"pending",value:void 0,then:function(a){l.push(a)}}}return Hc++,e.then(fr,fr),e}function fr(){if(--Hc===0&&tu!==null){dn!==null&&(dn.status="fulfilled");var t=tu;tu=null,hn=0,dn=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Od(t,e){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var n=0;n<l.length;n++)(0,l[n])(e)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var sr=V.S;V.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&xd(t,e),sr!==null&&sr(t,e)};var Ha=N(null);function Bc(){var t=Ha.current;return t!==null?t:Bt.pooledCache}function fi(t,e){e===null?L(Ha,Ha.current):L(Ha,e.pool)}function rr(){var t=Bc();return t===null?null:{parent:te._currentValue,pool:t}}var eu=Error(f(460)),or=Error(f(474)),si=Error(f(542)),Cc={then:function(){}};function hr(t){return t=t.status,t==="fulfilled"||t==="rejected"}function ri(){}function dr(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(ri,ri),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,mr(t),t;default:if(typeof e.status=="string")e.then(ri,ri);else{if(t=Bt,t!==null&&100<t.shellSuspendCounter)throw Error(f(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=a}},function(a){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,mr(t),t}throw lu=e,eu}}var lu=null;function vr(){if(lu===null)throw Error(f(459));var t=lu;return lu=null,t}function mr(t){if(t===eu||t===si)throw Error(f(483))}var Fl=!1;function Yc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function qc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Il(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Pl(t,e,l){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(Ot&2)!==0){var n=a.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),a.pending=e,e=li(t),er(t,null,l),e}return ei(t,a,e,l),li(t)}function au(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,Ku(t,l)}}function Vc(t,e){var l=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var i={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?n=u=i:u=u.next=i,l=l.next}while(l!==null);u===null?n=u=e:u=u.next=e}else n=u=e;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var Lc=!1;function nu(){if(Lc){var t=dn;if(t!==null)throw t}}function uu(t,e,l,a){Lc=!1;var n=t.updateQueue;Fl=!1;var u=n.firstBaseUpdate,i=n.lastBaseUpdate,c=n.shared.pending;if(c!==null){n.shared.pending=null;var r=c,b=r.next;r.next=null,i===null?u=b:i.next=b,i=r;var x=t.alternate;x!==null&&(x=x.updateQueue,c=x.lastBaseUpdate,c!==i&&(c===null?x.firstBaseUpdate=b:c.next=b,x.lastBaseUpdate=r))}if(u!==null){var R=n.baseState;i=0,x=b=r=null,c=u;do{var E=c.lane&-536870913,T=E!==c.lane;if(T?(St&E)===E:(a&E)===E){E!==0&&E===hn&&(Lc=!0),x!==null&&(x=x.next={lane:0,tag:c.tag,payload:c.payload,callback:null,next:null});t:{var ct=t,at=c;E=e;var Ut=l;switch(at.tag){case 1:if(ct=at.payload,typeof ct=="function"){R=ct.call(Ut,R,E);break t}R=ct;break t;case 3:ct.flags=ct.flags&-65537|128;case 0:if(ct=at.payload,E=typeof ct=="function"?ct.call(Ut,R,E):ct,E==null)break t;R=z({},R,E);break t;case 2:Fl=!0}}E=c.callback,E!==null&&(t.flags|=64,T&&(t.flags|=8192),T=n.callbacks,T===null?n.callbacks=[E]:T.push(E))}else T={lane:E,tag:c.tag,payload:c.payload,callback:c.callback,next:null},x===null?(b=x=T,r=R):x=x.next=T,i|=E;if(c=c.next,c===null){if(c=n.shared.pending,c===null)break;T=c,c=T.next,T.next=null,n.lastBaseUpdate=T,n.shared.pending=null}}while(!0);x===null&&(r=R),n.baseState=r,n.firstBaseUpdate=b,n.lastBaseUpdate=x,u===null&&(n.shared.lanes=0),ca|=i,t.lanes=i,t.memoizedState=R}}function yr(t,e){if(typeof t!="function")throw Error(f(191,t));t.call(e)}function gr(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)yr(l[t],e)}var vn=N(null),oi=N(0);function pr(t,e){t=jl,L(oi,t),L(vn,e),jl=t|e.baseLanes}function Gc(){L(oi,jl),L(vn,vn.current)}function Xc(){jl=oi.current,Q(vn),Q(oi)}var ta=0,dt=null,Nt=null,It=null,hi=!1,mn=!1,Ba=!1,di=0,iu=0,yn=null,Md=0;function $t(){throw Error(f(321))}function Qc(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!Ne(t[l],e[l]))return!1;return!0}function Zc(t,e,l,a,n,u){return ta=u,dt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,V.H=t===null||t.memoizedState===null?eo:lo,Ba=!1,u=l(a,n),Ba=!1,mn&&(u=br(e,l,a,n)),Sr(t),u}function Sr(t){V.H=Si;var e=Nt!==null&&Nt.next!==null;if(ta=0,It=Nt=dt=null,hi=!1,iu=0,yn=null,e)throw Error(f(300));t===null||ne||(t=t.dependencies,t!==null&&ii(t)&&(ne=!0))}function br(t,e,l,a){dt=t;var n=0;do{if(mn&&(yn=null),iu=0,mn=!1,25<=n)throw Error(f(301));if(n+=1,It=Nt=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}V.H=Hd,u=e(l,a)}while(mn);return u}function Dd(){var t=V.H,e=t.useState()[0];return e=typeof e.then=="function"?cu(e):e,t=t.useState()[0],(Nt!==null?Nt.memoizedState:null)!==t&&(dt.flags|=1024),e}function Kc(){var t=di!==0;return di=0,t}function Jc(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function $c(t){if(hi){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}hi=!1}ta=0,It=Nt=dt=null,mn=!1,iu=di=0,yn=null}function Te(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return It===null?dt.memoizedState=It=t:It=It.next=t,It}function Pt(){if(Nt===null){var t=dt.alternate;t=t!==null?t.memoizedState:null}else t=Nt.next;var e=It===null?dt.memoizedState:It.next;if(e!==null)It=e,Nt=t;else{if(t===null)throw dt.alternate===null?Error(f(467)):Error(f(310));Nt=t,t={memoizedState:Nt.memoizedState,baseState:Nt.baseState,baseQueue:Nt.baseQueue,queue:Nt.queue,next:null},It===null?dt.memoizedState=It=t:It=It.next=t}return It}function Wc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function cu(t){var e=iu;return iu+=1,yn===null&&(yn=[]),t=dr(yn,t,e),e=dt,(It===null?e.memoizedState:It.next)===null&&(e=e.alternate,V.H=e===null||e.memoizedState===null?eo:lo),t}function vi(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return cu(t);if(t.$$typeof===k)return ye(t)}throw Error(f(438,String(t)))}function kc(t){var e=null,l=dt.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var a=dt.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=Wc(),dt.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),a=0;a<t;a++)l[a]=Dt;return e.index++,l}function Ol(t,e){return typeof e=="function"?e(t):e}function mi(t){var e=Pt();return Fc(e,Nt,t)}function Fc(t,e,l){var a=t.queue;if(a===null)throw Error(f(311));a.lastRenderedReducer=l;var n=t.baseQueue,u=a.pending;if(u!==null){if(n!==null){var i=n.next;n.next=u.next,u.next=i}e.baseQueue=n=u,a.pending=null}if(u=t.baseState,n===null)t.memoizedState=u;else{e=n.next;var c=i=null,r=null,b=e,x=!1;do{var R=b.lane&-536870913;if(R!==b.lane?(St&R)===R:(ta&R)===R){var E=b.revertLane;if(E===0)r!==null&&(r=r.next={lane:0,revertLane:0,action:b.action,hasEagerState:b.hasEagerState,eagerState:b.eagerState,next:null}),R===hn&&(x=!0);else if((ta&E)===E){b=b.next,E===hn&&(x=!0);continue}else R={lane:0,revertLane:b.revertLane,action:b.action,hasEagerState:b.hasEagerState,eagerState:b.eagerState,next:null},r===null?(c=r=R,i=u):r=r.next=R,dt.lanes|=E,ca|=E;R=b.action,Ba&&l(u,R),u=b.hasEagerState?b.eagerState:l(u,R)}else E={lane:R,revertLane:b.revertLane,action:b.action,hasEagerState:b.hasEagerState,eagerState:b.eagerState,next:null},r===null?(c=r=E,i=u):r=r.next=E,dt.lanes|=R,ca|=R;b=b.next}while(b!==null&&b!==e);if(r===null?i=u:r.next=c,!Ne(u,t.memoizedState)&&(ne=!0,x&&(l=dn,l!==null)))throw l;t.memoizedState=u,t.baseState=i,t.baseQueue=r,a.lastRenderedState=u}return n===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function Ic(t){var e=Pt(),l=e.queue;if(l===null)throw Error(f(311));l.lastRenderedReducer=t;var a=l.dispatch,n=l.pending,u=e.memoizedState;if(n!==null){l.pending=null;var i=n=n.next;do u=t(u,i.action),i=i.next;while(i!==n);Ne(u,e.memoizedState)||(ne=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),l.lastRenderedState=u}return[u,a]}function zr(t,e,l){var a=dt,n=Pt(),u=Tt;if(u){if(l===void 0)throw Error(f(407));l=l()}else l=e();var i=!Ne((Nt||n).memoizedState,l);i&&(n.memoizedState=l,ne=!0),n=n.queue;var c=Tr.bind(null,a,n,t);if(fu(2048,8,c,[t]),n.getSnapshot!==e||i||It!==null&&It.memoizedState.tag&1){if(a.flags|=2048,gn(9,yi(),Er.bind(null,a,n,l,e),null),Bt===null)throw Error(f(349));u||(ta&124)!==0||_r(a,e,l)}return l}function _r(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=dt.updateQueue,e===null?(e=Wc(),dt.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function Er(t,e,l,a){e.value=l,e.getSnapshot=a,Ar(e)&&xr(t)}function Tr(t,e,l){return l(function(){Ar(e)&&xr(t)})}function Ar(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!Ne(t,l)}catch{return!0}}function xr(t){var e=fn(t,2);e!==null&&Be(e,t,2)}function Pc(t){var e=Te();if(typeof t=="function"){var l=t;if(t=l(),Ba){il(!0);try{l()}finally{il(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ol,lastRenderedState:t},e}function Or(t,e,l,a){return t.baseState=l,Fc(t,Nt,typeof a=="function"?a:Ol)}function Nd(t,e,l,a,n){if(pi(t))throw Error(f(485));if(t=e.action,t!==null){var u={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(i){u.listeners.push(i)}};V.T!==null?l(!0):u.isTransition=!1,a(u),l=e.pending,l===null?(u.next=e.pending=u,Mr(e,u)):(u.next=l.next,e.pending=l.next=u)}}function Mr(t,e){var l=e.action,a=e.payload,n=t.state;if(e.isTransition){var u=V.T,i={};V.T=i;try{var c=l(n,a),r=V.S;r!==null&&r(i,c),Dr(t,e,c)}catch(b){tf(t,e,b)}finally{V.T=u}}else try{u=l(n,a),Dr(t,e,u)}catch(b){tf(t,e,b)}}function Dr(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){Nr(t,e,a)},function(a){return tf(t,e,a)}):Nr(t,e,l)}function Nr(t,e,l){e.status="fulfilled",e.value=l,Rr(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,Mr(t,l)))}function tf(t,e,l){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=l,Rr(e),e=e.next;while(e!==a)}t.action=null}function Rr(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Ur(t,e){return e}function jr(t,e){if(Tt){var l=Bt.formState;if(l!==null){t:{var a=dt;if(Tt){if(Kt){e:{for(var n=Kt,u=hl;n.nodeType!==8;){if(!u){n=null;break e}if(n=nl(n.nextSibling),n===null){n=null;break e}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){Kt=nl(n.nextSibling),a=n.data==="F!";break t}}Ua(a)}a=!1}a&&(e=l[0])}}return l=Te(),l.memoizedState=l.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ur,lastRenderedState:e},l.queue=a,l=Ir.bind(null,dt,a),a.dispatch=l,a=Pc(!1),u=uf.bind(null,dt,!1,a.queue),a=Te(),n={state:e,dispatch:null,action:t,pending:null},a.queue=n,l=Nd.bind(null,dt,n,u,l),n.dispatch=l,a.memoizedState=t,[e,l,!1]}function wr(t){var e=Pt();return Hr(e,Nt,t)}function Hr(t,e,l){if(e=Fc(t,e,Ur)[0],t=mi(Ol)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=cu(e)}catch(i){throw i===eu?si:i}else a=e;e=Pt();var n=e.queue,u=n.dispatch;return l!==e.memoizedState&&(dt.flags|=2048,gn(9,yi(),Rd.bind(null,n,l),null)),[a,u,t]}function Rd(t,e){t.action=e}function Br(t){var e=Pt(),l=Nt;if(l!==null)return Hr(e,l,t);Pt(),e=e.memoizedState,l=Pt();var a=l.queue.dispatch;return l.memoizedState=t,[e,a,!1]}function gn(t,e,l,a){return t={tag:t,create:l,deps:a,inst:e,next:null},e=dt.updateQueue,e===null&&(e=Wc(),dt.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(a=l.next,l.next=t,t.next=a,e.lastEffect=t),t}function yi(){return{destroy:void 0,resource:void 0}}function Cr(){return Pt().memoizedState}function gi(t,e,l,a){var n=Te();a=a===void 0?null:a,dt.flags|=t,n.memoizedState=gn(1|e,yi(),l,a)}function fu(t,e,l,a){var n=Pt();a=a===void 0?null:a;var u=n.memoizedState.inst;Nt!==null&&a!==null&&Qc(a,Nt.memoizedState.deps)?n.memoizedState=gn(e,u,l,a):(dt.flags|=t,n.memoizedState=gn(1|e,u,l,a))}function Yr(t,e){gi(8390656,8,t,e)}function qr(t,e){fu(2048,8,t,e)}function Vr(t,e){return fu(4,2,t,e)}function Lr(t,e){return fu(4,4,t,e)}function Gr(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Xr(t,e,l){l=l!=null?l.concat([t]):null,fu(4,4,Gr.bind(null,e,t),l)}function ef(){}function Qr(t,e){var l=Pt();e=e===void 0?null:e;var a=l.memoizedState;return e!==null&&Qc(e,a[1])?a[0]:(l.memoizedState=[t,e],t)}function Zr(t,e){var l=Pt();e=e===void 0?null:e;var a=l.memoizedState;if(e!==null&&Qc(e,a[1]))return a[0];if(a=t(),Ba){il(!0);try{t()}finally{il(!1)}}return l.memoizedState=[a,e],a}function lf(t,e,l){return l===void 0||(ta&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=Wo(),dt.lanes|=t,ca|=t,l)}function Kr(t,e,l,a){return Ne(l,e)?l:vn.current!==null?(t=lf(t,l,a),Ne(t,e)||(ne=!0),t):(ta&42)===0?(ne=!0,t.memoizedState=l):(t=Wo(),dt.lanes|=t,ca|=t,e)}function Jr(t,e,l,a,n){var u=F.p;F.p=u!==0&&8>u?u:8;var i=V.T,c={};V.T=c,uf(t,!1,e,l);try{var r=n(),b=V.S;if(b!==null&&b(c,r),r!==null&&typeof r=="object"&&typeof r.then=="function"){var x=Od(r,a);su(t,e,x,He(t))}else su(t,e,a,He(t))}catch(R){su(t,e,{then:function(){},status:"rejected",reason:R},He())}finally{F.p=u,V.T=i}}function Ud(){}function af(t,e,l,a){if(t.tag!==5)throw Error(f(476));var n=$r(t).queue;Jr(t,n,e,xt,l===null?Ud:function(){return Wr(t),l(a)})}function $r(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:xt,baseState:xt,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ol,lastRenderedState:xt},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ol,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Wr(t){var e=$r(t).next.queue;su(t,e,{},He())}function nf(){return ye(Ou)}function kr(){return Pt().memoizedState}function Fr(){return Pt().memoizedState}function jd(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=He();t=Il(l);var a=Pl(e,t,l);a!==null&&(Be(a,e,l),au(a,e,l)),e={cache:wc()},t.payload=e;return}e=e.return}}function wd(t,e,l){var a=He();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},pi(t)?Pr(e,l):(l=Tc(t,e,l,a),l!==null&&(Be(l,t,a),to(l,e,a)))}function Ir(t,e,l){var a=He();su(t,e,l,a)}function su(t,e,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(pi(t))Pr(e,n);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var i=e.lastRenderedState,c=u(i,l);if(n.hasEagerState=!0,n.eagerState=c,Ne(c,i))return ei(t,e,n,0),Bt===null&&ti(),!1}catch{}finally{}if(l=Tc(t,e,n,a),l!==null)return Be(l,t,a),to(l,e,a),!0}return!1}function uf(t,e,l,a){if(a={lane:2,revertLane:Yf(),action:a,hasEagerState:!1,eagerState:null,next:null},pi(t)){if(e)throw Error(f(479))}else e=Tc(t,l,a,2),e!==null&&Be(e,t,2)}function pi(t){var e=t.alternate;return t===dt||e!==null&&e===dt}function Pr(t,e){mn=hi=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function to(t,e,l){if((l&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,Ku(t,l)}}var Si={readContext:ye,use:vi,useCallback:$t,useContext:$t,useEffect:$t,useImperativeHandle:$t,useLayoutEffect:$t,useInsertionEffect:$t,useMemo:$t,useReducer:$t,useRef:$t,useState:$t,useDebugValue:$t,useDeferredValue:$t,useTransition:$t,useSyncExternalStore:$t,useId:$t,useHostTransitionStatus:$t,useFormState:$t,useActionState:$t,useOptimistic:$t,useMemoCache:$t,useCacheRefresh:$t},eo={readContext:ye,use:vi,useCallback:function(t,e){return Te().memoizedState=[t,e===void 0?null:e],t},useContext:ye,useEffect:Yr,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,gi(4194308,4,Gr.bind(null,e,t),l)},useLayoutEffect:function(t,e){return gi(4194308,4,t,e)},useInsertionEffect:function(t,e){gi(4,2,t,e)},useMemo:function(t,e){var l=Te();e=e===void 0?null:e;var a=t();if(Ba){il(!0);try{t()}finally{il(!1)}}return l.memoizedState=[a,e],a},useReducer:function(t,e,l){var a=Te();if(l!==void 0){var n=l(e);if(Ba){il(!0);try{l(e)}finally{il(!1)}}}else n=e;return a.memoizedState=a.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},a.queue=t,t=t.dispatch=wd.bind(null,dt,t),[a.memoizedState,t]},useRef:function(t){var e=Te();return t={current:t},e.memoizedState=t},useState:function(t){t=Pc(t);var e=t.queue,l=Ir.bind(null,dt,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:ef,useDeferredValue:function(t,e){var l=Te();return lf(l,t,e)},useTransition:function(){var t=Pc(!1);return t=Jr.bind(null,dt,t.queue,!0,!1),Te().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var a=dt,n=Te();if(Tt){if(l===void 0)throw Error(f(407));l=l()}else{if(l=e(),Bt===null)throw Error(f(349));(St&124)!==0||_r(a,e,l)}n.memoizedState=l;var u={value:l,getSnapshot:e};return n.queue=u,Yr(Tr.bind(null,a,u,t),[t]),a.flags|=2048,gn(9,yi(),Er.bind(null,a,u,l,e),null),l},useId:function(){var t=Te(),e=Bt.identifierPrefix;if(Tt){var l=Tl,a=El;l=(a&~(1<<32-Se(a)-1)).toString(32)+l,e="«"+e+"R"+l,l=di++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=Md++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:nf,useFormState:jr,useActionState:jr,useOptimistic:function(t){var e=Te();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=uf.bind(null,dt,!0,l),l.dispatch=e,[t,e]},useMemoCache:kc,useCacheRefresh:function(){return Te().memoizedState=jd.bind(null,dt)}},lo={readContext:ye,use:vi,useCallback:Qr,useContext:ye,useEffect:qr,useImperativeHandle:Xr,useInsertionEffect:Vr,useLayoutEffect:Lr,useMemo:Zr,useReducer:mi,useRef:Cr,useState:function(){return mi(Ol)},useDebugValue:ef,useDeferredValue:function(t,e){var l=Pt();return Kr(l,Nt.memoizedState,t,e)},useTransition:function(){var t=mi(Ol)[0],e=Pt().memoizedState;return[typeof t=="boolean"?t:cu(t),e]},useSyncExternalStore:zr,useId:kr,useHostTransitionStatus:nf,useFormState:wr,useActionState:wr,useOptimistic:function(t,e){var l=Pt();return Or(l,Nt,t,e)},useMemoCache:kc,useCacheRefresh:Fr},Hd={readContext:ye,use:vi,useCallback:Qr,useContext:ye,useEffect:qr,useImperativeHandle:Xr,useInsertionEffect:Vr,useLayoutEffect:Lr,useMemo:Zr,useReducer:Ic,useRef:Cr,useState:function(){return Ic(Ol)},useDebugValue:ef,useDeferredValue:function(t,e){var l=Pt();return Nt===null?lf(l,t,e):Kr(l,Nt.memoizedState,t,e)},useTransition:function(){var t=Ic(Ol)[0],e=Pt().memoizedState;return[typeof t=="boolean"?t:cu(t),e]},useSyncExternalStore:zr,useId:kr,useHostTransitionStatus:nf,useFormState:Br,useActionState:Br,useOptimistic:function(t,e){var l=Pt();return Nt!==null?Or(l,Nt,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:kc,useCacheRefresh:Fr},pn=null,ru=0;function bi(t){var e=ru;return ru+=1,pn===null&&(pn=[]),dr(pn,t,e)}function ou(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function zi(t,e){throw e.$$typeof===O?Error(f(525)):(t=Object.prototype.toString.call(e),Error(f(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function ao(t){var e=t._init;return e(t._payload)}function no(t){function e(p,m){if(t){var S=p.deletions;S===null?(p.deletions=[m],p.flags|=16):S.push(m)}}function l(p,m){if(!t)return null;for(;m!==null;)e(p,m),m=m.sibling;return null}function a(p){for(var m=new Map;p!==null;)p.key!==null?m.set(p.key,p):m.set(p.index,p),p=p.sibling;return m}function n(p,m){return p=_l(p,m),p.index=0,p.sibling=null,p}function u(p,m,S){return p.index=S,t?(S=p.alternate,S!==null?(S=S.index,S<m?(p.flags|=67108866,m):S):(p.flags|=67108866,m)):(p.flags|=1048576,m)}function i(p){return t&&p.alternate===null&&(p.flags|=67108866),p}function c(p,m,S,D){return m===null||m.tag!==6?(m=xc(S,p.mode,D),m.return=p,m):(m=n(m,S),m.return=p,m)}function r(p,m,S,D){var J=S.type;return J===C?x(p,m,S.props.children,D,S.key):m!==null&&(m.elementType===J||typeof J=="object"&&J!==null&&J.$$typeof===st&&ao(J)===m.type)?(m=n(m,S.props),ou(m,S),m.return=p,m):(m=ai(S.type,S.key,S.props,null,p.mode,D),ou(m,S),m.return=p,m)}function b(p,m,S,D){return m===null||m.tag!==4||m.stateNode.containerInfo!==S.containerInfo||m.stateNode.implementation!==S.implementation?(m=Oc(S,p.mode,D),m.return=p,m):(m=n(m,S.children||[]),m.return=p,m)}function x(p,m,S,D,J){return m===null||m.tag!==7?(m=Ma(S,p.mode,D,J),m.return=p,m):(m=n(m,S),m.return=p,m)}function R(p,m,S){if(typeof m=="string"&&m!==""||typeof m=="number"||typeof m=="bigint")return m=xc(""+m,p.mode,S),m.return=p,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case H:return S=ai(m.type,m.key,m.props,null,p.mode,S),ou(S,m),S.return=p,S;case B:return m=Oc(m,p.mode,S),m.return=p,m;case st:var D=m._init;return m=D(m._payload),R(p,m,S)}if(ft(m)||rt(m))return m=Ma(m,p.mode,S,null),m.return=p,m;if(typeof m.then=="function")return R(p,bi(m),S);if(m.$$typeof===k)return R(p,ci(p,m),S);zi(p,m)}return null}function E(p,m,S,D){var J=m!==null?m.key:null;if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return J!==null?null:c(p,m,""+S,D);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case H:return S.key===J?r(p,m,S,D):null;case B:return S.key===J?b(p,m,S,D):null;case st:return J=S._init,S=J(S._payload),E(p,m,S,D)}if(ft(S)||rt(S))return J!==null?null:x(p,m,S,D,null);if(typeof S.then=="function")return E(p,m,bi(S),D);if(S.$$typeof===k)return E(p,m,ci(p,S),D);zi(p,S)}return null}function T(p,m,S,D,J){if(typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint")return p=p.get(S)||null,c(m,p,""+D,J);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case H:return p=p.get(D.key===null?S:D.key)||null,r(m,p,D,J);case B:return p=p.get(D.key===null?S:D.key)||null,b(m,p,D,J);case st:var mt=D._init;return D=mt(D._payload),T(p,m,S,D,J)}if(ft(D)||rt(D))return p=p.get(S)||null,x(m,p,D,J,null);if(typeof D.then=="function")return T(p,m,S,bi(D),J);if(D.$$typeof===k)return T(p,m,S,ci(m,D),J);zi(m,D)}return null}function ct(p,m,S,D){for(var J=null,mt=null,I=m,nt=m=0,ie=null;I!==null&&nt<S.length;nt++){I.index>nt?(ie=I,I=null):ie=I.sibling;var Et=E(p,I,S[nt],D);if(Et===null){I===null&&(I=ie);break}t&&I&&Et.alternate===null&&e(p,I),m=u(Et,m,nt),mt===null?J=Et:mt.sibling=Et,mt=Et,I=ie}if(nt===S.length)return l(p,I),Tt&&Na(p,nt),J;if(I===null){for(;nt<S.length;nt++)I=R(p,S[nt],D),I!==null&&(m=u(I,m,nt),mt===null?J=I:mt.sibling=I,mt=I);return Tt&&Na(p,nt),J}for(I=a(I);nt<S.length;nt++)ie=T(I,p,nt,S[nt],D),ie!==null&&(t&&ie.alternate!==null&&I.delete(ie.key===null?nt:ie.key),m=u(ie,m,nt),mt===null?J=ie:mt.sibling=ie,mt=ie);return t&&I.forEach(function(ya){return e(p,ya)}),Tt&&Na(p,nt),J}function at(p,m,S,D){if(S==null)throw Error(f(151));for(var J=null,mt=null,I=m,nt=m=0,ie=null,Et=S.next();I!==null&&!Et.done;nt++,Et=S.next()){I.index>nt?(ie=I,I=null):ie=I.sibling;var ya=E(p,I,Et.value,D);if(ya===null){I===null&&(I=ie);break}t&&I&&ya.alternate===null&&e(p,I),m=u(ya,m,nt),mt===null?J=ya:mt.sibling=ya,mt=ya,I=ie}if(Et.done)return l(p,I),Tt&&Na(p,nt),J;if(I===null){for(;!Et.done;nt++,Et=S.next())Et=R(p,Et.value,D),Et!==null&&(m=u(Et,m,nt),mt===null?J=Et:mt.sibling=Et,mt=Et);return Tt&&Na(p,nt),J}for(I=a(I);!Et.done;nt++,Et=S.next())Et=T(I,p,nt,Et.value,D),Et!==null&&(t&&Et.alternate!==null&&I.delete(Et.key===null?nt:Et.key),m=u(Et,m,nt),mt===null?J=Et:mt.sibling=Et,mt=Et);return t&&I.forEach(function(B2){return e(p,B2)}),Tt&&Na(p,nt),J}function Ut(p,m,S,D){if(typeof S=="object"&&S!==null&&S.type===C&&S.key===null&&(S=S.props.children),typeof S=="object"&&S!==null){switch(S.$$typeof){case H:t:{for(var J=S.key;m!==null;){if(m.key===J){if(J=S.type,J===C){if(m.tag===7){l(p,m.sibling),D=n(m,S.props.children),D.return=p,p=D;break t}}else if(m.elementType===J||typeof J=="object"&&J!==null&&J.$$typeof===st&&ao(J)===m.type){l(p,m.sibling),D=n(m,S.props),ou(D,S),D.return=p,p=D;break t}l(p,m);break}else e(p,m);m=m.sibling}S.type===C?(D=Ma(S.props.children,p.mode,D,S.key),D.return=p,p=D):(D=ai(S.type,S.key,S.props,null,p.mode,D),ou(D,S),D.return=p,p=D)}return i(p);case B:t:{for(J=S.key;m!==null;){if(m.key===J)if(m.tag===4&&m.stateNode.containerInfo===S.containerInfo&&m.stateNode.implementation===S.implementation){l(p,m.sibling),D=n(m,S.children||[]),D.return=p,p=D;break t}else{l(p,m);break}else e(p,m);m=m.sibling}D=Oc(S,p.mode,D),D.return=p,p=D}return i(p);case st:return J=S._init,S=J(S._payload),Ut(p,m,S,D)}if(ft(S))return ct(p,m,S,D);if(rt(S)){if(J=rt(S),typeof J!="function")throw Error(f(150));return S=J.call(S),at(p,m,S,D)}if(typeof S.then=="function")return Ut(p,m,bi(S),D);if(S.$$typeof===k)return Ut(p,m,ci(p,S),D);zi(p,S)}return typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint"?(S=""+S,m!==null&&m.tag===6?(l(p,m.sibling),D=n(m,S),D.return=p,p=D):(l(p,m),D=xc(S,p.mode,D),D.return=p,p=D),i(p)):l(p,m)}return function(p,m,S,D){try{ru=0;var J=Ut(p,m,S,D);return pn=null,J}catch(I){if(I===eu||I===si)throw I;var mt=Re(29,I,null,p.mode);return mt.lanes=D,mt.return=p,mt}finally{}}}var Sn=no(!0),uo=no(!1),$e=N(null),dl=null;function ea(t){var e=t.alternate;L(ee,ee.current&1),L($e,t),dl===null&&(e===null||vn.current!==null||e.memoizedState!==null)&&(dl=t)}function io(t){if(t.tag===22){if(L(ee,ee.current),L($e,t),dl===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(dl=t)}}else la()}function la(){L(ee,ee.current),L($e,$e.current)}function Ml(t){Q($e),dl===t&&(dl=null),Q(ee)}var ee=N(0);function _i(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||kf(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function cf(t,e,l,a){e=t.memoizedState,l=l(a,e),l=l==null?e:z({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var ff={enqueueSetState:function(t,e,l){t=t._reactInternals;var a=He(),n=Il(a);n.payload=e,l!=null&&(n.callback=l),e=Pl(t,n,a),e!==null&&(Be(e,t,a),au(e,t,a))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var a=He(),n=Il(a);n.tag=1,n.payload=e,l!=null&&(n.callback=l),e=Pl(t,n,a),e!==null&&(Be(e,t,a),au(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=He(),a=Il(l);a.tag=2,e!=null&&(a.callback=e),e=Pl(t,a,l),e!==null&&(Be(e,t,l),au(e,t,l))}};function co(t,e,l,a,n,u,i){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,u,i):e.prototype&&e.prototype.isPureReactComponent?!Jn(l,a)||!Jn(n,u):!0}function fo(t,e,l,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,a),e.state!==t&&ff.enqueueReplaceState(e,e.state,null)}function Ca(t,e){var l=e;if("ref"in e){l={};for(var a in e)a!=="ref"&&(l[a]=e[a])}if(t=t.defaultProps){l===e&&(l=z({},l));for(var n in t)l[n]===void 0&&(l[n]=t[n])}return l}var Ei=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function so(t){Ei(t)}function ro(t){console.error(t)}function oo(t){Ei(t)}function Ti(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function ho(t,e,l){try{var a=t.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function sf(t,e,l){return l=Il(l),l.tag=3,l.payload={element:null},l.callback=function(){Ti(t,e)},l}function vo(t){return t=Il(t),t.tag=3,t}function mo(t,e,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var u=a.value;t.payload=function(){return n(u)},t.callback=function(){ho(e,l,a)}}var i=l.stateNode;i!==null&&typeof i.componentDidCatch=="function"&&(t.callback=function(){ho(e,l,a),typeof n!="function"&&(fa===null?fa=new Set([this]):fa.add(this));var c=a.stack;this.componentDidCatch(a.value,{componentStack:c!==null?c:""})})}function Bd(t,e,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=l.alternate,e!==null&&In(e,l,n,!0),l=$e.current,l!==null){switch(l.tag){case 13:return dl===null?jf():l.alternate===null&&Jt===0&&(Jt=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===Cc?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([a]):e.add(a),Hf(t,a,n)),!1;case 22:return l.flags|=65536,a===Cc?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([a]):l.add(a)),Hf(t,a,n)),!1}throw Error(f(435,l.tag))}return Hf(t,a,n),jf(),!1}if(Tt)return e=$e.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,a!==Nc&&(t=Error(f(422),{cause:a}),Fn(Qe(t,l)))):(a!==Nc&&(e=Error(f(423),{cause:a}),Fn(Qe(e,l))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,a=Qe(a,l),n=sf(t.stateNode,a,n),Vc(t,n),Jt!==4&&(Jt=2)),!1;var u=Error(f(520),{cause:a});if(u=Qe(u,l),pu===null?pu=[u]:pu.push(u),Jt!==4&&(Jt=2),e===null)return!0;a=Qe(a,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=n&-n,l.lanes|=t,t=sf(l.stateNode,a,t),Vc(l,t),!1;case 1:if(e=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(fa===null||!fa.has(u))))return l.flags|=65536,n&=-n,l.lanes|=n,n=vo(n),mo(n,t,l,a),Vc(l,n),!1}l=l.return}while(l!==null);return!1}var yo=Error(f(461)),ne=!1;function fe(t,e,l,a){e.child=t===null?uo(e,null,l,a):Sn(e,t.child,l,a)}function go(t,e,l,a,n){l=l.render;var u=e.ref;if("ref"in a){var i={};for(var c in a)c!=="ref"&&(i[c]=a[c])}else i=a;return wa(e),a=Zc(t,e,l,i,u,n),c=Kc(),t!==null&&!ne?(Jc(t,e,n),Dl(t,e,n)):(Tt&&c&&Mc(e),e.flags|=1,fe(t,e,a,n),e.child)}function po(t,e,l,a,n){if(t===null){var u=l.type;return typeof u=="function"&&!Ac(u)&&u.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=u,So(t,e,u,a,n)):(t=ai(l.type,null,a,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!gf(t,n)){var i=u.memoizedProps;if(l=l.compare,l=l!==null?l:Jn,l(i,a)&&t.ref===e.ref)return Dl(t,e,n)}return e.flags|=1,t=_l(u,a),t.ref=e.ref,t.return=e,e.child=t}function So(t,e,l,a,n){if(t!==null){var u=t.memoizedProps;if(Jn(u,a)&&t.ref===e.ref)if(ne=!1,e.pendingProps=a=u,gf(t,n))(t.flags&131072)!==0&&(ne=!0);else return e.lanes=t.lanes,Dl(t,e,n)}return rf(t,e,l,a,n)}function bo(t,e,l){var a=e.pendingProps,n=a.children,u=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=u!==null?u.baseLanes|l:l,t!==null){for(n=e.child=t.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;e.childLanes=u&~a}else e.childLanes=0,e.child=null;return zo(t,e,a,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&fi(e,u!==null?u.cachePool:null),u!==null?pr(e,u):Gc(),io(e);else return e.lanes=e.childLanes=536870912,zo(t,e,u!==null?u.baseLanes|l:l,l)}else u!==null?(fi(e,u.cachePool),pr(e,u),la(),e.memoizedState=null):(t!==null&&fi(e,null),Gc(),la());return fe(t,e,n,l),e.child}function zo(t,e,l,a){var n=Bc();return n=n===null?null:{parent:te._currentValue,pool:n},e.memoizedState={baseLanes:l,cachePool:n},t!==null&&fi(e,null),Gc(),io(e),t!==null&&In(t,e,a,!0),null}function Ai(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(f(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function rf(t,e,l,a,n){return wa(e),l=Zc(t,e,l,a,void 0,n),a=Kc(),t!==null&&!ne?(Jc(t,e,n),Dl(t,e,n)):(Tt&&a&&Mc(e),e.flags|=1,fe(t,e,l,n),e.child)}function _o(t,e,l,a,n,u){return wa(e),e.updateQueue=null,l=br(e,a,l,n),Sr(t),a=Kc(),t!==null&&!ne?(Jc(t,e,u),Dl(t,e,u)):(Tt&&a&&Mc(e),e.flags|=1,fe(t,e,l,u),e.child)}function Eo(t,e,l,a,n){if(wa(e),e.stateNode===null){var u=sn,i=l.contextType;typeof i=="object"&&i!==null&&(u=ye(i)),u=new l(a,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=ff,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=a,u.state=e.memoizedState,u.refs={},Yc(e),i=l.contextType,u.context=typeof i=="object"&&i!==null?ye(i):sn,u.state=e.memoizedState,i=l.getDerivedStateFromProps,typeof i=="function"&&(cf(e,l,i,a),u.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(i=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),i!==u.state&&ff.enqueueReplaceState(u,u.state,null),uu(e,a,u,n),nu(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){u=e.stateNode;var c=e.memoizedProps,r=Ca(l,c);u.props=r;var b=u.context,x=l.contextType;i=sn,typeof x=="object"&&x!==null&&(i=ye(x));var R=l.getDerivedStateFromProps;x=typeof R=="function"||typeof u.getSnapshotBeforeUpdate=="function",c=e.pendingProps!==c,x||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(c||b!==i)&&fo(e,u,a,i),Fl=!1;var E=e.memoizedState;u.state=E,uu(e,a,u,n),nu(),b=e.memoizedState,c||E!==b||Fl?(typeof R=="function"&&(cf(e,l,R,a),b=e.memoizedState),(r=Fl||co(e,l,r,a,E,b,i))?(x||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=b),u.props=a,u.state=b,u.context=i,a=r):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{u=e.stateNode,qc(t,e),i=e.memoizedProps,x=Ca(l,i),u.props=x,R=e.pendingProps,E=u.context,b=l.contextType,r=sn,typeof b=="object"&&b!==null&&(r=ye(b)),c=l.getDerivedStateFromProps,(b=typeof c=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(i!==R||E!==r)&&fo(e,u,a,r),Fl=!1,E=e.memoizedState,u.state=E,uu(e,a,u,n),nu();var T=e.memoizedState;i!==R||E!==T||Fl||t!==null&&t.dependencies!==null&&ii(t.dependencies)?(typeof c=="function"&&(cf(e,l,c,a),T=e.memoizedState),(x=Fl||co(e,l,x,a,E,T,r)||t!==null&&t.dependencies!==null&&ii(t.dependencies))?(b||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,T,r),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,T,r)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||i===t.memoizedProps&&E===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===t.memoizedProps&&E===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=T),u.props=a,u.state=T,u.context=r,a=x):(typeof u.componentDidUpdate!="function"||i===t.memoizedProps&&E===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===t.memoizedProps&&E===t.memoizedState||(e.flags|=1024),a=!1)}return u=a,Ai(t,e),a=(e.flags&128)!==0,u||a?(u=e.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&a?(e.child=Sn(e,t.child,null,n),e.child=Sn(e,null,l,n)):fe(t,e,l,n),e.memoizedState=u.state,t=e.child):t=Dl(t,e,n),t}function To(t,e,l,a){return kn(),e.flags|=256,fe(t,e,l,a),e.child}var of={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function hf(t){return{baseLanes:t,cachePool:rr()}}function df(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=We),t}function Ao(t,e,l){var a=e.pendingProps,n=!1,u=(e.flags&128)!==0,i;if((i=u)||(i=t!==null&&t.memoizedState===null?!1:(ee.current&2)!==0),i&&(n=!0,e.flags&=-129),i=(e.flags&32)!==0,e.flags&=-33,t===null){if(Tt){if(n?ea(e):la(),Tt){var c=Kt,r;if(r=c){t:{for(r=c,c=hl;r.nodeType!==8;){if(!c){c=null;break t}if(r=nl(r.nextSibling),r===null){c=null;break t}}c=r}c!==null?(e.memoizedState={dehydrated:c,treeContext:Da!==null?{id:El,overflow:Tl}:null,retryLane:536870912,hydrationErrors:null},r=Re(18,null,null,0),r.stateNode=c,r.return=e,e.child=r,ze=e,Kt=null,r=!0):r=!1}r||Ua(e)}if(c=e.memoizedState,c!==null&&(c=c.dehydrated,c!==null))return kf(c)?e.lanes=32:e.lanes=536870912,null;Ml(e)}return c=a.children,a=a.fallback,n?(la(),n=e.mode,c=xi({mode:"hidden",children:c},n),a=Ma(a,n,l,null),c.return=e,a.return=e,c.sibling=a,e.child=c,n=e.child,n.memoizedState=hf(l),n.childLanes=df(t,i,l),e.memoizedState=of,a):(ea(e),vf(e,c))}if(r=t.memoizedState,r!==null&&(c=r.dehydrated,c!==null)){if(u)e.flags&256?(ea(e),e.flags&=-257,e=mf(t,e,l)):e.memoizedState!==null?(la(),e.child=t.child,e.flags|=128,e=null):(la(),n=a.fallback,c=e.mode,a=xi({mode:"visible",children:a.children},c),n=Ma(n,c,l,null),n.flags|=2,a.return=e,n.return=e,a.sibling=n,e.child=a,Sn(e,t.child,null,l),a=e.child,a.memoizedState=hf(l),a.childLanes=df(t,i,l),e.memoizedState=of,e=n);else if(ea(e),kf(c)){if(i=c.nextSibling&&c.nextSibling.dataset,i)var b=i.dgst;i=b,a=Error(f(419)),a.stack="",a.digest=i,Fn({value:a,source:null,stack:null}),e=mf(t,e,l)}else if(ne||In(t,e,l,!1),i=(l&t.childLanes)!==0,ne||i){if(i=Bt,i!==null&&(a=l&-l,a=(a&42)!==0?1:Ka(a),a=(a&(i.suspendedLanes|l))!==0?0:a,a!==0&&a!==r.retryLane))throw r.retryLane=a,fn(t,a),Be(i,t,a),yo;c.data==="$?"||jf(),e=mf(t,e,l)}else c.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=r.treeContext,Kt=nl(c.nextSibling),ze=e,Tt=!0,Ra=null,hl=!1,t!==null&&(Ke[Je++]=El,Ke[Je++]=Tl,Ke[Je++]=Da,El=t.id,Tl=t.overflow,Da=e),e=vf(e,a.children),e.flags|=4096);return e}return n?(la(),n=a.fallback,c=e.mode,r=t.child,b=r.sibling,a=_l(r,{mode:"hidden",children:a.children}),a.subtreeFlags=r.subtreeFlags&65011712,b!==null?n=_l(b,n):(n=Ma(n,c,l,null),n.flags|=2),n.return=e,a.return=e,a.sibling=n,e.child=a,a=n,n=e.child,c=t.child.memoizedState,c===null?c=hf(l):(r=c.cachePool,r!==null?(b=te._currentValue,r=r.parent!==b?{parent:b,pool:b}:r):r=rr(),c={baseLanes:c.baseLanes|l,cachePool:r}),n.memoizedState=c,n.childLanes=df(t,i,l),e.memoizedState=of,a):(ea(e),l=t.child,t=l.sibling,l=_l(l,{mode:"visible",children:a.children}),l.return=e,l.sibling=null,t!==null&&(i=e.deletions,i===null?(e.deletions=[t],e.flags|=16):i.push(t)),e.child=l,e.memoizedState=null,l)}function vf(t,e){return e=xi({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function xi(t,e){return t=Re(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function mf(t,e,l){return Sn(e,t.child,null,l),t=vf(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function xo(t,e,l){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),Uc(t.return,e,l)}function yf(t,e,l,a,n){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=l,u.tailMode=n)}function Oo(t,e,l){var a=e.pendingProps,n=a.revealOrder,u=a.tail;if(fe(t,e,a.children,l),a=ee.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&xo(t,l,e);else if(t.tag===19)xo(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(L(ee,a),n){case"forwards":for(l=e.child,n=null;l!==null;)t=l.alternate,t!==null&&_i(t)===null&&(n=l),l=l.sibling;l=n,l===null?(n=e.child,e.child=null):(n=l.sibling,l.sibling=null),yf(e,!1,n,l,u);break;case"backwards":for(l=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&_i(t)===null){e.child=n;break}t=n.sibling,n.sibling=l,l=n,n=t}yf(e,!0,l,null,u);break;case"together":yf(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Dl(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),ca|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(In(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(f(153));if(e.child!==null){for(t=e.child,l=_l(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=_l(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function gf(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&ii(t)))}function Cd(t,e,l){switch(e.tag){case 3:Z(e,e.stateNode.containerInfo),kl(e,te,t.memoizedState.cache),kn();break;case 27:case 5:jt(e);break;case 4:Z(e,e.stateNode.containerInfo);break;case 10:kl(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(ea(e),e.flags|=128,null):(l&e.child.childLanes)!==0?Ao(t,e,l):(ea(e),t=Dl(t,e,l),t!==null?t.sibling:null);ea(e);break;case 19:var n=(t.flags&128)!==0;if(a=(l&e.childLanes)!==0,a||(In(t,e,l,!1),a=(l&e.childLanes)!==0),n){if(a)return Oo(t,e,l);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),L(ee,ee.current),a)break;return null;case 22:case 23:return e.lanes=0,bo(t,e,l);case 24:kl(e,te,t.memoizedState.cache)}return Dl(t,e,l)}function Mo(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)ne=!0;else{if(!gf(t,l)&&(e.flags&128)===0)return ne=!1,Cd(t,e,l);ne=(t.flags&131072)!==0}else ne=!1,Tt&&(e.flags&1048576)!==0&&ar(e,ui,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,n=a._init;if(a=n(a._payload),e.type=a,typeof a=="function")Ac(a)?(t=Ca(a,t),e.tag=1,e=Eo(null,e,a,t,l)):(e.tag=0,e=rf(null,e,a,t,l));else{if(a!=null){if(n=a.$$typeof,n===ut){e.tag=11,e=go(null,e,a,t,l);break t}else if(n===w){e.tag=14,e=po(null,e,a,t,l);break t}}throw e=At(a)||a,Error(f(306,e,""))}}return e;case 0:return rf(t,e,e.type,e.pendingProps,l);case 1:return a=e.type,n=Ca(a,e.pendingProps),Eo(t,e,a,n,l);case 3:t:{if(Z(e,e.stateNode.containerInfo),t===null)throw Error(f(387));a=e.pendingProps;var u=e.memoizedState;n=u.element,qc(t,e),uu(e,a,null,l);var i=e.memoizedState;if(a=i.cache,kl(e,te,a),a!==u.cache&&jc(e,[te],l,!0),nu(),a=i.element,u.isDehydrated)if(u={element:a,isDehydrated:!1,cache:i.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=To(t,e,a,l);break t}else if(a!==n){n=Qe(Error(f(424)),e),Fn(n),e=To(t,e,a,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Kt=nl(t.firstChild),ze=e,Tt=!0,Ra=null,hl=!0,l=uo(e,null,a,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(kn(),a===n){e=Dl(t,e,l);break t}fe(t,e,a,l)}e=e.child}return e;case 26:return Ai(t,e),t===null?(l=U0(e.type,null,e.pendingProps,null))?e.memoizedState=l:Tt||(l=e.type,t=e.pendingProps,a=Vi(lt.current).createElement(l),a[Yt]=e,a[he]=t,re(a,l,t),v(a),e.stateNode=a):e.memoizedState=U0(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return jt(e),t===null&&Tt&&(a=e.stateNode=D0(e.type,e.pendingProps,lt.current),ze=e,hl=!0,n=Kt,oa(e.type)?(Ff=n,Kt=nl(a.firstChild)):Kt=n),fe(t,e,e.pendingProps.children,l),Ai(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&Tt&&((n=a=Kt)&&(a=o2(a,e.type,e.pendingProps,hl),a!==null?(e.stateNode=a,ze=e,Kt=nl(a.firstChild),hl=!1,n=!0):n=!1),n||Ua(e)),jt(e),n=e.type,u=e.pendingProps,i=t!==null?t.memoizedProps:null,a=u.children,Jf(n,u)?a=null:i!==null&&Jf(n,i)&&(e.flags|=32),e.memoizedState!==null&&(n=Zc(t,e,Dd,null,null,l),Ou._currentValue=n),Ai(t,e),fe(t,e,a,l),e.child;case 6:return t===null&&Tt&&((t=l=Kt)&&(l=h2(l,e.pendingProps,hl),l!==null?(e.stateNode=l,ze=e,Kt=null,t=!0):t=!1),t||Ua(e)),null;case 13:return Ao(t,e,l);case 4:return Z(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=Sn(e,null,a,l):fe(t,e,a,l),e.child;case 11:return go(t,e,e.type,e.pendingProps,l);case 7:return fe(t,e,e.pendingProps,l),e.child;case 8:return fe(t,e,e.pendingProps.children,l),e.child;case 12:return fe(t,e,e.pendingProps.children,l),e.child;case 10:return a=e.pendingProps,kl(e,e.type,a.value),fe(t,e,a.children,l),e.child;case 9:return n=e.type._context,a=e.pendingProps.children,wa(e),n=ye(n),a=a(n),e.flags|=1,fe(t,e,a,l),e.child;case 14:return po(t,e,e.type,e.pendingProps,l);case 15:return So(t,e,e.type,e.pendingProps,l);case 19:return Oo(t,e,l);case 31:return a=e.pendingProps,l=e.mode,a={mode:a.mode,children:a.children},t===null?(l=xi(a,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=_l(t.child,a),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return bo(t,e,l);case 24:return wa(e),a=ye(te),t===null?(n=Bc(),n===null&&(n=Bt,u=wc(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=l),n=u),e.memoizedState={parent:a,cache:n},Yc(e),kl(e,te,n)):((t.lanes&l)!==0&&(qc(t,e),uu(e,null,null,l),nu()),n=t.memoizedState,u=e.memoizedState,n.parent!==a?(n={parent:a,cache:a},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),kl(e,te,a)):(a=u.cache,kl(e,te,a),a!==n.cache&&jc(e,[te],l,!0))),fe(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(f(156,e.tag))}function Nl(t){t.flags|=4}function Do(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!C0(e)){if(e=$e.current,e!==null&&((St&4194048)===St?dl!==null:(St&62914560)!==St&&(St&536870912)===0||e!==dl))throw lu=Cc,or;t.flags|=8192}}function Oi(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Sa():536870912,t.lanes|=e,En|=e)}function hu(t,e){if(!Tt)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function Zt(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,a=0;if(e)for(var n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=a,t.childLanes=l,e}function Yd(t,e,l){var a=e.pendingProps;switch(Dc(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Zt(e),null;case 1:return Zt(e),null;case 3:return l=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),xl(te),it(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(Wn(e)?Nl(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,ir())),Zt(e),null;case 26:return l=e.memoizedState,t===null?(Nl(e),l!==null?(Zt(e),Do(e,l)):(Zt(e),e.flags&=-16777217)):l?l!==t.memoizedState?(Nl(e),Zt(e),Do(e,l)):(Zt(e),e.flags&=-16777217):(t.memoizedProps!==a&&Nl(e),Zt(e),e.flags&=-16777217),null;case 27:Ht(e),l=lt.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&Nl(e);else{if(!a){if(e.stateNode===null)throw Error(f(166));return Zt(e),null}t=et.current,Wn(e)?nr(e):(t=D0(n,a,l),e.stateNode=t,Nl(e))}return Zt(e),null;case 5:if(Ht(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&Nl(e);else{if(!a){if(e.stateNode===null)throw Error(f(166));return Zt(e),null}if(t=et.current,Wn(e))nr(e);else{switch(n=Vi(lt.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}t[Yt]=e,t[he]=a;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(re(t,l,a),l){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Nl(e)}}return Zt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&Nl(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(f(166));if(t=lt.current,Wn(e)){if(t=e.stateNode,l=e.memoizedProps,a=null,n=ze,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}t[Yt]=e,t=!!(t.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||_0(t.nodeValue,l)),t||Ua(e)}else t=Vi(t).createTextNode(a),t[Yt]=e,e.stateNode=t}return Zt(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=Wn(e),a!==null&&a.dehydrated!==null){if(t===null){if(!n)throw Error(f(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(f(317));n[Yt]=e}else kn(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Zt(e),n=!1}else n=ir(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?(Ml(e),e):(Ml(e),null)}if(Ml(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=a!==null,t=t!==null&&t.memoizedState!==null,l){a=e.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==n&&(a.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),Oi(e,e.updateQueue),Zt(e),null;case 4:return it(),t===null&&Gf(e.stateNode.containerInfo),Zt(e),null;case 10:return xl(e.type),Zt(e),null;case 19:if(Q(ee),n=e.memoizedState,n===null)return Zt(e),null;if(a=(e.flags&128)!==0,u=n.rendering,u===null)if(a)hu(n,!1);else{if(Jt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=_i(t),u!==null){for(e.flags|=128,hu(n,!1),t=u.updateQueue,e.updateQueue=t,Oi(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)lr(l,t),l=l.sibling;return L(ee,ee.current&1|2),e.child}t=t.sibling}n.tail!==null&&le()>Ni&&(e.flags|=128,a=!0,hu(n,!1),e.lanes=4194304)}else{if(!a)if(t=_i(u),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,Oi(e,t),hu(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!Tt)return Zt(e),null}else 2*le()-n.renderingStartTime>Ni&&l!==536870912&&(e.flags|=128,a=!0,hu(n,!1),e.lanes=4194304);n.isBackwards?(u.sibling=e.child,e.child=u):(t=n.last,t!==null?t.sibling=u:e.child=u,n.last=u)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=le(),e.sibling=null,t=ee.current,L(ee,a?t&1|2:t&1),e):(Zt(e),null);case 22:case 23:return Ml(e),Xc(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(l&536870912)!==0&&(e.flags&128)===0&&(Zt(e),e.subtreeFlags&6&&(e.flags|=8192)):Zt(e),l=e.updateQueue,l!==null&&Oi(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==l&&(e.flags|=2048),t!==null&&Q(Ha),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),xl(te),Zt(e),null;case 25:return null;case 30:return null}throw Error(f(156,e.tag))}function qd(t,e){switch(Dc(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return xl(te),it(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Ht(e),null;case 13:if(Ml(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(f(340));kn()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Q(ee),null;case 4:return it(),null;case 10:return xl(e.type),null;case 22:case 23:return Ml(e),Xc(),t!==null&&Q(Ha),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return xl(te),null;case 25:return null;default:return null}}function No(t,e){switch(Dc(e),e.tag){case 3:xl(te),it();break;case 26:case 27:case 5:Ht(e);break;case 4:it();break;case 13:Ml(e);break;case 19:Q(ee);break;case 10:xl(e.type);break;case 22:case 23:Ml(e),Xc(),t!==null&&Q(Ha);break;case 24:xl(te)}}function du(t,e){try{var l=e.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&t)===t){a=void 0;var u=l.create,i=l.inst;a=u(),i.destroy=a}l=l.next}while(l!==n)}}catch(c){wt(e,e.return,c)}}function aa(t,e,l){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&t)===t){var i=a.inst,c=i.destroy;if(c!==void 0){i.destroy=void 0,n=e;var r=l,b=c;try{b()}catch(x){wt(n,r,x)}}}a=a.next}while(a!==u)}}catch(x){wt(e,e.return,x)}}function Ro(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{gr(e,l)}catch(a){wt(t,t.return,a)}}}function Uo(t,e,l){l.props=Ca(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(a){wt(t,e,a)}}function vu(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof l=="function"?t.refCleanup=l(a):l.current=a}}catch(n){wt(t,e,n)}}function vl(t,e){var l=t.ref,a=t.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){wt(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){wt(t,e,n)}else l.current=null}function jo(t){var e=t.type,l=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break t;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){wt(t,t.return,n)}}function pf(t,e,l){try{var a=t.stateNode;i2(a,t.type,l,e),a[he]=e}catch(n){wt(t,t.return,n)}}function wo(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&oa(t.type)||t.tag===4}function Sf(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||wo(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&oa(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function bf(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=qi));else if(a!==4&&(a===27&&oa(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(bf(t,e,l),t=t.sibling;t!==null;)bf(t,e,l),t=t.sibling}function Mi(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(a!==4&&(a===27&&oa(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(Mi(t,e,l),t=t.sibling;t!==null;)Mi(t,e,l),t=t.sibling}function Ho(t){var e=t.stateNode,l=t.memoizedProps;try{for(var a=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);re(e,a,l),e[Yt]=t,e[he]=l}catch(u){wt(t,t.return,u)}}var Rl=!1,Wt=!1,zf=!1,Bo=typeof WeakSet=="function"?WeakSet:Set,ue=null;function Vd(t,e){if(t=t.containerInfo,Zf=Ki,t=Ks(t),pc(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break t}var i=0,c=-1,r=-1,b=0,x=0,R=t,E=null;e:for(;;){for(var T;R!==l||n!==0&&R.nodeType!==3||(c=i+n),R!==u||a!==0&&R.nodeType!==3||(r=i+a),R.nodeType===3&&(i+=R.nodeValue.length),(T=R.firstChild)!==null;)E=R,R=T;for(;;){if(R===t)break e;if(E===l&&++b===n&&(c=i),E===u&&++x===a&&(r=i),(T=R.nextSibling)!==null)break;R=E,E=R.parentNode}R=T}l=c===-1||r===-1?null:{start:c,end:r}}else l=null}l=l||{start:0,end:0}}else l=null;for(Kf={focusedElem:t,selectionRange:l},Ki=!1,ue=e;ue!==null;)if(e=ue,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,ue=t;else for(;ue!==null;){switch(e=ue,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,l=e,n=u.memoizedProps,u=u.memoizedState,a=l.stateNode;try{var ct=Ca(l.type,n,l.elementType===l.type);t=a.getSnapshotBeforeUpdate(ct,u),a.__reactInternalSnapshotBeforeUpdate=t}catch(at){wt(l,l.return,at)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)Wf(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":Wf(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(f(163))}if(t=e.sibling,t!==null){t.return=e.return,ue=t;break}ue=e.return}}function Co(t,e,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:na(t,l),a&4&&du(5,l);break;case 1:if(na(t,l),a&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(i){wt(l,l.return,i)}else{var n=Ca(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(i){wt(l,l.return,i)}}a&64&&Ro(l),a&512&&vu(l,l.return);break;case 3:if(na(t,l),a&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{gr(t,e)}catch(i){wt(l,l.return,i)}}break;case 27:e===null&&a&4&&Ho(l);case 26:case 5:na(t,l),e===null&&a&4&&jo(l),a&512&&vu(l,l.return);break;case 12:na(t,l);break;case 13:na(t,l),a&4&&Vo(t,l),a&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=Wd.bind(null,l),d2(t,l))));break;case 22:if(a=l.memoizedState!==null||Rl,!a){e=e!==null&&e.memoizedState!==null||Wt,n=Rl;var u=Wt;Rl=a,(Wt=e)&&!u?ua(t,l,(l.subtreeFlags&8772)!==0):na(t,l),Rl=n,Wt=u}break;case 30:break;default:na(t,l)}}function Yo(t){var e=t.alternate;e!==null&&(t.alternate=null,Yo(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Fa(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Xt=null,Ae=!1;function Ul(t,e,l){for(l=l.child;l!==null;)qo(t,e,l),l=l.sibling}function qo(t,e,l){if(pe&&typeof pe.onCommitFiberUnmount=="function")try{pe.onCommitFiberUnmount(tl,l)}catch{}switch(l.tag){case 26:Wt||vl(l,e),Ul(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Wt||vl(l,e);var a=Xt,n=Ae;oa(l.type)&&(Xt=l.stateNode,Ae=!1),Ul(t,e,l),Eu(l.stateNode),Xt=a,Ae=n;break;case 5:Wt||vl(l,e);case 6:if(a=Xt,n=Ae,Xt=null,Ul(t,e,l),Xt=a,Ae=n,Xt!==null)if(Ae)try{(Xt.nodeType===9?Xt.body:Xt.nodeName==="HTML"?Xt.ownerDocument.body:Xt).removeChild(l.stateNode)}catch(u){wt(l,e,u)}else try{Xt.removeChild(l.stateNode)}catch(u){wt(l,e,u)}break;case 18:Xt!==null&&(Ae?(t=Xt,O0(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),Ru(t)):O0(Xt,l.stateNode));break;case 4:a=Xt,n=Ae,Xt=l.stateNode.containerInfo,Ae=!0,Ul(t,e,l),Xt=a,Ae=n;break;case 0:case 11:case 14:case 15:Wt||aa(2,l,e),Wt||aa(4,l,e),Ul(t,e,l);break;case 1:Wt||(vl(l,e),a=l.stateNode,typeof a.componentWillUnmount=="function"&&Uo(l,e,a)),Ul(t,e,l);break;case 21:Ul(t,e,l);break;case 22:Wt=(a=Wt)||l.memoizedState!==null,Ul(t,e,l),Wt=a;break;default:Ul(t,e,l)}}function Vo(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Ru(t)}catch(l){wt(e,e.return,l)}}function Ld(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Bo),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Bo),e;default:throw Error(f(435,t.tag))}}function _f(t,e){var l=Ld(t);e.forEach(function(a){var n=kd.bind(null,t,a);l.has(a)||(l.add(a),a.then(n,n))})}function Ue(t,e){var l=e.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],u=t,i=e,c=i;t:for(;c!==null;){switch(c.tag){case 27:if(oa(c.type)){Xt=c.stateNode,Ae=!1;break t}break;case 5:Xt=c.stateNode,Ae=!1;break t;case 3:case 4:Xt=c.stateNode.containerInfo,Ae=!0;break t}c=c.return}if(Xt===null)throw Error(f(160));qo(u,i,n),Xt=null,Ae=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Lo(e,t),e=e.sibling}var al=null;function Lo(t,e){var l=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Ue(e,t),je(t),a&4&&(aa(3,t,t.return),du(3,t),aa(5,t,t.return));break;case 1:Ue(e,t),je(t),a&512&&(Wt||l===null||vl(l,l.return)),a&64&&Rl&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=al;if(Ue(e,t),je(t),a&512&&(Wt||l===null||vl(l,l.return)),a&4){var u=l!==null?l.memoizedState:null;if(a=t.memoizedState,l===null)if(a===null)if(t.stateNode===null){t:{a=t.type,l=t.memoizedProps,n=n.ownerDocument||n;e:switch(a){case"title":u=n.getElementsByTagName("title")[0],(!u||u[Ll]||u[Yt]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(a),n.head.insertBefore(u,n.querySelector("head > title"))),re(u,a,l),u[Yt]=t,v(u),a=u;break t;case"link":var i=H0("link","href",n).get(a+(l.href||""));if(i){for(var c=0;c<i.length;c++)if(u=i[c],u.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){i.splice(c,1);break e}}u=n.createElement(a),re(u,a,l),n.head.appendChild(u);break;case"meta":if(i=H0("meta","content",n).get(a+(l.content||""))){for(c=0;c<i.length;c++)if(u=i[c],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){i.splice(c,1);break e}}u=n.createElement(a),re(u,a,l),n.head.appendChild(u);break;default:throw Error(f(468,a))}u[Yt]=t,v(u),a=u}t.stateNode=a}else B0(n,t.type,t.stateNode);else t.stateNode=w0(n,a,t.memoizedProps);else u!==a?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,a===null?B0(n,t.type,t.stateNode):w0(n,a,t.memoizedProps)):a===null&&t.stateNode!==null&&pf(t,t.memoizedProps,l.memoizedProps)}break;case 27:Ue(e,t),je(t),a&512&&(Wt||l===null||vl(l,l.return)),l!==null&&a&4&&pf(t,t.memoizedProps,l.memoizedProps);break;case 5:if(Ue(e,t),je(t),a&512&&(Wt||l===null||vl(l,l.return)),t.flags&32){n=t.stateNode;try{el(n,"")}catch(T){wt(t,t.return,T)}}a&4&&t.stateNode!=null&&(n=t.memoizedProps,pf(t,n,l!==null?l.memoizedProps:n)),a&1024&&(zf=!0);break;case 6:if(Ue(e,t),je(t),a&4){if(t.stateNode===null)throw Error(f(162));a=t.memoizedProps,l=t.stateNode;try{l.nodeValue=a}catch(T){wt(t,t.return,T)}}break;case 3:if(Xi=null,n=al,al=Li(e.containerInfo),Ue(e,t),al=n,je(t),a&4&&l!==null&&l.memoizedState.isDehydrated)try{Ru(e.containerInfo)}catch(T){wt(t,t.return,T)}zf&&(zf=!1,Go(t));break;case 4:a=al,al=Li(t.stateNode.containerInfo),Ue(e,t),je(t),al=a;break;case 12:Ue(e,t),je(t);break;case 13:Ue(e,t),je(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Mf=le()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,_f(t,a)));break;case 22:n=t.memoizedState!==null;var r=l!==null&&l.memoizedState!==null,b=Rl,x=Wt;if(Rl=b||n,Wt=x||r,Ue(e,t),Wt=x,Rl=b,je(t),a&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(l===null||r||Rl||Wt||Ya(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){r=l=e;try{if(u=r.stateNode,n)i=u.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none";else{c=r.stateNode;var R=r.memoizedProps.style,E=R!=null&&R.hasOwnProperty("display")?R.display:null;c.style.display=E==null||typeof E=="boolean"?"":(""+E).trim()}}catch(T){wt(r,r.return,T)}}}else if(e.tag===6){if(l===null){r=e;try{r.stateNode.nodeValue=n?"":r.memoizedProps}catch(T){wt(r,r.return,T)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,_f(t,l))));break;case 19:Ue(e,t),je(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,_f(t,a)));break;case 30:break;case 21:break;default:Ue(e,t),je(t)}}function je(t){var e=t.flags;if(e&2){try{for(var l,a=t.return;a!==null;){if(wo(a)){l=a;break}a=a.return}if(l==null)throw Error(f(160));switch(l.tag){case 27:var n=l.stateNode,u=Sf(t);Mi(t,u,n);break;case 5:var i=l.stateNode;l.flags&32&&(el(i,""),l.flags&=-33);var c=Sf(t);Mi(t,c,i);break;case 3:case 4:var r=l.stateNode.containerInfo,b=Sf(t);bf(t,b,r);break;default:throw Error(f(161))}}catch(x){wt(t,t.return,x)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Go(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Go(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function na(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Co(t,e.alternate,e),e=e.sibling}function Ya(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:aa(4,e,e.return),Ya(e);break;case 1:vl(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&Uo(e,e.return,l),Ya(e);break;case 27:Eu(e.stateNode);case 26:case 5:vl(e,e.return),Ya(e);break;case 22:e.memoizedState===null&&Ya(e);break;case 30:Ya(e);break;default:Ya(e)}t=t.sibling}}function ua(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,n=t,u=e,i=u.flags;switch(u.tag){case 0:case 11:case 15:ua(n,u,l),du(4,u);break;case 1:if(ua(n,u,l),a=u,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(b){wt(a,a.return,b)}if(a=u,n=a.updateQueue,n!==null){var c=a.stateNode;try{var r=n.shared.hiddenCallbacks;if(r!==null)for(n.shared.hiddenCallbacks=null,n=0;n<r.length;n++)yr(r[n],c)}catch(b){wt(a,a.return,b)}}l&&i&64&&Ro(u),vu(u,u.return);break;case 27:Ho(u);case 26:case 5:ua(n,u,l),l&&a===null&&i&4&&jo(u),vu(u,u.return);break;case 12:ua(n,u,l);break;case 13:ua(n,u,l),l&&i&4&&Vo(n,u);break;case 22:u.memoizedState===null&&ua(n,u,l),vu(u,u.return);break;case 30:break;default:ua(n,u,l)}e=e.sibling}}function Ef(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&Pn(l))}function Tf(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Pn(t))}function ml(t,e,l,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Xo(t,e,l,a),e=e.sibling}function Xo(t,e,l,a){var n=e.flags;switch(e.tag){case 0:case 11:case 15:ml(t,e,l,a),n&2048&&du(9,e);break;case 1:ml(t,e,l,a);break;case 3:ml(t,e,l,a),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Pn(t)));break;case 12:if(n&2048){ml(t,e,l,a),t=e.stateNode;try{var u=e.memoizedProps,i=u.id,c=u.onPostCommit;typeof c=="function"&&c(i,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(r){wt(e,e.return,r)}}else ml(t,e,l,a);break;case 13:ml(t,e,l,a);break;case 23:break;case 22:u=e.stateNode,i=e.alternate,e.memoizedState!==null?u._visibility&2?ml(t,e,l,a):mu(t,e):u._visibility&2?ml(t,e,l,a):(u._visibility|=2,bn(t,e,l,a,(e.subtreeFlags&10256)!==0)),n&2048&&Ef(i,e);break;case 24:ml(t,e,l,a),n&2048&&Tf(e.alternate,e);break;default:ml(t,e,l,a)}}function bn(t,e,l,a,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,i=e,c=l,r=a,b=i.flags;switch(i.tag){case 0:case 11:case 15:bn(u,i,c,r,n),du(8,i);break;case 23:break;case 22:var x=i.stateNode;i.memoizedState!==null?x._visibility&2?bn(u,i,c,r,n):mu(u,i):(x._visibility|=2,bn(u,i,c,r,n)),n&&b&2048&&Ef(i.alternate,i);break;case 24:bn(u,i,c,r,n),n&&b&2048&&Tf(i.alternate,i);break;default:bn(u,i,c,r,n)}e=e.sibling}}function mu(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,a=e,n=a.flags;switch(a.tag){case 22:mu(l,a),n&2048&&Ef(a.alternate,a);break;case 24:mu(l,a),n&2048&&Tf(a.alternate,a);break;default:mu(l,a)}e=e.sibling}}var yu=8192;function zn(t){if(t.subtreeFlags&yu)for(t=t.child;t!==null;)Qo(t),t=t.sibling}function Qo(t){switch(t.tag){case 26:zn(t),t.flags&yu&&t.memoizedState!==null&&x2(al,t.memoizedState,t.memoizedProps);break;case 5:zn(t);break;case 3:case 4:var e=al;al=Li(t.stateNode.containerInfo),zn(t),al=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=yu,yu=16777216,zn(t),yu=e):zn(t));break;default:zn(t)}}function Zo(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function gu(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];ue=a,Jo(a,t)}Zo(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Ko(t),t=t.sibling}function Ko(t){switch(t.tag){case 0:case 11:case 15:gu(t),t.flags&2048&&aa(9,t,t.return);break;case 3:gu(t);break;case 12:gu(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Di(t)):gu(t);break;default:gu(t)}}function Di(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];ue=a,Jo(a,t)}Zo(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:aa(8,e,e.return),Di(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,Di(e));break;default:Di(e)}t=t.sibling}}function Jo(t,e){for(;ue!==null;){var l=ue;switch(l.tag){case 0:case 11:case 15:aa(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:Pn(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,ue=a;else t:for(l=t;ue!==null;){a=ue;var n=a.sibling,u=a.return;if(Yo(a),a===l){ue=null;break t}if(n!==null){n.return=u,ue=n;break t}ue=u}}}var Gd={getCacheForType:function(t){var e=ye(te),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},Xd=typeof WeakMap=="function"?WeakMap:Map,Ot=0,Bt=null,gt=null,St=0,Mt=0,we=null,ia=!1,_n=!1,Af=!1,jl=0,Jt=0,ca=0,qa=0,xf=0,We=0,En=0,pu=null,xe=null,Of=!1,Mf=0,Ni=1/0,Ri=null,fa=null,se=0,sa=null,Tn=null,An=0,Df=0,Nf=null,$o=null,Su=0,Rf=null;function He(){if((Ot&2)!==0&&St!==0)return St&-St;if(V.T!==null){var t=hn;return t!==0?t:Yf()}return ba()}function Wo(){We===0&&(We=(St&536870912)===0||Tt?Xu():536870912);var t=$e.current;return t!==null&&(t.flags|=32),We}function Be(t,e,l){(t===Bt&&(Mt===2||Mt===9)||t.cancelPendingCommit!==null)&&(xn(t,0),ra(t,St,We,!1)),ql(t,l),((Ot&2)===0||t!==Bt)&&(t===Bt&&((Ot&2)===0&&(qa|=l),Jt===4&&ra(t,St,We,!1)),yl(t))}function ko(t,e,l){if((Ot&6)!==0)throw Error(f(327));var a=!l&&(e&124)===0&&(e&t.expiredLanes)===0||cl(t,e),n=a?Kd(t,e):wf(t,e,!0),u=a;do{if(n===0){_n&&!a&&ra(t,e,0,!1);break}else{if(l=t.current.alternate,u&&!Qd(l)){n=wf(t,e,!1),u=!1;continue}if(n===2){if(u=e,t.errorRecoveryDisabledLanes&u)var i=0;else i=t.pendingLanes&-536870913,i=i!==0?i:i&536870912?536870912:0;if(i!==0){e=i;t:{var c=t;n=pu;var r=c.current.memoizedState.isDehydrated;if(r&&(xn(c,i).flags|=256),i=wf(c,i,!1),i!==2){if(Af&&!r){c.errorRecoveryDisabledLanes|=u,qa|=u,n=4;break t}u=xe,xe=n,u!==null&&(xe===null?xe=u:xe.push.apply(xe,u))}n=i}if(u=!1,n!==2)continue}}if(n===1){xn(t,0),ra(t,e,0,!0);break}t:{switch(a=t,u=n,u){case 0:case 1:throw Error(f(345));case 4:if((e&4194048)!==e)break;case 6:ra(a,e,We,!ia);break t;case 2:xe=null;break;case 3:case 5:break;default:throw Error(f(329))}if((e&62914560)===e&&(n=Mf+300-le(),10<n)){if(ra(a,e,We,!ia),qe(a,0,!0)!==0)break t;a.timeoutHandle=A0(Fo.bind(null,a,l,xe,Ri,Of,e,We,qa,En,ia,u,2,-0,0),n);break t}Fo(a,l,xe,Ri,Of,e,We,qa,En,ia,u,0,-0,0)}}break}while(!0);yl(t)}function Fo(t,e,l,a,n,u,i,c,r,b,x,R,E,T){if(t.timeoutHandle=-1,R=e.subtreeFlags,(R&8192||(R&16785408)===16785408)&&(xu={stylesheets:null,count:0,unsuspend:A2},Qo(e),R=O2(),R!==null)){t.cancelPendingCommit=R(n0.bind(null,t,e,u,l,a,n,i,c,r,x,1,E,T)),ra(t,u,i,!b);return}n0(t,e,u,l,a,n,i,c,r)}function Qd(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],u=n.getSnapshot;n=n.value;try{if(!Ne(u(),n))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function ra(t,e,l,a){e&=~xf,e&=~qa,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var n=e;0<n;){var u=31-Se(n),i=1<<u;a[u]=-1,n&=~i}l!==0&&Zu(t,l,e)}function Ui(){return(Ot&6)===0?(bu(0),!1):!0}function Uf(){if(gt!==null){if(Mt===0)var t=gt.return;else t=gt,Al=ja=null,$c(t),pn=null,ru=0,t=gt;for(;t!==null;)No(t.alternate,t),t=t.return;gt=null}}function xn(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,f2(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),Uf(),Bt=t,gt=l=_l(t.current,null),St=e,Mt=0,we=null,ia=!1,_n=cl(t,e),Af=!1,En=We=xf=qa=ca=Jt=0,xe=pu=null,Of=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var n=31-Se(a),u=1<<n;e|=t[n],a&=~u}return jl=e,ti(),l}function Io(t,e){dt=null,V.H=Si,e===eu||e===si?(e=vr(),Mt=3):e===or?(e=vr(),Mt=4):Mt=e===yo?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,we=e,gt===null&&(Jt=1,Ti(t,Qe(e,t.current)))}function Po(){var t=V.H;return V.H=Si,t===null?Si:t}function t0(){var t=V.A;return V.A=Gd,t}function jf(){Jt=4,ia||(St&4194048)!==St&&$e.current!==null||(_n=!0),(ca&134217727)===0&&(qa&134217727)===0||Bt===null||ra(Bt,St,We,!1)}function wf(t,e,l){var a=Ot;Ot|=2;var n=Po(),u=t0();(Bt!==t||St!==e)&&(Ri=null,xn(t,e)),e=!1;var i=Jt;t:do try{if(Mt!==0&&gt!==null){var c=gt,r=we;switch(Mt){case 8:Uf(),i=6;break t;case 3:case 2:case 9:case 6:$e.current===null&&(e=!0);var b=Mt;if(Mt=0,we=null,On(t,c,r,b),l&&_n){i=0;break t}break;default:b=Mt,Mt=0,we=null,On(t,c,r,b)}}Zd(),i=Jt;break}catch(x){Io(t,x)}while(!0);return e&&t.shellSuspendCounter++,Al=ja=null,Ot=a,V.H=n,V.A=u,gt===null&&(Bt=null,St=0,ti()),i}function Zd(){for(;gt!==null;)e0(gt)}function Kd(t,e){var l=Ot;Ot|=2;var a=Po(),n=t0();Bt!==t||St!==e?(Ri=null,Ni=le()+500,xn(t,e)):_n=cl(t,e);t:do try{if(Mt!==0&&gt!==null){e=gt;var u=we;e:switch(Mt){case 1:Mt=0,we=null,On(t,e,u,1);break;case 2:case 9:if(hr(u)){Mt=0,we=null,l0(e);break}e=function(){Mt!==2&&Mt!==9||Bt!==t||(Mt=7),yl(t)},u.then(e,e);break t;case 3:Mt=7;break t;case 4:Mt=5;break t;case 7:hr(u)?(Mt=0,we=null,l0(e)):(Mt=0,we=null,On(t,e,u,7));break;case 5:var i=null;switch(gt.tag){case 26:i=gt.memoizedState;case 5:case 27:var c=gt;if(!i||C0(i)){Mt=0,we=null;var r=c.sibling;if(r!==null)gt=r;else{var b=c.return;b!==null?(gt=b,ji(b)):gt=null}break e}}Mt=0,we=null,On(t,e,u,5);break;case 6:Mt=0,we=null,On(t,e,u,6);break;case 8:Uf(),Jt=6;break t;default:throw Error(f(462))}}Jd();break}catch(x){Io(t,x)}while(!0);return Al=ja=null,V.H=a,V.A=n,Ot=l,gt!==null?0:(Bt=null,St=0,ti(),Jt)}function Jd(){for(;gt!==null&&!Yu();)e0(gt)}function e0(t){var e=Mo(t.alternate,t,jl);t.memoizedProps=t.pendingProps,e===null?ji(t):gt=e}function l0(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=_o(l,e,e.pendingProps,e.type,void 0,St);break;case 11:e=_o(l,e,e.pendingProps,e.type.render,e.ref,St);break;case 5:$c(e);default:No(l,e),e=gt=lr(e,jl),e=Mo(l,e,jl)}t.memoizedProps=t.pendingProps,e===null?ji(t):gt=e}function On(t,e,l,a){Al=ja=null,$c(e),pn=null,ru=0;var n=e.return;try{if(Bd(t,n,e,l,St)){Jt=1,Ti(t,Qe(l,t.current)),gt=null;return}}catch(u){if(n!==null)throw gt=n,u;Jt=1,Ti(t,Qe(l,t.current)),gt=null;return}e.flags&32768?(Tt||a===1?t=!0:_n||(St&536870912)!==0?t=!1:(ia=t=!0,(a===2||a===9||a===3||a===6)&&(a=$e.current,a!==null&&a.tag===13&&(a.flags|=16384))),a0(e,t)):ji(e)}function ji(t){var e=t;do{if((e.flags&32768)!==0){a0(e,ia);return}t=e.return;var l=Yd(e.alternate,e,jl);if(l!==null){gt=l;return}if(e=e.sibling,e!==null){gt=e;return}gt=e=t}while(e!==null);Jt===0&&(Jt=5)}function a0(t,e){do{var l=qd(t.alternate,t);if(l!==null){l.flags&=32767,gt=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){gt=t;return}gt=t=l}while(t!==null);Jt=6,gt=null}function n0(t,e,l,a,n,u,i,c,r){t.cancelPendingCommit=null;do wi();while(se!==0);if((Ot&6)!==0)throw Error(f(327));if(e!==null){if(e===t.current)throw Error(f(177));if(u=e.lanes|e.childLanes,u|=Ec,Qu(t,l,u,i,c,r),t===Bt&&(gt=Bt=null,St=0),Tn=e,sa=t,An=l,Df=u,Nf=n,$o=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Fd(Cl,function(){return s0(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=V.T,V.T=null,n=F.p,F.p=2,i=Ot,Ot|=4;try{Vd(t,e,l)}finally{Ot=i,F.p=n,V.T=a}}se=1,u0(),i0(),c0()}}function u0(){if(se===1){se=0;var t=sa,e=Tn,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=V.T,V.T=null;var a=F.p;F.p=2;var n=Ot;Ot|=4;try{Lo(e,t);var u=Kf,i=Ks(t.containerInfo),c=u.focusedElem,r=u.selectionRange;if(i!==c&&c&&c.ownerDocument&&Zs(c.ownerDocument.documentElement,c)){if(r!==null&&pc(c)){var b=r.start,x=r.end;if(x===void 0&&(x=b),"selectionStart"in c)c.selectionStart=b,c.selectionEnd=Math.min(x,c.value.length);else{var R=c.ownerDocument||document,E=R&&R.defaultView||window;if(E.getSelection){var T=E.getSelection(),ct=c.textContent.length,at=Math.min(r.start,ct),Ut=r.end===void 0?at:Math.min(r.end,ct);!T.extend&&at>Ut&&(i=Ut,Ut=at,at=i);var p=Qs(c,at),m=Qs(c,Ut);if(p&&m&&(T.rangeCount!==1||T.anchorNode!==p.node||T.anchorOffset!==p.offset||T.focusNode!==m.node||T.focusOffset!==m.offset)){var S=R.createRange();S.setStart(p.node,p.offset),T.removeAllRanges(),at>Ut?(T.addRange(S),T.extend(m.node,m.offset)):(S.setEnd(m.node,m.offset),T.addRange(S))}}}}for(R=[],T=c;T=T.parentNode;)T.nodeType===1&&R.push({element:T,left:T.scrollLeft,top:T.scrollTop});for(typeof c.focus=="function"&&c.focus(),c=0;c<R.length;c++){var D=R[c];D.element.scrollLeft=D.left,D.element.scrollTop=D.top}}Ki=!!Zf,Kf=Zf=null}finally{Ot=n,F.p=a,V.T=l}}t.current=e,se=2}}function i0(){if(se===2){se=0;var t=sa,e=Tn,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=V.T,V.T=null;var a=F.p;F.p=2;var n=Ot;Ot|=4;try{Co(t,e.alternate,e)}finally{Ot=n,F.p=a,V.T=l}}se=3}}function c0(){if(se===4||se===3){se=0,ac();var t=sa,e=Tn,l=An,a=$o;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?se=5:(se=0,Tn=sa=null,f0(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(fa=null),Ja(l),e=e.stateNode,pe&&typeof pe.onCommitFiberRoot=="function")try{pe.onCommitFiberRoot(tl,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=V.T,n=F.p,F.p=2,V.T=null;try{for(var u=t.onRecoverableError,i=0;i<a.length;i++){var c=a[i];u(c.value,{componentStack:c.stack})}}finally{V.T=e,F.p=n}}(An&3)!==0&&wi(),yl(t),n=t.pendingLanes,(l&4194090)!==0&&(n&42)!==0?t===Rf?Su++:(Su=0,Rf=t):Su=0,bu(0)}}function f0(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Pn(e)))}function wi(t){return u0(),i0(),c0(),s0()}function s0(){if(se!==5)return!1;var t=sa,e=Df;Df=0;var l=Ja(An),a=V.T,n=F.p;try{F.p=32>l?32:l,V.T=null,l=Nf,Nf=null;var u=sa,i=An;if(se=0,Tn=sa=null,An=0,(Ot&6)!==0)throw Error(f(331));var c=Ot;if(Ot|=4,Ko(u.current),Xo(u,u.current,i,l),Ot=c,bu(0,!1),pe&&typeof pe.onPostCommitFiberRoot=="function")try{pe.onPostCommitFiberRoot(tl,u)}catch{}return!0}finally{F.p=n,V.T=a,f0(t,e)}}function r0(t,e,l){e=Qe(l,e),e=sf(t.stateNode,e,2),t=Pl(t,e,2),t!==null&&(ql(t,2),yl(t))}function wt(t,e,l){if(t.tag===3)r0(t,t,l);else for(;e!==null;){if(e.tag===3){r0(e,t,l);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(fa===null||!fa.has(a))){t=Qe(l,t),l=vo(2),a=Pl(e,l,2),a!==null&&(mo(l,a,e,t),ql(a,2),yl(a));break}}e=e.return}}function Hf(t,e,l){var a=t.pingCache;if(a===null){a=t.pingCache=new Xd;var n=new Set;a.set(e,n)}else n=a.get(e),n===void 0&&(n=new Set,a.set(e,n));n.has(l)||(Af=!0,n.add(l),t=$d.bind(null,t,e,l),e.then(t,t))}function $d(t,e,l){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,Bt===t&&(St&l)===l&&(Jt===4||Jt===3&&(St&62914560)===St&&300>le()-Mf?(Ot&2)===0&&xn(t,0):xf|=l,En===St&&(En=0)),yl(t)}function o0(t,e){e===0&&(e=Sa()),t=fn(t,e),t!==null&&(ql(t,e),yl(t))}function Wd(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),o0(t,l)}function kd(t,e){var l=0;switch(t.tag){case 13:var a=t.stateNode,n=t.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(f(314))}a!==null&&a.delete(e),o0(t,l)}function Fd(t,e){return Bl(t,e)}var Hi=null,Mn=null,Bf=!1,Bi=!1,Cf=!1,Va=0;function yl(t){t!==Mn&&t.next===null&&(Mn===null?Hi=Mn=t:Mn=Mn.next=t),Bi=!0,Bf||(Bf=!0,Pd())}function bu(t,e){if(!Cf&&Bi){Cf=!0;do for(var l=!1,a=Hi;a!==null;){if(t!==0){var n=a.pendingLanes;if(n===0)var u=0;else{var i=a.suspendedLanes,c=a.pingedLanes;u=(1<<31-Se(42|t)+1)-1,u&=n&~(i&~c),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(l=!0,m0(a,u))}else u=St,u=qe(a,a===Bt?u:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(u&3)===0||cl(a,u)||(l=!0,m0(a,u));a=a.next}while(l);Cf=!1}}function Id(){h0()}function h0(){Bi=Bf=!1;var t=0;Va!==0&&(c2()&&(t=Va),Va=0);for(var e=le(),l=null,a=Hi;a!==null;){var n=a.next,u=d0(a,e);u===0?(a.next=null,l===null?Hi=n:l.next=n,n===null&&(Mn=l)):(l=a,(t!==0||(u&3)!==0)&&(Bi=!0)),a=n}bu(t)}function d0(t,e){for(var l=t.suspendedLanes,a=t.pingedLanes,n=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var i=31-Se(u),c=1<<i,r=n[i];r===-1?((c&l)===0||(c&a)!==0)&&(n[i]=fl(c,e)):r<=e&&(t.expiredLanes|=c),u&=~c}if(e=Bt,l=St,l=qe(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,l===0||t===e&&(Mt===2||Mt===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&Qa(a),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||cl(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(a!==null&&Qa(a),Ja(l)){case 2:case 8:l=Lu;break;case 32:l=Cl;break;case 268435456:l=Za;break;default:l=Cl}return a=v0.bind(null,t),l=Bl(l,a),t.callbackPriority=e,t.callbackNode=l,e}return a!==null&&a!==null&&Qa(a),t.callbackPriority=2,t.callbackNode=null,2}function v0(t,e){if(se!==0&&se!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(wi()&&t.callbackNode!==l)return null;var a=St;return a=qe(t,t===Bt?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(ko(t,a,e),d0(t,le()),t.callbackNode!=null&&t.callbackNode===l?v0.bind(null,t):null)}function m0(t,e){if(wi())return null;ko(t,e,!0)}function Pd(){s2(function(){(Ot&6)!==0?Bl(Vu,Id):h0()})}function Yf(){return Va===0&&(Va=Xu()),Va}function y0(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Ta(""+t)}function g0(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function t2(t,e,l,a,n){if(e==="submit"&&l&&l.stateNode===n){var u=y0((n[he]||null).action),i=a.submitter;i&&(e=(e=i[he]||null)?y0(e.formAction):i.getAttribute("formAction"),e!==null&&(u=e,i=null));var c=new Fu("action","action",null,a,n);t.push({event:c,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Va!==0){var r=i?g0(n,i):new FormData(n);af(l,{pending:!0,data:r,method:n.method,action:u},null,r)}}else typeof u=="function"&&(c.preventDefault(),r=i?g0(n,i):new FormData(n),af(l,{pending:!0,data:r,method:n.method,action:u},u,r))},currentTarget:n}]})}}for(var qf=0;qf<_c.length;qf++){var Vf=_c[qf],e2=Vf.toLowerCase(),l2=Vf[0].toUpperCase()+Vf.slice(1);ll(e2,"on"+l2)}ll(Ws,"onAnimationEnd"),ll(ks,"onAnimationIteration"),ll(Fs,"onAnimationStart"),ll("dblclick","onDoubleClick"),ll("focusin","onFocus"),ll("focusout","onBlur"),ll(Sd,"onTransitionRun"),ll(bd,"onTransitionStart"),ll(zd,"onTransitionCancel"),ll(Is,"onTransitionEnd"),bt("onMouseEnter",["mouseout","mouseover"]),bt("onMouseLeave",["mouseout","mouseover"]),bt("onPointerEnter",["pointerout","pointerover"]),bt("onPointerLeave",["pointerout","pointerover"]),K("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),K("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),K("onBeforeInput",["compositionend","keypress","textInput","paste"]),K("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),K("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),K("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zu="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),a2=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(zu));function p0(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var a=t[l],n=a.event;a=a.listeners;t:{var u=void 0;if(e)for(var i=a.length-1;0<=i;i--){var c=a[i],r=c.instance,b=c.currentTarget;if(c=c.listener,r!==u&&n.isPropagationStopped())break t;u=c,n.currentTarget=b;try{u(n)}catch(x){Ei(x)}n.currentTarget=null,u=r}else for(i=0;i<a.length;i++){if(c=a[i],r=c.instance,b=c.currentTarget,c=c.listener,r!==u&&n.isPropagationStopped())break t;u=c,n.currentTarget=b;try{u(n)}catch(x){Ei(x)}n.currentTarget=null,u=r}}}}function pt(t,e){var l=e[$a];l===void 0&&(l=e[$a]=new Set);var a=t+"__bubble";l.has(a)||(S0(e,t,2,!1),l.add(a))}function Lf(t,e,l){var a=0;e&&(a|=4),S0(l,t,a,e)}var Ci="_reactListening"+Math.random().toString(36).slice(2);function Gf(t){if(!t[Ci]){t[Ci]=!0,A.forEach(function(l){l!=="selectionchange"&&(a2.has(l)||Lf(l,!1,t),Lf(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Ci]||(e[Ci]=!0,Lf("selectionchange",!1,e))}}function S0(t,e,l,a){switch(X0(e)){case 2:var n=N2;break;case 8:n=R2;break;default:n=ls}l=n.bind(null,e,l,t),n=void 0,!sc||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),a?n!==void 0?t.addEventListener(e,l,{capture:!0,passive:n}):t.addEventListener(e,l,!0):n!==void 0?t.addEventListener(e,l,{passive:n}):t.addEventListener(e,l,!1)}function Xf(t,e,l,a,n){var u=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var i=a.tag;if(i===3||i===4){var c=a.stateNode.containerInfo;if(c===n)break;if(i===4)for(i=a.return;i!==null;){var r=i.tag;if((r===3||r===4)&&i.stateNode.containerInfo===n)return;i=i.return}for(;c!==null;){if(i=pl(c),i===null)return;if(r=i.tag,r===5||r===6||r===26||r===27){a=u=i;continue t}c=c.parentNode}}a=a.return}As(function(){var b=u,x=$l(l),R=[];t:{var E=Ps.get(t);if(E!==void 0){var T=Fu,ct=t;switch(t){case"keypress":if(Wu(l)===0)break t;case"keydown":case"keyup":T=Fh;break;case"focusin":ct="focus",T=dc;break;case"focusout":ct="blur",T=dc;break;case"beforeblur":case"afterblur":T=dc;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":T=Ms;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":T=qh;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":T=td;break;case Ws:case ks:case Fs:T=Gh;break;case Is:T=ld;break;case"scroll":case"scrollend":T=Ch;break;case"wheel":T=nd;break;case"copy":case"cut":case"paste":T=Qh;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":T=Ns;break;case"toggle":case"beforetoggle":T=id}var at=(e&4)!==0,Ut=!at&&(t==="scroll"||t==="scrollend"),p=at?E!==null?E+"Capture":null:E;at=[];for(var m=b,S;m!==null;){var D=m;if(S=D.stateNode,D=D.tag,D!==5&&D!==26&&D!==27||S===null||p===null||(D=Vn(m,p),D!=null&&at.push(_u(m,D,S))),Ut)break;m=m.return}0<at.length&&(E=new T(E,ct,null,l,x),R.push({event:E,listeners:at}))}}if((e&7)===0){t:{if(E=t==="mouseover"||t==="pointerover",T=t==="mouseout"||t==="pointerout",E&&l!==Jl&&(ct=l.relatedTarget||l.fromElement)&&(pl(ct)||ct[_e]))break t;if((T||E)&&(E=x.window===x?x:(E=x.ownerDocument)?E.defaultView||E.parentWindow:window,T?(ct=l.relatedTarget||l.toElement,T=b,ct=ct?pl(ct):null,ct!==null&&(Ut=M(ct),at=ct.tag,ct!==Ut||at!==5&&at!==27&&at!==6)&&(ct=null)):(T=null,ct=b),T!==ct)){if(at=Ms,D="onMouseLeave",p="onMouseEnter",m="mouse",(t==="pointerout"||t==="pointerover")&&(at=Ns,D="onPointerLeave",p="onPointerEnter",m="pointer"),Ut=T==null?E:Gl(T),S=ct==null?E:Gl(ct),E=new at(D,m+"leave",T,l,x),E.target=Ut,E.relatedTarget=S,D=null,pl(x)===b&&(at=new at(p,m+"enter",ct,l,x),at.target=S,at.relatedTarget=Ut,D=at),Ut=D,T&&ct)e:{for(at=T,p=ct,m=0,S=at;S;S=Dn(S))m++;for(S=0,D=p;D;D=Dn(D))S++;for(;0<m-S;)at=Dn(at),m--;for(;0<S-m;)p=Dn(p),S--;for(;m--;){if(at===p||p!==null&&at===p.alternate)break e;at=Dn(at),p=Dn(p)}at=null}else at=null;T!==null&&b0(R,E,T,at,!1),ct!==null&&Ut!==null&&b0(R,Ut,ct,at,!0)}}t:{if(E=b?Gl(b):window,T=E.nodeName&&E.nodeName.toLowerCase(),T==="select"||T==="input"&&E.type==="file")var J=Ys;else if(Bs(E))if(qs)J=yd;else{J=vd;var mt=dd}else T=E.nodeName,!T||T.toLowerCase()!=="input"||E.type!=="checkbox"&&E.type!=="radio"?b&&Ea(b.elementType)&&(J=Ys):J=md;if(J&&(J=J(t,b))){Cs(R,J,l,x);break t}mt&&mt(t,E,b),t==="focusout"&&b&&E.type==="number"&&b.memoizedProps.value!=null&&Ge(E,"number",E.value)}switch(mt=b?Gl(b):window,t){case"focusin":(Bs(mt)||mt.contentEditable==="true")&&(nn=mt,Sc=b,$n=null);break;case"focusout":$n=Sc=nn=null;break;case"mousedown":bc=!0;break;case"contextmenu":case"mouseup":case"dragend":bc=!1,Js(R,l,x);break;case"selectionchange":if(pd)break;case"keydown":case"keyup":Js(R,l,x)}var I;if(mc)t:{switch(t){case"compositionstart":var nt="onCompositionStart";break t;case"compositionend":nt="onCompositionEnd";break t;case"compositionupdate":nt="onCompositionUpdate";break t}nt=void 0}else an?ws(t,l)&&(nt="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(nt="onCompositionStart");nt&&(Rs&&l.locale!=="ko"&&(an||nt!=="onCompositionStart"?nt==="onCompositionEnd"&&an&&(I=xs()):(Wl=x,rc="value"in Wl?Wl.value:Wl.textContent,an=!0)),mt=Yi(b,nt),0<mt.length&&(nt=new Ds(nt,t,null,l,x),R.push({event:nt,listeners:mt}),I?nt.data=I:(I=Hs(l),I!==null&&(nt.data=I)))),(I=fd?sd(t,l):rd(t,l))&&(nt=Yi(b,"onBeforeInput"),0<nt.length&&(mt=new Ds("onBeforeInput","beforeinput",null,l,x),R.push({event:mt,listeners:nt}),mt.data=I)),t2(R,t,b,l,x)}p0(R,e)})}function _u(t,e,l){return{instance:t,listener:e,currentTarget:l}}function Yi(t,e){for(var l=e+"Capture",a=[];t!==null;){var n=t,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=Vn(t,l),n!=null&&a.unshift(_u(t,n,u)),n=Vn(t,e),n!=null&&a.push(_u(t,n,u))),t.tag===3)return a;t=t.return}return[]}function Dn(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function b0(t,e,l,a,n){for(var u=e._reactName,i=[];l!==null&&l!==a;){var c=l,r=c.alternate,b=c.stateNode;if(c=c.tag,r!==null&&r===a)break;c!==5&&c!==26&&c!==27||b===null||(r=b,n?(b=Vn(l,u),b!=null&&i.unshift(_u(l,b,r))):n||(b=Vn(l,u),b!=null&&i.push(_u(l,b,r)))),l=l.return}i.length!==0&&t.push({event:e,listeners:i})}var n2=/\r\n?/g,u2=/\u0000|\uFFFD/g;function z0(t){return(typeof t=="string"?t:""+t).replace(n2,`
`).replace(u2,"")}function _0(t,e){return e=z0(e),z0(t)===e}function qi(){}function Rt(t,e,l,a,n,u){switch(l){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||el(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&el(t,""+a);break;case"className":Gt(t,"class",a);break;case"tabIndex":Gt(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Gt(t,l,a);break;case"style":_a(t,a,u);break;case"data":if(e!=="object"){Gt(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=Ta(""+a),t.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(e!=="input"&&Rt(t,e,"name",n.name,n,null),Rt(t,e,"formEncType",n.formEncType,n,null),Rt(t,e,"formMethod",n.formMethod,n,null),Rt(t,e,"formTarget",n.formTarget,n,null)):(Rt(t,e,"encType",n.encType,n,null),Rt(t,e,"method",n.method,n,null),Rt(t,e,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=Ta(""+a),t.setAttribute(l,a);break;case"onClick":a!=null&&(t.onclick=qi);break;case"onScroll":a!=null&&pt("scroll",t);break;case"onScrollEnd":a!=null&&pt("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(f(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(f(60));t.innerHTML=l}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}l=Ta(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""+a):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":a===!0?t.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,a):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(l,a):t.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(l):t.setAttribute(l,a);break;case"popover":pt("beforetoggle",t),pt("toggle",t),Lt(t,"popover",a);break;case"xlinkActuate":Ft(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Ft(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Ft(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Ft(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Ft(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Ft(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Ft(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Ft(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Ft(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Lt(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=Pa.get(l)||l,Lt(t,l,a))}}function Qf(t,e,l,a,n,u){switch(l){case"style":_a(t,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(f(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(f(60));t.innerHTML=l}}break;case"children":typeof a=="string"?el(t,a):(typeof a=="number"||typeof a=="bigint")&&el(t,""+a);break;case"onScroll":a!=null&&pt("scroll",t);break;case"onScrollEnd":a!=null&&pt("scrollend",t);break;case"onClick":a!=null&&(t.onclick=qi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!q.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),e=l.slice(2,n?l.length-7:void 0),u=t[he]||null,u=u!=null?u[l]:null,typeof u=="function"&&t.removeEventListener(e,u,n),typeof a=="function")){typeof u!="function"&&u!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,a,n);break t}l in t?t[l]=a:a===!0?t.setAttribute(l,""):Lt(t,l,a)}}}function re(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":pt("error",t),pt("load",t);var a=!1,n=!1,u;for(u in l)if(l.hasOwnProperty(u)){var i=l[u];if(i!=null)switch(u){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(f(137,e));default:Rt(t,e,u,i,l,null)}}n&&Rt(t,e,"srcSet",l.srcSet,l,null),a&&Rt(t,e,"src",l.src,l,null);return;case"input":pt("invalid",t);var c=u=i=n=null,r=null,b=null;for(a in l)if(l.hasOwnProperty(a)){var x=l[a];if(x!=null)switch(a){case"name":n=x;break;case"type":i=x;break;case"checked":r=x;break;case"defaultChecked":b=x;break;case"value":u=x;break;case"defaultValue":c=x;break;case"children":case"dangerouslySetInnerHTML":if(x!=null)throw Error(f(137,e));break;default:Rt(t,e,a,x,l,null)}}Le(t,u,c,r,b,i,n,!1),zt(t);return;case"select":pt("invalid",t),a=i=u=null;for(n in l)if(l.hasOwnProperty(n)&&(c=l[n],c!=null))switch(n){case"value":u=c;break;case"defaultValue":i=c;break;case"multiple":a=c;default:Rt(t,e,n,c,l,null)}e=u,l=i,t.multiple=!!a,e!=null?Xe(t,!!a,e,!1):l!=null&&Xe(t,!!a,l,!0);return;case"textarea":pt("invalid",t),u=n=a=null;for(i in l)if(l.hasOwnProperty(i)&&(c=l[i],c!=null))switch(i){case"value":a=c;break;case"defaultValue":n=c;break;case"children":u=c;break;case"dangerouslySetInnerHTML":if(c!=null)throw Error(f(91));break;default:Rt(t,e,i,c,l,null)}Ia(t,a,n,u),zt(t);return;case"option":for(r in l)if(l.hasOwnProperty(r)&&(a=l[r],a!=null))switch(r){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:Rt(t,e,r,a,l,null)}return;case"dialog":pt("beforetoggle",t),pt("toggle",t),pt("cancel",t),pt("close",t);break;case"iframe":case"object":pt("load",t);break;case"video":case"audio":for(a=0;a<zu.length;a++)pt(zu[a],t);break;case"image":pt("error",t),pt("load",t);break;case"details":pt("toggle",t);break;case"embed":case"source":case"link":pt("error",t),pt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(b in l)if(l.hasOwnProperty(b)&&(a=l[b],a!=null))switch(b){case"children":case"dangerouslySetInnerHTML":throw Error(f(137,e));default:Rt(t,e,b,a,l,null)}return;default:if(Ea(e)){for(x in l)l.hasOwnProperty(x)&&(a=l[x],a!==void 0&&Qf(t,e,x,a,l,void 0));return}}for(c in l)l.hasOwnProperty(c)&&(a=l[c],a!=null&&Rt(t,e,c,a,l,null))}function i2(t,e,l,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,i=null,c=null,r=null,b=null,x=null;for(T in l){var R=l[T];if(l.hasOwnProperty(T)&&R!=null)switch(T){case"checked":break;case"value":break;case"defaultValue":r=R;default:a.hasOwnProperty(T)||Rt(t,e,T,null,a,R)}}for(var E in a){var T=a[E];if(R=l[E],a.hasOwnProperty(E)&&(T!=null||R!=null))switch(E){case"type":u=T;break;case"name":n=T;break;case"checked":b=T;break;case"defaultChecked":x=T;break;case"value":i=T;break;case"defaultValue":c=T;break;case"children":case"dangerouslySetInnerHTML":if(T!=null)throw Error(f(137,e));break;default:T!==R&&Rt(t,e,E,T,a,R)}}me(t,i,c,r,b,x,u,n);return;case"select":T=i=c=E=null;for(u in l)if(r=l[u],l.hasOwnProperty(u)&&r!=null)switch(u){case"value":break;case"multiple":T=r;default:a.hasOwnProperty(u)||Rt(t,e,u,null,a,r)}for(n in a)if(u=a[n],r=l[n],a.hasOwnProperty(n)&&(u!=null||r!=null))switch(n){case"value":E=u;break;case"defaultValue":c=u;break;case"multiple":i=u;default:u!==r&&Rt(t,e,n,u,a,r)}e=c,l=i,a=T,E!=null?Xe(t,!!l,E,!1):!!a!=!!l&&(e!=null?Xe(t,!!l,e,!0):Xe(t,!!l,l?[]:"",!1));return;case"textarea":T=E=null;for(c in l)if(n=l[c],l.hasOwnProperty(c)&&n!=null&&!a.hasOwnProperty(c))switch(c){case"value":break;case"children":break;default:Rt(t,e,c,null,a,n)}for(i in a)if(n=a[i],u=l[i],a.hasOwnProperty(i)&&(n!=null||u!=null))switch(i){case"value":E=n;break;case"defaultValue":T=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(f(91));break;default:n!==u&&Rt(t,e,i,n,a,u)}za(t,E,T);return;case"option":for(var ct in l)if(E=l[ct],l.hasOwnProperty(ct)&&E!=null&&!a.hasOwnProperty(ct))switch(ct){case"selected":t.selected=!1;break;default:Rt(t,e,ct,null,a,E)}for(r in a)if(E=a[r],T=l[r],a.hasOwnProperty(r)&&E!==T&&(E!=null||T!=null))switch(r){case"selected":t.selected=E&&typeof E!="function"&&typeof E!="symbol";break;default:Rt(t,e,r,E,a,T)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var at in l)E=l[at],l.hasOwnProperty(at)&&E!=null&&!a.hasOwnProperty(at)&&Rt(t,e,at,null,a,E);for(b in a)if(E=a[b],T=l[b],a.hasOwnProperty(b)&&E!==T&&(E!=null||T!=null))switch(b){case"children":case"dangerouslySetInnerHTML":if(E!=null)throw Error(f(137,e));break;default:Rt(t,e,b,E,a,T)}return;default:if(Ea(e)){for(var Ut in l)E=l[Ut],l.hasOwnProperty(Ut)&&E!==void 0&&!a.hasOwnProperty(Ut)&&Qf(t,e,Ut,void 0,a,E);for(x in a)E=a[x],T=l[x],!a.hasOwnProperty(x)||E===T||E===void 0&&T===void 0||Qf(t,e,x,E,a,T);return}}for(var p in l)E=l[p],l.hasOwnProperty(p)&&E!=null&&!a.hasOwnProperty(p)&&Rt(t,e,p,null,a,E);for(R in a)E=a[R],T=l[R],!a.hasOwnProperty(R)||E===T||E==null&&T==null||Rt(t,e,R,E,a,T)}var Zf=null,Kf=null;function Vi(t){return t.nodeType===9?t:t.ownerDocument}function E0(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function T0(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Jf(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var $f=null;function c2(){var t=window.event;return t&&t.type==="popstate"?t===$f?!1:($f=t,!0):($f=null,!1)}var A0=typeof setTimeout=="function"?setTimeout:void 0,f2=typeof clearTimeout=="function"?clearTimeout:void 0,x0=typeof Promise=="function"?Promise:void 0,s2=typeof queueMicrotask=="function"?queueMicrotask:typeof x0<"u"?function(t){return x0.resolve(null).then(t).catch(r2)}:A0;function r2(t){setTimeout(function(){throw t})}function oa(t){return t==="head"}function O0(t,e){var l=e,a=0,n=0;do{var u=l.nextSibling;if(t.removeChild(l),u&&u.nodeType===8)if(l=u.data,l==="/$"){if(0<a&&8>a){l=a;var i=t.ownerDocument;if(l&1&&Eu(i.documentElement),l&2&&Eu(i.body),l&4)for(l=i.head,Eu(l),i=l.firstChild;i;){var c=i.nextSibling,r=i.nodeName;i[Ll]||r==="SCRIPT"||r==="STYLE"||r==="LINK"&&i.rel.toLowerCase()==="stylesheet"||l.removeChild(i),i=c}}if(n===0){t.removeChild(u),Ru(e);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=u}while(l);Ru(e)}function Wf(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":Wf(l),Fa(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function o2(t,e,l,a){for(;t.nodeType===1;){var n=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[Ll])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=nl(t.nextSibling),t===null)break}return null}function h2(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=nl(t.nextSibling),t===null))return null;return t}function kf(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function d2(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var a=function(){e(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function nl(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var Ff=null;function M0(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function D0(t,e,l){switch(e=Vi(l),t){case"html":if(t=e.documentElement,!t)throw Error(f(452));return t;case"head":if(t=e.head,!t)throw Error(f(453));return t;case"body":if(t=e.body,!t)throw Error(f(454));return t;default:throw Error(f(451))}}function Eu(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Fa(t)}var ke=new Map,N0=new Set;function Li(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var wl=F.d;F.d={f:v2,r:m2,D:y2,C:g2,L:p2,m:S2,X:z2,S:b2,M:_2};function v2(){var t=wl.f(),e=Ui();return t||e}function m2(t){var e=Ve(t);e!==null&&e.tag===5&&e.type==="form"?Wr(e):wl.r(t)}var Nn=typeof document>"u"?null:document;function R0(t,e,l){var a=Nn;if(a&&typeof e=="string"&&e){var n=ve(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),N0.has(n)||(N0.add(n),t={rel:t,crossOrigin:l,href:e},a.querySelector(n)===null&&(e=a.createElement("link"),re(e,"link",t),v(e),a.head.appendChild(e)))}}function y2(t){wl.D(t),R0("dns-prefetch",t,null)}function g2(t,e){wl.C(t,e),R0("preconnect",t,e)}function p2(t,e,l){wl.L(t,e,l);var a=Nn;if(a&&t&&e){var n='link[rel="preload"][as="'+ve(e)+'"]';e==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+ve(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+ve(l.imageSizes)+'"]')):n+='[href="'+ve(t)+'"]';var u=n;switch(e){case"style":u=Rn(t);break;case"script":u=Un(t)}ke.has(u)||(t=z({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),ke.set(u,t),a.querySelector(n)!==null||e==="style"&&a.querySelector(Tu(u))||e==="script"&&a.querySelector(Au(u))||(e=a.createElement("link"),re(e,"link",t),v(e),a.head.appendChild(e)))}}function S2(t,e){wl.m(t,e);var l=Nn;if(l&&t){var a=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+ve(a)+'"][href="'+ve(t)+'"]',u=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Un(t)}if(!ke.has(u)&&(t=z({rel:"modulepreload",href:t},e),ke.set(u,t),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Au(u)))return}a=l.createElement("link"),re(a,"link",t),v(a),l.head.appendChild(a)}}}function b2(t,e,l){wl.S(t,e,l);var a=Nn;if(a&&t){var n=Xl(a).hoistableStyles,u=Rn(t);e=e||"default";var i=n.get(u);if(!i){var c={loading:0,preload:null};if(i=a.querySelector(Tu(u)))c.loading=5;else{t=z({rel:"stylesheet",href:t,"data-precedence":e},l),(l=ke.get(u))&&If(t,l);var r=i=a.createElement("link");v(r),re(r,"link",t),r._p=new Promise(function(b,x){r.onload=b,r.onerror=x}),r.addEventListener("load",function(){c.loading|=1}),r.addEventListener("error",function(){c.loading|=2}),c.loading|=4,Gi(i,e,a)}i={type:"stylesheet",instance:i,count:1,state:c},n.set(u,i)}}}function z2(t,e){wl.X(t,e);var l=Nn;if(l&&t){var a=Xl(l).hoistableScripts,n=Un(t),u=a.get(n);u||(u=l.querySelector(Au(n)),u||(t=z({src:t,async:!0},e),(e=ke.get(n))&&Pf(t,e),u=l.createElement("script"),v(u),re(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function _2(t,e){wl.M(t,e);var l=Nn;if(l&&t){var a=Xl(l).hoistableScripts,n=Un(t),u=a.get(n);u||(u=l.querySelector(Au(n)),u||(t=z({src:t,async:!0,type:"module"},e),(e=ke.get(n))&&Pf(t,e),u=l.createElement("script"),v(u),re(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function U0(t,e,l,a){var n=(n=lt.current)?Li(n):null;if(!n)throw Error(f(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=Rn(l.href),l=Xl(n).hoistableStyles,a=l.get(e),a||(a={type:"style",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=Rn(l.href);var u=Xl(n).hoistableStyles,i=u.get(t);if(i||(n=n.ownerDocument||n,i={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,i),(u=n.querySelector(Tu(t)))&&!u._p&&(i.instance=u,i.state.loading=5),ke.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},ke.set(t,l),u||E2(n,t,l,i.state))),e&&a===null)throw Error(f(528,""));return i}if(e&&a!==null)throw Error(f(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Un(l),l=Xl(n).hoistableScripts,a=l.get(e),a||(a={type:"script",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(f(444,t))}}function Rn(t){return'href="'+ve(t)+'"'}function Tu(t){return'link[rel="stylesheet"]['+t+"]"}function j0(t){return z({},t,{"data-precedence":t.precedence,precedence:null})}function E2(t,e,l,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),re(e,"link",l),v(e),t.head.appendChild(e))}function Un(t){return'[src="'+ve(t)+'"]'}function Au(t){return"script[async]"+t}function w0(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+ve(l.href)+'"]');if(a)return e.instance=a,v(a),a;var n=z({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),v(a),re(a,"style",n),Gi(a,l.precedence,t),e.instance=a;case"stylesheet":n=Rn(l.href);var u=t.querySelector(Tu(n));if(u)return e.state.loading|=4,e.instance=u,v(u),u;a=j0(l),(n=ke.get(n))&&If(a,n),u=(t.ownerDocument||t).createElement("link"),v(u);var i=u;return i._p=new Promise(function(c,r){i.onload=c,i.onerror=r}),re(u,"link",a),e.state.loading|=4,Gi(u,l.precedence,t),e.instance=u;case"script":return u=Un(l.src),(n=t.querySelector(Au(u)))?(e.instance=n,v(n),n):(a=l,(n=ke.get(u))&&(a=z({},l),Pf(a,n)),t=t.ownerDocument||t,n=t.createElement("script"),v(n),re(n,"link",a),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(f(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,Gi(a,l.precedence,t));return e.instance}function Gi(t,e,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,u=n,i=0;i<a.length;i++){var c=a[i];if(c.dataset.precedence===e)u=c;else if(u!==n)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function If(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Pf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Xi=null;function H0(t,e,l){if(Xi===null){var a=new Map,n=Xi=new Map;n.set(l,a)}else n=Xi,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(t))return a;for(a.set(t,null),l=l.getElementsByTagName(t),n=0;n<l.length;n++){var u=l[n];if(!(u[Ll]||u[Yt]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var i=u.getAttribute(e)||"";i=t+i;var c=a.get(i);c?c.push(u):a.set(i,[u])}}return a}function B0(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function T2(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function C0(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var xu=null;function A2(){}function x2(t,e,l){if(xu===null)throw Error(f(475));var a=xu;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=Rn(l.href),u=t.querySelector(Tu(n));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Qi.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=u,v(u);return}u=t.ownerDocument||t,l=j0(l),(n=ke.get(n))&&If(l,n),u=u.createElement("link"),v(u);var i=u;i._p=new Promise(function(c,r){i.onload=c,i.onerror=r}),re(u,"link",l),e.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Qi.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function O2(){if(xu===null)throw Error(f(475));var t=xu;return t.stylesheets&&t.count===0&&ts(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&ts(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function Qi(){if(this.count--,this.count===0){if(this.stylesheets)ts(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Zi=null;function ts(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Zi=new Map,e.forEach(M2,t),Zi=null,Qi.call(t))}function M2(t,e){if(!(e.state.loading&4)){var l=Zi.get(t);if(l)var a=l.get(null);else{l=new Map,Zi.set(t,l);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var i=n[u];(i.nodeName==="LINK"||i.getAttribute("media")!=="not all")&&(l.set(i.dataset.precedence,i),a=i)}a&&l.set(null,a)}n=e.instance,i=n.getAttribute("data-precedence"),u=l.get(i)||a,u===a&&l.set(null,n),l.set(i,n),this.count++,a=Qi.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),u?u.parentNode.insertBefore(n,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var Ou={$$typeof:k,Provider:null,Consumer:null,_currentValue:xt,_currentValue2:xt,_threadCount:0};function D2(t,e,l,a,n,u,i,c){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=gl(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gl(0),this.hiddenUpdates=gl(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=c,this.incompleteTransitions=new Map}function Y0(t,e,l,a,n,u,i,c,r,b,x,R){return t=new D2(t,e,l,i,c,r,b,R),e=1,u===!0&&(e|=24),u=Re(3,null,null,e),t.current=u,u.stateNode=t,e=wc(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:a,isDehydrated:l,cache:e},Yc(u),t}function q0(t){return t?(t=sn,t):sn}function V0(t,e,l,a,n,u){n=q0(n),a.context===null?a.context=n:a.pendingContext=n,a=Il(e),a.payload={element:l},u=u===void 0?null:u,u!==null&&(a.callback=u),l=Pl(t,a,e),l!==null&&(Be(l,t,e),au(l,t,e))}function L0(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function es(t,e){L0(t,e),(t=t.alternate)&&L0(t,e)}function G0(t){if(t.tag===13){var e=fn(t,67108864);e!==null&&Be(e,t,67108864),es(t,67108864)}}var Ki=!0;function N2(t,e,l,a){var n=V.T;V.T=null;var u=F.p;try{F.p=2,ls(t,e,l,a)}finally{F.p=u,V.T=n}}function R2(t,e,l,a){var n=V.T;V.T=null;var u=F.p;try{F.p=8,ls(t,e,l,a)}finally{F.p=u,V.T=n}}function ls(t,e,l,a){if(Ki){var n=as(a);if(n===null)Xf(t,e,a,Ji,l),Q0(t,a);else if(j2(n,t,e,l,a))a.stopPropagation();else if(Q0(t,a),e&4&&-1<U2.indexOf(t)){for(;n!==null;){var u=Ve(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var i=ce(u.pendingLanes);if(i!==0){var c=u;for(c.pendingLanes|=2,c.entangledLanes|=2;i;){var r=1<<31-Se(i);c.entanglements[1]|=r,i&=~r}yl(u),(Ot&6)===0&&(Ni=le()+500,bu(0))}}break;case 13:c=fn(u,2),c!==null&&Be(c,u,2),Ui(),es(u,2)}if(u=as(a),u===null&&Xf(t,e,a,Ji,l),u===n)break;n=u}n!==null&&a.stopPropagation()}else Xf(t,e,a,null,l)}}function as(t){return t=$l(t),ns(t)}var Ji=null;function ns(t){if(Ji=null,t=pl(t),t!==null){var e=M(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=j(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Ji=t,null}function X0(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(qu()){case Vu:return 2;case Lu:return 8;case Cl:case pa:return 32;case Za:return 268435456;default:return 32}default:return 32}}var us=!1,ha=null,da=null,va=null,Mu=new Map,Du=new Map,ma=[],U2="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Q0(t,e){switch(t){case"focusin":case"focusout":ha=null;break;case"dragenter":case"dragleave":da=null;break;case"mouseover":case"mouseout":va=null;break;case"pointerover":case"pointerout":Mu.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Du.delete(e.pointerId)}}function Nu(t,e,l,a,n,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:l,eventSystemFlags:a,nativeEvent:u,targetContainers:[n]},e!==null&&(e=Ve(e),e!==null&&G0(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function j2(t,e,l,a,n){switch(e){case"focusin":return ha=Nu(ha,t,e,l,a,n),!0;case"dragenter":return da=Nu(da,t,e,l,a,n),!0;case"mouseover":return va=Nu(va,t,e,l,a,n),!0;case"pointerover":var u=n.pointerId;return Mu.set(u,Nu(Mu.get(u)||null,t,e,l,a,n)),!0;case"gotpointercapture":return u=n.pointerId,Du.set(u,Nu(Du.get(u)||null,t,e,l,a,n)),!0}return!1}function Z0(t){var e=pl(t.target);if(e!==null){var l=M(e);if(l!==null){if(e=l.tag,e===13){if(e=j(l),e!==null){t.blockedOn=e,Vl(t.priority,function(){if(l.tag===13){var a=He();a=Ka(a);var n=fn(l,a);n!==null&&Be(n,l,a),es(l,a)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function $i(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=as(t.nativeEvent);if(l===null){l=t.nativeEvent;var a=new l.constructor(l.type,l);Jl=a,l.target.dispatchEvent(a),Jl=null}else return e=Ve(l),e!==null&&G0(e),t.blockedOn=l,!1;e.shift()}return!0}function K0(t,e,l){$i(t)&&l.delete(e)}function w2(){us=!1,ha!==null&&$i(ha)&&(ha=null),da!==null&&$i(da)&&(da=null),va!==null&&$i(va)&&(va=null),Mu.forEach(K0),Du.forEach(K0)}function Wi(t,e){t.blockedOn===e&&(t.blockedOn=null,us||(us=!0,d.unstable_scheduleCallback(d.unstable_NormalPriority,w2)))}var ki=null;function J0(t){ki!==t&&(ki=t,d.unstable_scheduleCallback(d.unstable_NormalPriority,function(){ki===t&&(ki=null);for(var e=0;e<t.length;e+=3){var l=t[e],a=t[e+1],n=t[e+2];if(typeof a!="function"){if(ns(a||l)===null)continue;break}var u=Ve(l);u!==null&&(t.splice(e,3),e-=3,af(u,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function Ru(t){function e(r){return Wi(r,t)}ha!==null&&Wi(ha,t),da!==null&&Wi(da,t),va!==null&&Wi(va,t),Mu.forEach(e),Du.forEach(e);for(var l=0;l<ma.length;l++){var a=ma[l];a.blockedOn===t&&(a.blockedOn=null)}for(;0<ma.length&&(l=ma[0],l.blockedOn===null);)Z0(l),l.blockedOn===null&&ma.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],u=l[a+1],i=n[he]||null;if(typeof u=="function")i||J0(l);else if(i){var c=null;if(u&&u.hasAttribute("formAction")){if(n=u,i=u[he]||null)c=i.formAction;else if(ns(n)!==null)continue}else c=i.action;typeof c=="function"?l[a+1]=c:(l.splice(a,3),a-=3),J0(l)}}}function is(t){this._internalRoot=t}Fi.prototype.render=is.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(f(409));var l=e.current,a=He();V0(l,a,t,e,null,null)},Fi.prototype.unmount=is.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;V0(t.current,2,null,t,null,null),Ui(),e[_e]=null}};function Fi(t){this._internalRoot=t}Fi.prototype.unstable_scheduleHydration=function(t){if(t){var e=ba();t={blockedOn:null,target:t,priority:e};for(var l=0;l<ma.length&&e!==0&&e<ma[l].priority;l++);ma.splice(l,0,t),l===0&&Z0(t)}};var $0=s.version;if($0!=="19.1.1")throw Error(f(527,$0,"19.1.1"));F.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(f(188)):(t=Object.keys(t).join(","),Error(f(268,t)));return t=y(e),t=t!==null?h(t):null,t=t===null?null:t.stateNode,t};var H2={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:V,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ii=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ii.isDisabled&&Ii.supportsFiber)try{tl=Ii.inject(H2),pe=Ii}catch{}}return ju.createRoot=function(t,e){if(!_(t))throw Error(f(299));var l=!1,a="",n=so,u=ro,i=oo,c=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(i=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(c=e.unstable_transitionCallbacks)),e=Y0(t,1,!1,null,null,l,a,n,u,i,c,null),t[_e]=e.current,Gf(t),new is(e)},ju.hydrateRoot=function(t,e,l){if(!_(t))throw Error(f(299));var a=!1,n="",u=so,i=ro,c=oo,r=null,b=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(i=l.onCaughtError),l.onRecoverableError!==void 0&&(c=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(r=l.unstable_transitionCallbacks),l.formState!==void 0&&(b=l.formState)),e=Y0(t,1,!0,e,l??null,a,n,u,i,c,r,b),e.context=q0(null),l=e.current,a=He(),a=Ka(a),n=Il(a),n.callback=null,Pl(l,n,a),l=a,e.current.lanes=l,ql(e,l),yl(e),t[_e]=e.current,Gf(t),new Fi(e)},ju.version="19.1.1",ju}var nh;function lv(){if(nh)return fs.exports;nh=1;function d(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(d)}catch(s){console.error(s)}}return d(),fs.exports=ev(),fs.exports}var av=lv(),W=zs();const Hl=G2(W);function Hu(){return Hu=Object.assign?Object.assign.bind():function(d){for(var s=1;s<arguments.length;s++){var o=arguments[s];for(var f in o)({}).hasOwnProperty.call(o,f)&&(d[f]=o[f])}return d},Hu.apply(null,arguments)}var ga=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Cu(d){return d&&d.__esModule&&Object.prototype.hasOwnProperty.call(d,"default")?d.default:d}var uh,ih={exports:{}};/*!
  Copyright (c) 2018 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/var os,ch,fh,nv=(uh||(uh=1,os=ih,(function(){var d={}.hasOwnProperty;function s(){for(var o=[],f=0;f<arguments.length;f++){var _=arguments[f];if(_){var M=typeof _;if(M==="string"||M==="number")o.push(_);else if(Array.isArray(_)){if(_.length){var j=s.apply(null,_);j&&o.push(j)}}else if(M==="object")if(_.toString===Object.prototype.toString)for(var U in _)d.call(_,U)&&_[U]&&o.push(U);else o.push(_.toString())}}return o.join(" ")}os.exports?(s.default=s,os.exports=s):window.classNames=s})()),ih.exports),bs=Cu(nv),sh,ul=Cu((function(){if(fh)return ch;fh=1;var d=/^\s+|\s+$/g,s=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,f=/^0o[0-7]+$/i,_=parseInt,M=Object.prototype.toString;function j(y){var h=typeof y;return!!y&&(h=="object"||h=="function")}function U(y){if(typeof y=="number")return y;if((function(O){return typeof O=="symbol"||(function(H){return!!H&&typeof H=="object"})(O)&&M.call(O)=="[object Symbol]"})(y))return NaN;if(j(y)){var h=typeof y.valueOf=="function"?y.valueOf():y;y=j(h)?h+"":h}if(typeof y!="string")return y===0?y:+y;y=y.replace(d,"");var z=o.test(y);return z||f.test(y)?_(y.slice(2),z?2:8):s.test(y)?NaN:+y}return ch=function(y,h,z){return z===void 0&&(z=h,h=void 0),z!==void 0&&(z=(z=U(z))==z?z:0),h!==void 0&&(h=(h=U(h))==h?h:0),(function(O,H,B){return O==O&&(B!==void 0&&(O=O<=B?O:B),H!==void 0&&(O=O>=H?O:H)),O})(U(y),h,z)}})()),hs={exports:{}},uv=(sh||(sh=1,(function(d,s){var o="__lodash_hash_undefined__",f=9007199254740991,_="[object Arguments]",M="[object Array]",j="[object Boolean]",U="[object Date]",y="[object Error]",h="[object Function]",z="[object Map]",O="[object Number]",H="[object Object]",B="[object Promise]",C="[object RegExp]",Y="[object Set]",G="[object String]",X="[object Symbol]",$="[object WeakMap]",k="[object ArrayBuffer]",ut="[object DataView]",tt=/^\[object .+?Constructor\]$/,Ct=/^(?:0|[1-9]\d*)$/,w={};w["[object Float32Array]"]=w["[object Float64Array]"]=w["[object Int8Array]"]=w["[object Int16Array]"]=w["[object Int32Array]"]=w["[object Uint8Array]"]=w["[object Uint8ClampedArray]"]=w["[object Uint16Array]"]=w["[object Uint32Array]"]=!0,w[_]=w[M]=w[k]=w[j]=w[ut]=w[U]=w[y]=w[h]=w[z]=w[O]=w[H]=w[C]=w[Y]=w[G]=w[$]=!1;var st=typeof ga=="object"&&ga&&ga.Object===Object&&ga,Vt=typeof self=="object"&&self&&self.Object===Object&&self,Dt=st||Vt||Function("return this")(),oe=s&&!s.nodeType&&s,rt=oe&&d&&!d.nodeType&&d,ht=rt&&rt.exports===oe,At=ht&&st.process,ft=(function(){try{return At&&At.binding&&At.binding("util")}catch{}})(),V=ft&&ft.isTypedArray;function F(v,A){for(var q=-1,K=v==null?0:v.length;++q<K;)if(A(v[q],q,v))return!0;return!1}function xt(v){var A=-1,q=Array(v.size);return v.forEach(function(K,bt){q[++A]=[bt,K]}),q}function Oe(v){var A=-1,q=Array(v.size);return v.forEach(function(K){q[++A]=K}),q}var g,N,Q,L=Array.prototype,et=Function.prototype,yt=Object.prototype,lt=Dt["__core-js_shared__"],kt=et.toString,Z=yt.hasOwnProperty,it=(g=/[^.]+$/.exec(lt&&lt.keys&&lt.keys.IE_PROTO||""))?"Symbol(src)_1."+g:"",jt=yt.toString,Ht=RegExp("^"+kt.call(Z).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Xa=ht?Dt.Buffer:void 0,Bl=Dt.Symbol,Qa=Dt.Uint8Array,Yu=yt.propertyIsEnumerable,ac=L.splice,le=Bl?Bl.toStringTag:void 0,qu=Object.getOwnPropertySymbols,Vu=Xa?Xa.isBuffer:void 0,Lu=(N=Object.keys,Q=Object,function(v){return N(Q(v))}),Cl=Vl(Dt,"DataView"),pa=Vl(Dt,"Map"),Za=Vl(Dt,"Promise"),Cn=Vl(Dt,"Set"),Yn=Vl(Dt,"WeakMap"),tl=Vl(Object,"create"),pe=_e(Cl),il=_e(pa),Se=_e(Za),nc=_e(Cn),uc=_e(Yn),Gu=Bl?Bl.prototype:void 0,Yl=Gu?Gu.valueOf:void 0;function Ye(v){var A=-1,q=v==null?0:v.length;for(this.clear();++A<q;){var K=v[A];this.set(K[0],K[1])}}function ce(v){var A=-1,q=v==null?0:v.length;for(this.clear();++A<q;){var K=v[A];this.set(K[0],K[1])}}function qe(v){var A=-1,q=v==null?0:v.length;for(this.clear();++A<q;){var K=v[A];this.set(K[0],K[1])}}function cl(v){var A=-1,q=v==null?0:v.length;for(this.__data__=new qe;++A<q;)this.add(v[A])}function fl(v){var A=this.__data__=new ce(v);this.size=A.size}function Xu(v,A){var q=Wa(v),K=!q&&ic(v),bt=!q&&!K&&ka(v),vt=!q&&!K&&!bt&&Gl(v),qt=q||K||bt||vt,Qt=qt?(function(Gt,Ft){for(var De=-1,ae=Array(Gt);++De<Gt;)ae[De]=Ft(De);return ae})(v.length,String):[],Me=Qt.length;for(var Lt in v)!Z.call(v,Lt)||qt&&(Lt=="length"||bt&&(Lt=="offset"||Lt=="parent")||vt&&(Lt=="buffer"||Lt=="byteLength"||Lt=="byteOffset")||he(Lt,Me))||Qt.push(Lt);return Qt}function Sa(v,A){for(var q=v.length;q--;)if($a(v[q][0],A))return q;return-1}function gl(v){return v==null?v===void 0?"[object Undefined]":"[object Null]":le&&le in Object(v)?(function(A){var q=Z.call(A,le),K=A[le];try{A[le]=void 0;var bt=!0}catch{}var vt=jt.call(A);return bt&&(q?A[le]=K:delete A[le]),vt})(v):(function(A){return jt.call(A)})(v)}function ql(v){return Ve(v)&&gl(v)==_}function Qu(v,A,q,K,bt){return v===A||(v==null||A==null||!Ve(v)&&!Ve(A)?v!=v&&A!=A:(function(vt,qt,Qt,Me,Lt,Gt){var Ft=Wa(vt),De=Wa(qt),ae=Ft?M:Yt(vt),de=De?M:Yt(qt),rl=(ae=ae==_?H:ae)==H,Ql=(de=de==_?H:de)==H,Zl=ae==de;if(Zl&&ka(vt)){if(!ka(qt))return!1;Ft=!0,rl=!1}if(Zl&&!rl)return Gt||(Gt=new fl),Ft||Gl(vt)?Ka(vt,qt,Qt,Me,Lt,Gt):(function(zt,_t,Sl,ol,ve,me,Le){switch(Sl){case ut:if(zt.byteLength!=_t.byteLength||zt.byteOffset!=_t.byteOffset)return!1;zt=zt.buffer,_t=_t.buffer;case k:return!(zt.byteLength!=_t.byteLength||!me(new Qa(zt),new Qa(_t)));case j:case U:case O:return $a(+zt,+_t);case y:return zt.name==_t.name&&zt.message==_t.message;case C:case G:return zt==_t+"";case z:var Ge=xt;case Y:var Xe=1&ol;if(Ge||(Ge=Oe),zt.size!=_t.size&&!Xe)return!1;var za=Le.get(zt);if(za)return za==_t;ol|=2,Le.set(zt,_t);var Ia=Ka(Ge(zt),Ge(_t),ol,ve,me,Le);return Le.delete(zt),Ia;case X:if(Yl)return Yl.call(zt)==Yl.call(_t)}return!1})(vt,qt,ae,Qt,Me,Lt,Gt);if(!(1&Qt)){var Kl=rl&&Z.call(vt,"__wrapped__"),be=Ql&&Z.call(qt,"__wrapped__");if(Kl||be){var Ju=Kl?vt.value():vt,cc=be?qt.value():qt;return Gt||(Gt=new fl),Lt(Ju,cc,Qt,Me,Gt)}}return!!Zl&&(Gt||(Gt=new fl),(function(zt,_t,Sl,ol,ve,me){var Le=1&Sl,Ge=Ja(zt),Xe=Ge.length,za=Ja(_t),Ia=za.length;if(Xe!=Ia&&!Le)return!1;for(var el=Xe;el--;){var bl=Ge[el];if(!(Le?bl in _t:Z.call(_t,bl)))return!1}var qn=me.get(zt);if(qn&&me.get(_t))return qn==_t;var _a=!0;me.set(zt,_t),me.set(_t,zt);for(var Ea=Le;++el<Xe;){var Pa=zt[bl=Ge[el]],tn=_t[bl];if(ol)var Ta=Le?ol(tn,Pa,bl,_t,zt,me):ol(Pa,tn,bl,zt,_t,me);if(!(Ta===void 0?Pa===tn||ve(Pa,tn,Sl,ol,me):Ta)){_a=!1;break}Ea||(Ea=bl=="constructor")}if(_a&&!Ea){var Jl=zt.constructor,$l=_t.constructor;Jl==$l||!("constructor"in zt)||!("constructor"in _t)||typeof Jl=="function"&&Jl instanceof Jl&&typeof $l=="function"&&$l instanceof $l||(_a=!1)}return me.delete(zt),me.delete(_t),_a})(vt,qt,Qt,Me,Lt,Gt))})(v,A,q,K,Qu,bt))}function Zu(v){return!(!pl(v)||(function(A){return!!it&&it in A})(v))&&(Ll(v)?Ht:tt).test(_e(v))}function Ku(v){if(q=(A=v)&&A.constructor,K=typeof q=="function"&&q.prototype||yt,A!==K)return Lu(v);var A,q,K,bt=[];for(var vt in Object(v))Z.call(v,vt)&&vt!="constructor"&&bt.push(vt);return bt}function Ka(v,A,q,K,bt,vt){var qt=1&q,Qt=v.length,Me=A.length;if(Qt!=Me&&!(qt&&Me>Qt))return!1;var Lt=vt.get(v);if(Lt&&vt.get(A))return Lt==A;var Gt=-1,Ft=!0,De=2&q?new cl:void 0;for(vt.set(v,A),vt.set(A,v);++Gt<Qt;){var ae=v[Gt],de=A[Gt];if(K)var rl=qt?K(de,ae,Gt,A,v,vt):K(ae,de,Gt,v,A,vt);if(rl!==void 0){if(rl)continue;Ft=!1;break}if(De){if(!F(A,function(Ql,Zl){if(Kl=Zl,!De.has(Kl)&&(ae===Ql||bt(ae,Ql,q,K,vt)))return De.push(Zl);var Kl})){Ft=!1;break}}else if(ae!==de&&!bt(ae,de,q,K,vt)){Ft=!1;break}}return vt.delete(v),vt.delete(A),Ft}function Ja(v){return(function(A,q,K){var bt=q(A);return Wa(A)?bt:(function(vt,qt){for(var Qt=-1,Me=qt.length,Lt=vt.length;++Qt<Me;)vt[Lt+Qt]=qt[Qt];return vt})(bt,K(A))})(v,Xl,sl)}function ba(v,A){var q,K,bt=v.__data__;return((K=typeof(q=A))=="string"||K=="number"||K=="symbol"||K=="boolean"?q!=="__proto__":q===null)?bt[typeof A=="string"?"string":"hash"]:bt.map}function Vl(v,A){var q=(function(K,bt){return K?.[bt]})(v,A);return Zu(q)?q:void 0}Ye.prototype.clear=function(){this.__data__=tl?tl(null):{},this.size=0},Ye.prototype.delete=function(v){var A=this.has(v)&&delete this.__data__[v];return this.size-=A?1:0,A},Ye.prototype.get=function(v){var A=this.__data__;if(tl){var q=A[v];return q===o?void 0:q}return Z.call(A,v)?A[v]:void 0},Ye.prototype.has=function(v){var A=this.__data__;return tl?A[v]!==void 0:Z.call(A,v)},Ye.prototype.set=function(v,A){var q=this.__data__;return this.size+=this.has(v)?0:1,q[v]=tl&&A===void 0?o:A,this},ce.prototype.clear=function(){this.__data__=[],this.size=0},ce.prototype.delete=function(v){var A=this.__data__,q=Sa(A,v);return!(q<0||(q==A.length-1?A.pop():ac.call(A,q,1),--this.size,0))},ce.prototype.get=function(v){var A=this.__data__,q=Sa(A,v);return q<0?void 0:A[q][1]},ce.prototype.has=function(v){return Sa(this.__data__,v)>-1},ce.prototype.set=function(v,A){var q=this.__data__,K=Sa(q,v);return K<0?(++this.size,q.push([v,A])):q[K][1]=A,this},qe.prototype.clear=function(){this.size=0,this.__data__={hash:new Ye,map:new(pa||ce),string:new Ye}},qe.prototype.delete=function(v){var A=ba(this,v).delete(v);return this.size-=A?1:0,A},qe.prototype.get=function(v){return ba(this,v).get(v)},qe.prototype.has=function(v){return ba(this,v).has(v)},qe.prototype.set=function(v,A){var q=ba(this,v),K=q.size;return q.set(v,A),this.size+=q.size==K?0:1,this},cl.prototype.add=cl.prototype.push=function(v){return this.__data__.set(v,o),this},cl.prototype.has=function(v){return this.__data__.has(v)},fl.prototype.clear=function(){this.__data__=new ce,this.size=0},fl.prototype.delete=function(v){var A=this.__data__,q=A.delete(v);return this.size=A.size,q},fl.prototype.get=function(v){return this.__data__.get(v)},fl.prototype.has=function(v){return this.__data__.has(v)},fl.prototype.set=function(v,A){var q=this.__data__;if(q instanceof ce){var K=q.__data__;if(!pa||K.length<199)return K.push([v,A]),this.size=++q.size,this;q=this.__data__=new qe(K)}return q.set(v,A),this.size=q.size,this};var sl=qu?function(v){return v==null?[]:(v=Object(v),(function(A,q){for(var K=-1,bt=A==null?0:A.length,vt=0,qt=[];++K<bt;){var Qt=A[K];q(Qt,K,A)&&(qt[vt++]=Qt)}return qt})(qu(v),function(A){return Yu.call(v,A)}))}:function(){return[]},Yt=gl;function he(v,A){return!!(A=A??f)&&(typeof v=="number"||Ct.test(v))&&v>-1&&v%1==0&&v<A}function _e(v){if(v!=null){try{return kt.call(v)}catch{}try{return v+""}catch{}}return""}function $a(v,A){return v===A||v!=v&&A!=A}(Cl&&Yt(new Cl(new ArrayBuffer(1)))!=ut||pa&&Yt(new pa)!=z||Za&&Yt(Za.resolve())!=B||Cn&&Yt(new Cn)!=Y||Yn&&Yt(new Yn)!=$)&&(Yt=function(v){var A=gl(v),q=A==H?v.constructor:void 0,K=q?_e(q):"";if(K)switch(K){case pe:return ut;case il:return z;case Se:return B;case nc:return Y;case uc:return $}return A});var ic=ql((function(){return arguments})())?ql:function(v){return Ve(v)&&Z.call(v,"callee")&&!Yu.call(v,"callee")},Wa=Array.isArray,ka=Vu||function(){return!1};function Ll(v){if(!pl(v))return!1;var A=gl(v);return A==h||A=="[object GeneratorFunction]"||A=="[object AsyncFunction]"||A=="[object Proxy]"}function Fa(v){return typeof v=="number"&&v>-1&&v%1==0&&v<=f}function pl(v){var A=typeof v;return v!=null&&(A=="object"||A=="function")}function Ve(v){return v!=null&&typeof v=="object"}var Gl=V?(function(v){return function(A){return v(A)}})(V):function(v){return Ve(v)&&Fa(v.length)&&!!w[gl(v)]};function Xl(v){return(A=v)!=null&&Fa(A.length)&&!Ll(A)?Xu(v):Ku(v);var A}d.exports=function(v,A){return Qu(v,A)}})(hs,hs.exports)),hs.exports),iv=Cu(uv);function rh(d,s,o){return d[s]?d[s][0]?d[s][0][o]:d[s][o]:s==="contentBoxSize"?d.contentRect[o==="inlineSize"?"width":"height"]:void 0}function cv(d){d===void 0&&(d={});var s=d.onResize,o=W.useRef(void 0);o.current=s;var f=d.round||Math.round,_=W.useRef(),M=W.useState({width:void 0,height:void 0}),j=M[0],U=M[1],y=W.useRef(!1);W.useEffect(function(){return y.current=!1,function(){y.current=!0}},[]);var h=W.useRef({width:void 0,height:void 0}),z=(function(O,H){var B=W.useRef(null),C=W.useRef(null);C.current=H;var Y=W.useRef(null);W.useEffect(function(){G()});var G=W.useCallback(function(){var X=Y.current,$=C.current,k=X||($?$ instanceof Element?$:$.current:null);B.current&&B.current.element===k&&B.current.subscriber===O||(B.current&&B.current.cleanup&&B.current.cleanup(),B.current={element:k,subscriber:O,cleanup:k?O(k):void 0})},[O]);return W.useEffect(function(){return function(){B.current&&B.current.cleanup&&(B.current.cleanup(),B.current=null)}},[]),W.useCallback(function(X){Y.current=X,G()},[G])})(W.useCallback(function(O){return _.current&&_.current.box===d.box&&_.current.round===f||(_.current={box:d.box,round:f,instance:new ResizeObserver(function(H){var B=H[0],C=d.box==="border-box"?"borderBoxSize":d.box==="device-pixel-content-box"?"devicePixelContentBoxSize":"contentBoxSize",Y=rh(B,C,"inlineSize"),G=rh(B,C,"blockSize"),X=Y?f(Y):void 0,$=G?f(G):void 0;if(h.current.width!==X||h.current.height!==$){var k={width:X,height:$};h.current.width=X,h.current.height=$,o.current?o.current(k):y.current||U(k)}})}),_.current.instance.observe(O,{box:d.box}),function(){_.current&&_.current.instance.unobserve(O)}},[d.box,f]),d.ref);return W.useMemo(function(){return{ref:z,width:j.width,height:j.height}},[z,j.width,j.height])}var fv="allotment-module_splitView__L-yRc",sv="allotment-module_sashContainer__fzwJF",rv="allotment-module_splitViewContainer__rQnVa",Mh="allotment-module_splitViewView__MGZ6O",ov="allotment-module_vertical__WSwwa",hv="allotment-module_horizontal__7doS8",dv="allotment-module_separatorBorder__x-rDS";let wu,Dh=!1,Nh=!1;typeof navigator=="object"&&(wu=navigator.userAgent,Nh=wu.indexOf("Macintosh")>=0,Dh=(wu.indexOf("Macintosh")>=0||wu.indexOf("iPad")>=0||wu.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0);const Rh=Dh,vv=Nh,oh=typeof window<"u"&&window.document!==void 0&&window.document.createElement!==void 0?W.useLayoutEffect:W.useEffect;class mv{constructor(){this._size=void 0}getSize(){return this._size}setSize(s){this._size=s}}function Pi(d,s){const o=d.length,f=o-s.length;return f>=0&&d.slice(f,o)===s}var hh,dh={exports:{}},vh,mh,yv=(hh||(hh=1,(function(d){var s=Object.prototype.hasOwnProperty,o="~";function f(){}function _(y,h,z){this.fn=y,this.context=h,this.once=z||!1}function M(y,h,z,O,H){if(typeof z!="function")throw new TypeError("The listener must be a function");var B=new _(z,O||y,H),C=o?o+h:h;return y._events[C]?y._events[C].fn?y._events[C]=[y._events[C],B]:y._events[C].push(B):(y._events[C]=B,y._eventsCount++),y}function j(y,h){--y._eventsCount==0?y._events=new f:delete y._events[h]}function U(){this._events=new f,this._eventsCount=0}Object.create&&(f.prototype=Object.create(null),new f().__proto__||(o=!1)),U.prototype.eventNames=function(){var y,h,z=[];if(this._eventsCount===0)return z;for(h in y=this._events)s.call(y,h)&&z.push(o?h.slice(1):h);return Object.getOwnPropertySymbols?z.concat(Object.getOwnPropertySymbols(y)):z},U.prototype.listeners=function(y){var h=o?o+y:y,z=this._events[h];if(!z)return[];if(z.fn)return[z.fn];for(var O=0,H=z.length,B=new Array(H);O<H;O++)B[O]=z[O].fn;return B},U.prototype.listenerCount=function(y){var h=o?o+y:y,z=this._events[h];return z?z.fn?1:z.length:0},U.prototype.emit=function(y,h,z,O,H,B){var C=o?o+y:y;if(!this._events[C])return!1;var Y,G,X=this._events[C],$=arguments.length;if(X.fn){switch(X.once&&this.removeListener(y,X.fn,void 0,!0),$){case 1:return X.fn.call(X.context),!0;case 2:return X.fn.call(X.context,h),!0;case 3:return X.fn.call(X.context,h,z),!0;case 4:return X.fn.call(X.context,h,z,O),!0;case 5:return X.fn.call(X.context,h,z,O,H),!0;case 6:return X.fn.call(X.context,h,z,O,H,B),!0}for(G=1,Y=new Array($-1);G<$;G++)Y[G-1]=arguments[G];X.fn.apply(X.context,Y)}else{var k,ut=X.length;for(G=0;G<ut;G++)switch(X[G].once&&this.removeListener(y,X[G].fn,void 0,!0),$){case 1:X[G].fn.call(X[G].context);break;case 2:X[G].fn.call(X[G].context,h);break;case 3:X[G].fn.call(X[G].context,h,z);break;case 4:X[G].fn.call(X[G].context,h,z,O);break;default:if(!Y)for(k=1,Y=new Array($-1);k<$;k++)Y[k-1]=arguments[k];X[G].fn.apply(X[G].context,Y)}}return!0},U.prototype.on=function(y,h,z){return M(this,y,h,z,!1)},U.prototype.once=function(y,h,z){return M(this,y,h,z,!0)},U.prototype.removeListener=function(y,h,z,O){var H=o?o+y:y;if(!this._events[H])return this;if(!h)return j(this,H),this;var B=this._events[H];if(B.fn)B.fn!==h||O&&!B.once||z&&B.context!==z||j(this,H);else{for(var C=0,Y=[],G=B.length;C<G;C++)(B[C].fn!==h||O&&!B[C].once||z&&B[C].context!==z)&&Y.push(B[C]);Y.length?this._events[H]=Y.length===1?Y[0]:Y:j(this,H)}return this},U.prototype.removeAllListeners=function(y){var h;return y?(h=o?o+y:y,this._events[h]&&j(this,h)):(this._events=new f,this._eventsCount=0),this},U.prototype.off=U.prototype.removeListener,U.prototype.addListener=U.prototype.on,U.prefixed=o,U.EventEmitter=U,d.exports=U})(dh)),dh.exports),_s=Cu(yv);function yh(d,s){const o=d.indexOf(s);o>-1&&(d.splice(o,1),d.unshift(s))}function ds(d,s){const o=d.indexOf(s);o>-1&&(d.splice(o,1),d.push(s))}function Fe(d,s,o=1){const f=Math.max(0,Math.ceil((s-d)/o)),_=new Array(f);let M=-1;for(;++M<f;)_[M]=d+M*o;return _}var gv=Cu((function(){if(mh)return vh;mh=1;var d=/^\s+|\s+$/g,s=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,f=/^0o[0-7]+$/i,_=parseInt,M=typeof ga=="object"&&ga&&ga.Object===Object&&ga,j=typeof self=="object"&&self&&self.Object===Object&&self,U=M||j||Function("return this")(),y=Object.prototype.toString,h=Math.max,z=Math.min,O=function(){return U.Date.now()};function H(C){var Y=typeof C;return!!C&&(Y=="object"||Y=="function")}function B(C){if(typeof C=="number")return C;if((function(X){return typeof X=="symbol"||(function($){return!!$&&typeof $=="object"})(X)&&y.call(X)=="[object Symbol]"})(C))return NaN;if(H(C)){var Y=typeof C.valueOf=="function"?C.valueOf():C;C=H(Y)?Y+"":Y}if(typeof C!="string")return C===0?C:+C;C=C.replace(d,"");var G=o.test(C);return G||f.test(C)?_(C.slice(2),G?2:8):s.test(C)?NaN:+C}return vh=function(C,Y,G){var X,$,k,ut,tt,Ct,w=0,st=!1,Vt=!1,Dt=!0;if(typeof C!="function")throw new TypeError("Expected a function");function oe(V){var F=X,xt=$;return X=$=void 0,w=V,ut=C.apply(xt,F)}function rt(V){var F=V-Ct;return Ct===void 0||F>=Y||F<0||Vt&&V-w>=k}function ht(){var V=O();if(rt(V))return At(V);tt=setTimeout(ht,(function(F){var xt=Y-(F-Ct);return Vt?z(xt,k-(F-w)):xt})(V))}function At(V){return tt=void 0,Dt&&X?oe(V):(X=$=void 0,ut)}function ft(){var V=O(),F=rt(V);if(X=arguments,$=this,Ct=V,F){if(tt===void 0)return(function(xt){return w=xt,tt=setTimeout(ht,Y),st?oe(xt):ut})(Ct);if(Vt)return tt=setTimeout(ht,Y),oe(Ct)}return tt===void 0&&(tt=setTimeout(ht,Y)),ut}return Y=B(Y)||0,H(G)&&(st=!!G.leading,k=(Vt="maxWait"in G)?h(B(G.maxWait)||0,Y):k,Dt="trailing"in G?!!G.trailing:Dt),ft.cancel=function(){tt!==void 0&&clearTimeout(tt),w=0,X=Ct=$=tt=void 0},ft.flush=function(){return tt===void 0?ut:At(O())},ft}})()),pv="sash-module_sash__K-9lB",Sv="sash-module_disabled__Hm-wx",bv="sash-module_mac__Jf6OJ",gh="sash-module_vertical__pB-rs",zv="sash-module_minimum__-UKxp",_v="sash-module_maximum__TCWxD",ph="sash-module_horizontal__kFbiw",vs="sash-module_hover__80W6I",ms="sash-module_active__bJspD";let Pe=(function(d){return d.Vertical="VERTICAL",d.Horizontal="HORIZONTAL",d})({}),Ce=(function(d){return d.Disabled="DISABLED",d.Minimum="MINIMUM",d.Maximum="MAXIMUM",d.Enabled="ENABLED",d})({}),Uh=Rh?20:8;const jh=new _s;class Sh extends _s{get state(){return this._state}set state(s){this._state!==s&&(this.el.classList.toggle(Sv,s===Ce.Disabled),this.el.classList.toggle("sash-disabled",s===Ce.Disabled),this.el.classList.toggle(zv,s===Ce.Minimum),this.el.classList.toggle("sash-minimum",s===Ce.Minimum),this.el.classList.toggle(_v,s===Ce.Maximum),this.el.classList.toggle("sash-maximum",s===Ce.Maximum),this._state=s,this.emit("enablementChange",s))}constructor(s,o,f){var _;super(),this.el=void 0,this.layoutProvider=void 0,this.orientation=void 0,this.size=void 0,this.hoverDelay=300,this.hoverDelayer=gv(M=>M.classList.add("sash-hover",vs),this.hoverDelay),this._state=Ce.Enabled,this.onPointerStart=M=>{const j=M.pageX,U=M.pageY,y={startX:j,currentX:j,startY:U,currentY:U};this.el.classList.add("sash-active",ms),this.emit("start",y),this.el.setPointerCapture(M.pointerId);const h=O=>{O.preventDefault();const H={startX:j,currentX:O.pageX,startY:U,currentY:O.pageY};this.emit("change",H)},z=O=>{O.preventDefault(),this.el.classList.remove("sash-active",ms),this.hoverDelayer.cancel(),this.emit("end"),this.el.releasePointerCapture(O.pointerId),window.removeEventListener("pointermove",h),window.removeEventListener("pointerup",z)};window.addEventListener("pointermove",h),window.addEventListener("pointerup",z)},this.onPointerDoublePress=()=>{this.emit("reset")},this.onMouseEnter=()=>{this.el.classList.contains(ms)?(this.hoverDelayer.cancel(),this.el.classList.add("sash-hover",vs)):this.hoverDelayer(this.el)},this.onMouseLeave=()=>{this.hoverDelayer.cancel(),this.el.classList.remove("sash-hover",vs)},this.el=document.createElement("div"),this.el.classList.add("sash",pv),this.el.dataset.testid="sash",s.append(this.el),vv&&this.el.classList.add("sash-mac",bv),this.el.addEventListener("pointerdown",this.onPointerStart),this.el.addEventListener("dblclick",this.onPointerDoublePress),this.el.addEventListener("mouseenter",this.onMouseEnter),this.el.addEventListener("mouseleave",this.onMouseLeave),typeof f.size=="number"?(this.size=f.size,f.orientation===Pe.Vertical?this.el.style.width=`${this.size}px`:this.el.style.height=`${this.size}px`):(this.size=Uh,jh.on("onDidChangeGlobalSize",M=>{this.size=M,this.layout()})),this.layoutProvider=o,this.orientation=(_=f.orientation)!=null?_:Pe.Vertical,this.orientation===Pe.Horizontal?(this.el.classList.add("sash-horizontal",ph),this.el.classList.remove("sash-vertical",gh)):(this.el.classList.remove("sash-horizontal",ph),this.el.classList.add("sash-vertical",gh)),this.layout()}layout(){if(this.orientation===Pe.Vertical){const s=this.layoutProvider;this.el.style.left=s.getVerticalSashLeft(this)-this.size/2+"px",s.getVerticalSashTop&&(this.el.style.top=s.getVerticalSashTop(this)+"px"),s.getVerticalSashHeight&&(this.el.style.height=s.getVerticalSashHeight(this)+"px")}else{const s=this.layoutProvider;this.el.style.top=s.getHorizontalSashTop(this)-this.size/2+"px",s.getHorizontalSashLeft&&(this.el.style.left=s.getHorizontalSashLeft(this)+"px"),s.getHorizontalSashWidth&&(this.el.style.width=s.getHorizontalSashWidth(this)+"px")}}dispose(){this.el.removeEventListener("pointerdown",this.onPointerStart),this.el.removeEventListener("dblclick",this.onPointerDoublePress),this.el.removeEventListener("mouseenter",this.onMouseEnter),this.el.removeEventListener("mouseleave",()=>this.onMouseLeave),this.el.remove()}}let lc;var ys;(ys=lc||(lc={})).Distribute={type:"distribute"},ys.Split=function(d){return{type:"split",index:d}},ys.Invisible=function(d){return{type:"invisible",cachedVisibleSize:d}};let Ie=(function(d){return d.Normal="NORMAL",d.Low="LOW",d.High="HIGH",d})({});class wh{constructor(s,o,f){this.container=void 0,this.view=void 0,this._size=void 0,this._cachedVisibleSize=void 0,this.container=s,this.view=o,this.container.classList.add("split-view-view",Mh),this.container.dataset.testid="split-view-view",typeof f=="number"?(this._size=f,this._cachedVisibleSize=void 0,s.classList.add("split-view-view-visible")):(this._size=0,this._cachedVisibleSize=f.cachedVisibleSize)}set size(s){this._size=s}get size(){return this._size}get priority(){return this.view.priority}get snap(){return!!this.view.snap}get cachedVisibleSize(){return this._cachedVisibleSize}get visible(){return this._cachedVisibleSize===void 0}setVisible(s,o){s!==this.visible&&(s?(this.size=ul(this._cachedVisibleSize,this.viewMinimumSize,this.viewMaximumSize),this._cachedVisibleSize=void 0):(this._cachedVisibleSize=typeof o=="number"?o:this.size,this.size=0),this.container.classList.toggle("split-view-view-visible",s),this.view.setVisible&&this.view.setVisible(s))}get minimumSize(){return this.visible?this.view.minimumSize:0}get viewMinimumSize(){return this.view.minimumSize}get maximumSize(){return this.visible?this.view.maximumSize:0}get viewMaximumSize(){return this.view.maximumSize}set enabled(s){this.container.style.pointerEvents=s?"":"none"}layout(s){this.layoutContainer(s),this.view.layout(this.size,s)}}class Ev extends wh{layoutContainer(s){this.container.style.left=`${s}px`,this.container.style.width=`${this.size}px`}}class Tv extends wh{layoutContainer(s){this.container.style.top=`${s}px`,this.container.style.height=`${this.size}px`}}class Av extends _s{get startSnappingEnabled(){return this._startSnappingEnabled}set startSnappingEnabled(s){this._startSnappingEnabled!==s&&(this._startSnappingEnabled=s,this.updateSashEnablement())}get endSnappingEnabled(){return this._endSnappingEnabled}set endSnappingEnabled(s){this._endSnappingEnabled!==s&&(this._endSnappingEnabled=s,this.updateSashEnablement())}constructor(s,o={},f,_,M){var j,U;if(super(),this.onDidChange=void 0,this.onDidDragStart=void 0,this.onDidDragEnd=void 0,this.orientation=void 0,this.sashContainer=void 0,this.size=0,this.contentSize=0,this.proportions=void 0,this.viewItems=[],this.sashItems=[],this.sashDragState=void 0,this.proportionalLayout=void 0,this.getSashOrthogonalSize=void 0,this._startSnappingEnabled=!0,this._endSnappingEnabled=!0,this.onSashEnd=y=>{this.emit("sashchange",y),this.saveProportions();for(const h of this.viewItems)h.enabled=!0},this.orientation=(j=o.orientation)!=null?j:Pe.Vertical,this.proportionalLayout=(U=o.proportionalLayout)!=null?U:!0,this.getSashOrthogonalSize=o.getSashOrthogonalSize,f&&(this.onDidChange=f),_&&(this.onDidDragStart=_),M&&(this.onDidDragEnd=M),this.sashContainer=document.createElement("div"),this.sashContainer.classList.add("sash-container",sv),s.prepend(this.sashContainer),o.descriptor){this.size=o.descriptor.size;for(const[y,h]of o.descriptor.views.entries()){const z=h.size,O=h.container,H=h.view;this.addView(O,H,z,y,!0)}this.contentSize=this.viewItems.reduce((y,h)=>y+h.size,0),this.saveProportions()}}addView(s,o,f,_=this.viewItems.length,M){let j;j=typeof f=="number"?f:f.type==="split"?this.getViewSize(f.index)/2:f.type==="invisible"?{cachedVisibleSize:f.cachedVisibleSize}:o.minimumSize;const U=this.orientation===Pe.Vertical?new Tv(s,o,j):new Ev(s,o,j);if(this.viewItems.splice(_,0,U),this.viewItems.length>1){const y=this.orientation===Pe.Vertical?new Sh(this.sashContainer,{getHorizontalSashTop:O=>this.getSashPosition(O),getHorizontalSashWidth:this.getSashOrthogonalSize},{orientation:Pe.Horizontal}):new Sh(this.sashContainer,{getVerticalSashLeft:O=>this.getSashPosition(O),getVerticalSashHeight:this.getSashOrthogonalSize},{orientation:Pe.Vertical}),h=this.orientation===Pe.Vertical?O=>({sash:y,start:O.startY,current:O.currentY}):O=>({sash:y,start:O.startX,current:O.currentX});y.on("start",O=>{var H;this.emit("sashDragStart"),this.onSashStart(h(O));const B=this.viewItems.map(C=>C.size);(H=this.onDidDragStart)==null||H.call(this,B)}),y.on("change",O=>this.onSashChange(h(O))),y.on("end",()=>{var O;this.emit("sashDragEnd"),this.onSashEnd(this.sashItems.findIndex(B=>B.sash===y));const H=this.viewItems.map(B=>B.size);(O=this.onDidDragEnd)==null||O.call(this,H)}),y.on("reset",()=>{const O=this.sashItems.findIndex(G=>G.sash===y),H=Fe(O,-1,-1),B=Fe(O+1,this.viewItems.length),C=this.findFirstSnapIndex(H),Y=this.findFirstSnapIndex(B);(typeof C!="number"||this.viewItems[C].visible)&&(typeof Y!="number"||this.viewItems[Y].visible)&&this.emit("sashreset",O)});const z={sash:y};this.sashItems.splice(_-1,0,z)}M||this.relayout(),M||typeof f=="number"||f.type!=="distribute"||this.distributeViewSizes()}removeView(s,o){if(s<0||s>=this.viewItems.length)throw new Error("Index out of bounds");const f=this.viewItems.splice(s,1)[0].view;if(this.viewItems.length>=1){const _=Math.max(s-1,0);this.sashItems.splice(_,1)[0].sash.dispose()}return this.relayout(),o&&o.type==="distribute"&&this.distributeViewSizes(),f}moveView(s,o,f){const _=this.getViewCachedVisibleSize(o),M=_===void 0?this.getViewSize(o):lc.Invisible(_),j=this.removeView(o);this.addView(s,j,M,f)}getViewCachedVisibleSize(s){if(s<0||s>=this.viewItems.length)throw new Error("Index out of bounds");return this.viewItems[s].cachedVisibleSize}layout(s=this.size){const o=Math.max(this.size,this.contentSize);if(this.size=s,this.proportions)for(let f=0;f<this.viewItems.length;f++){const _=this.viewItems[f];_.size=ul(Math.round(this.proportions[f]*s),_.minimumSize,_.maximumSize)}else{const f=Fe(0,this.viewItems.length),_=f.filter(j=>this.viewItems[j].priority===Ie.Low),M=f.filter(j=>this.viewItems[j].priority===Ie.High);this.resize(this.viewItems.length-1,s-o,void 0,_,M)}this.distributeEmptySpace(),this.layoutViews()}resizeView(s,o){if(s<0||s>=this.viewItems.length)return;const f=Fe(0,this.viewItems.length).filter(U=>U!==s),_=[...f.filter(U=>this.viewItems[U].priority===Ie.Low),s],M=f.filter(U=>this.viewItems[U].priority===Ie.High),j=this.viewItems[s];o=Math.round(o),o=ul(o,j.minimumSize,Math.min(j.maximumSize,this.size)),j.size=o,this.relayout(_,M)}resizeViews(s){for(let o=0;o<s.length;o++){const f=this.viewItems[o];let _=s[o];_=Math.round(_),_=ul(_,f.minimumSize,Math.min(f.maximumSize,this.size)),f.size=_}this.contentSize=this.viewItems.reduce((o,f)=>o+f.size,0),this.saveProportions(),this.layout(this.size)}getViewSize(s){return s<0||s>=this.viewItems.length?-1:this.viewItems[s].size}isViewVisible(s){if(s<0||s>=this.viewItems.length)throw new Error("Index out of bounds");return this.viewItems[s].visible}setViewVisible(s,o){if(s<0||s>=this.viewItems.length)throw new Error("Index out of bounds");this.viewItems[s].setVisible(o),this.distributeEmptySpace(s),this.layoutViews(),this.saveProportions()}distributeViewSizes(){const s=[];let o=0;for(const U of this.viewItems)U.maximumSize-U.minimumSize>0&&(s.push(U),o+=U.size);const f=Math.floor(o/s.length);for(const U of s)U.size=ul(f,U.minimumSize,U.maximumSize);const _=Fe(0,this.viewItems.length),M=_.filter(U=>this.viewItems[U].priority===Ie.Low),j=_.filter(U=>this.viewItems[U].priority===Ie.High);this.relayout(M,j)}dispose(){this.sashItems.forEach(s=>s.sash.dispose()),this.sashItems=[],this.sashContainer.remove()}relayout(s,o){const f=this.viewItems.reduce((_,M)=>_+M.size,0);this.resize(this.viewItems.length-1,this.size-f,void 0,s,o),this.distributeEmptySpace(),this.layoutViews(),this.saveProportions()}onSashStart({sash:s,start:o}){const f=this.sashItems.findIndex(_=>_.sash===s);(_=>{const M=this.viewItems.map($=>$.size);let j,U,y=Number.NEGATIVE_INFINITY,h=Number.POSITIVE_INFINITY;const z=Fe(f,-1,-1),O=Fe(f+1,this.viewItems.length),H=z.reduce(($,k)=>$+(this.viewItems[k].minimumSize-M[k]),0),B=z.reduce(($,k)=>$+(this.viewItems[k].viewMaximumSize-M[k]),0),C=O.length===0?Number.POSITIVE_INFINITY:O.reduce(($,k)=>$+(M[k]-this.viewItems[k].minimumSize),0),Y=O.length===0?Number.NEGATIVE_INFINITY:O.reduce(($,k)=>$+(M[k]-this.viewItems[k].viewMaximumSize),0);y=Math.max(H,Y),h=Math.min(C,B);const G=this.findFirstSnapIndex(z),X=this.findFirstSnapIndex(O);if(typeof G=="number"){const $=this.viewItems[G],k=Math.floor($.viewMinimumSize/2);j={index:G,limitDelta:$.visible?y-k:y+k,size:$.size}}if(typeof X=="number"){const $=this.viewItems[X],k=Math.floor($.viewMinimumSize/2);U={index:X,limitDelta:$.visible?h+k:h-k,size:$.size}}this.sashDragState={start:_,current:_,index:f,sizes:M,minDelta:y,maxDelta:h,snapBefore:j,snapAfter:U}})(o)}onSashChange({current:s}){const{index:o,start:f,sizes:_,minDelta:M,maxDelta:j,snapBefore:U,snapAfter:y}=this.sashDragState;this.sashDragState.current=s;const h=s-f;this.resize(o,h,_,void 0,void 0,M,j,U,y),this.distributeEmptySpace(),this.layoutViews()}getSashPosition(s){let o=0;for(let f=0;f<this.sashItems.length;f++)if(o+=this.viewItems[f].size,this.sashItems[f].sash===s)return o;return 0}resize(s,o,f=this.viewItems.map(z=>z.size),_,M,j=Number.NEGATIVE_INFINITY,U=Number.POSITIVE_INFINITY,y,h){if(s<0||s>=this.viewItems.length)return 0;const z=Fe(s,-1,-1),O=Fe(s+1,this.viewItems.length);if(M)for(const w of M)yh(z,w),yh(O,w);if(_)for(const w of _)ds(z,w),ds(O,w);const H=z.map(w=>this.viewItems[w]),B=z.map(w=>f[w]),C=O.map(w=>this.viewItems[w]),Y=O.map(w=>f[w]),G=z.reduce((w,st)=>w+(this.viewItems[st].minimumSize-f[st]),0),X=z.reduce((w,st)=>w+(this.viewItems[st].maximumSize-f[st]),0),$=O.length===0?Number.POSITIVE_INFINITY:O.reduce((w,st)=>w+(f[st]-this.viewItems[st].minimumSize),0),k=O.length===0?Number.NEGATIVE_INFINITY:O.reduce((w,st)=>w+(f[st]-this.viewItems[st].maximumSize),0),ut=Math.max(G,k,j),tt=Math.min($,X,U);let Ct=!1;if(y){const w=this.viewItems[y.index],st=o>=y.limitDelta;Ct=st!==w.visible,w.setVisible(st,y.size)}if(!Ct&&h){const w=this.viewItems[h.index],st=o<h.limitDelta;Ct=st!==w.visible,w.setVisible(st,h.size)}if(Ct)return this.resize(s,o,f,_,M,j,U);for(let w=0,st=o=ul(o,ut,tt);w<H.length;w++){const Vt=H[w],Dt=ul(B[w]+st,Vt.minimumSize,Vt.maximumSize);st-=Dt-B[w],Vt.size=Dt}for(let w=0,st=o;w<C.length;w++){const Vt=C[w],Dt=ul(Y[w]-st,Vt.minimumSize,Vt.maximumSize);st+=Dt-Y[w],Vt.size=Dt}return o}distributeEmptySpace(s){const o=this.viewItems.reduce((h,z)=>h+z.size,0);let f=this.size-o;const _=Fe(0,this.viewItems.length),M=[],j=_.filter(h=>this.viewItems[h].priority===Ie.Low),U=_.filter(h=>this.viewItems[h].priority===Ie.Normal),y=_.filter(h=>this.viewItems[h].priority===Ie.High);M.push(...y,...U,...j),typeof s=="number"&&ds(M,s);for(let h=0;f!==0&&h<M.length;h++){const z=this.viewItems[M[h]],O=ul(z.size+f,z.minimumSize,z.maximumSize);f-=O-z.size,z.size=O}}layoutViews(){var s;this.contentSize=this.viewItems.reduce((f,_)=>f+_.size,0);let o=0;for(const f of this.viewItems)f.layout(o),o+=f.size;(s=this.onDidChange)!=null&&s.call(this,this.viewItems.map(f=>f.size)),this.sashItems.forEach(f=>f.sash.layout()),this.updateSashEnablement()}saveProportions(){this.proportionalLayout&&this.contentSize>0&&(this.proportions=this.viewItems.map(s=>s.size/this.contentSize))}updateSashEnablement(){let s=!1;const o=this.viewItems.map(y=>s=y.size-y.minimumSize>0||s);s=!1;const f=this.viewItems.map(y=>s=y.maximumSize-y.size>0||s),_=[...this.viewItems].reverse();s=!1;const M=_.map(y=>s=y.size-y.minimumSize>0||s).reverse();s=!1;const j=_.map(y=>s=y.maximumSize-y.size>0||s).reverse();let U=0;for(let y=0;y<this.sashItems.length;y++){const{sash:h}=this.sashItems[y];U+=this.viewItems[y].size;const z=!(o[y]&&j[y+1]),O=!(f[y]&&M[y+1]);if(z&&O){const H=Fe(y,-1,-1),B=Fe(y+1,this.viewItems.length),C=this.findFirstSnapIndex(H),Y=this.findFirstSnapIndex(B),G=typeof C=="number"&&!this.viewItems[C].visible,X=typeof Y=="number"&&!this.viewItems[Y].visible;G&&M[y]&&(U>0||this.startSnappingEnabled)?h.state=Ce.Minimum:X&&o[y]&&(U<this.contentSize||this.endSnappingEnabled)?h.state=Ce.Maximum:h.state=Ce.Disabled}else h.state=z&&!O?Ce.Minimum:!z&&O?Ce.Maximum:Ce.Enabled}}findFirstSnapIndex(s){for(const o of s){const f=this.viewItems[o];if(f.visible&&f.snap)return o}for(const o of s){const f=this.viewItems[o];if(f.visible&&f.maximumSize-f.minimumSize>0)return;if(!f.visible&&f.snap)return o}}}class jn{constructor(s){this.size=void 0,this.size=s}getPreferredSize(){return this.size}}class bh{constructor(s,o){this.proportion=void 0,this.layoutService=void 0,this.proportion=s,this.layoutService=o}getPreferredSize(){return this.proportion*this.layoutService.getSize()}}class tc{getPreferredSize(){}}class zh{get preferredSize(){return this.layoutStrategy.getPreferredSize()}set preferredSize(s){if(typeof s=="number")this.layoutStrategy=new jn(s);else if(typeof s=="string"){const o=s.trim();if(Pi(o,"%")){const f=Number(o.slice(0,-1))/100;this.layoutStrategy=new bh(f,this.layoutService)}else if(Pi(o,"px")){const f=Number(o.slice(0,-2))/100;this.layoutStrategy=new jn(f)}else if(typeof Number.parseFloat(o)=="number"){const f=Number.parseFloat(o);this.layoutStrategy=new jn(f)}else this.layoutStrategy=new tc}else this.layoutStrategy=new tc}constructor(s,o){var f;if(this.minimumSize=0,this.maximumSize=Number.POSITIVE_INFINITY,this.element=void 0,this.priority=void 0,this.snap=void 0,this.layoutService=void 0,this.layoutStrategy=void 0,this.layoutService=s,this.element=o.element,this.minimumSize=typeof o.minimumSize=="number"?o.minimumSize:30,this.maximumSize=typeof o.maximumSize=="number"?o.maximumSize:Number.POSITIVE_INFINITY,typeof o.preferredSize=="number")this.layoutStrategy=new jn(o.preferredSize);else if(typeof o.preferredSize=="string"){const _=o.preferredSize.trim();if(Pi(_,"%")){const M=Number(_.slice(0,-1))/100;this.layoutStrategy=new bh(M,this.layoutService)}else if(Pi(_,"px")){const M=Number(_.slice(0,-2));this.layoutStrategy=new jn(M)}else if(typeof Number.parseFloat(_)=="number"){const M=Number.parseFloat(_);this.layoutStrategy=new jn(M)}else this.layoutStrategy=new tc}else this.layoutStrategy=new tc;this.priority=(f=o.priority)!=null?f:Ie.Normal,this.snap=typeof o.snap=="boolean"&&o.snap}layout(s){}}function _h(d){return d.minSize!==void 0||d.maxSize!==void 0||d.preferredSize!==void 0||d.priority!==void 0||d.visible!==void 0}const Es=W.forwardRef(({className:d,children:s},o)=>Hl.createElement("div",{ref:o,className:bs("split-view-view",Mh,d)},s));Es.displayName="Allotment.Pane";const Hh=W.forwardRef(({children:d,className:s,id:o,maxSize:f=1/0,minSize:_=30,proportionalLayout:M=!0,separator:j=!0,sizes:U,defaultSizes:y=U,snap:h=!1,vertical:z=!1,onChange:O,onReset:H,onVisibleChange:B,onDragStart:C,onDragEnd:Y},G)=>{const X=W.useRef(null),$=W.useRef([]),k=W.useRef(new Map),ut=W.useRef(null),tt=W.useRef(new Map),Ct=W.useRef(new mv),w=W.useRef([]),[st,Vt]=W.useState(!1),Dt=W.useMemo(()=>Hl.Children.toArray(d).filter(Hl.isValidElement),[d]),oe=W.useCallback(rt=>{var ht,At;const ft=(ht=w.current)==null?void 0:ht[rt];return typeof ft?.preferredSize=="number"&&((At=ut.current)!=null&&At.resizeView(rt,Math.round(ft.preferredSize)),!0)},[]);return W.useImperativeHandle(G,()=>({reset:()=>{if(H)H();else{var rt;(rt=ut.current)==null||rt.distributeViewSizes();for(let ht=0;ht<w.current.length;ht++)oe(ht)}},resize:rt=>{var ht;(ht=ut.current)==null||ht.resizeViews(rt)}})),oh(()=>{let rt=!0;y&&tt.current.size!==y.length&&(rt=!1,console.warn(`Expected ${y.length} children based on defaultSizes but found ${tt.current.size}`)),rt&&y&&($.current=Dt.map(ft=>ft.key));const ht=Hu({orientation:z?Pe.Vertical:Pe.Horizontal,proportionalLayout:M},rt&&y&&{descriptor:{size:y.reduce((ft,V)=>ft+V,0),views:y.map((ft,V)=>{var F,xt,Oe,g;const N=k.current.get($.current[V]),Q=new zh(Ct.current,Hu({element:document.createElement("div"),minimumSize:(F=N?.minSize)!=null?F:_,maximumSize:(xt=N?.maxSize)!=null?xt:f,priority:(Oe=N?.priority)!=null?Oe:Ie.Normal},N?.preferredSize&&{preferredSize:N?.preferredSize},{snap:(g=N?.snap)!=null?g:h}));return w.current.push(Q),{container:[...tt.current.values()][V],size:ft,view:Q}})}});ut.current=new Av(X.current,ht,O,C,Y),ut.current.on("sashDragStart",()=>{var ft;(ft=X.current)==null||ft.classList.add("split-view-sash-dragging")}),ut.current.on("sashDragEnd",()=>{var ft;(ft=X.current)==null||ft.classList.remove("split-view-sash-dragging")}),ut.current.on("sashchange",ft=>{if(B&&ut.current){const V=Dt.map(F=>F.key);for(let F=0;F<V.length;F++){const xt=k.current.get(V[F]);xt?.visible!==void 0&&xt.visible!==ut.current.isViewVisible(F)&&B(F,ut.current.isViewVisible(F))}}}),ut.current.on("sashreset",ft=>{if(H)H();else{var V;if(oe(ft)||oe(ft+1))return;(V=ut.current)==null||V.distributeViewSizes()}});const At=ut.current;return()=>{At.dispose()}},[]),oh(()=>{if(st){const L=Dt.map(Z=>Z.key),et=[...$.current],yt=L.filter(Z=>!$.current.includes(Z)),lt=L.filter(Z=>$.current.includes(Z)),kt=$.current.map(Z=>!L.includes(Z));for(let Z=kt.length-1;Z>=0;Z--){var rt;kt[Z]&&((rt=ut.current)!=null&&rt.removeView(Z),et.splice(Z,1),w.current.splice(Z,1))}for(const Z of yt){var ht,At,ft,V,F;const it=k.current.get(Z),jt=new zh(Ct.current,Hu({element:document.createElement("div"),minimumSize:(ht=it?.minSize)!=null?ht:_,maximumSize:(At=it?.maxSize)!=null?At:f,priority:(ft=it?.priority)!=null?ft:Ie.Normal},it?.preferredSize&&{preferredSize:it?.preferredSize},{snap:(V=it?.snap)!=null?V:h}));(F=ut.current)!=null&&F.addView(tt.current.get(Z),jt,lc.Distribute,L.findIndex(Ht=>Ht===Z)),et.splice(L.findIndex(Ht=>Ht===Z),0,Z),w.current.splice(L.findIndex(Ht=>Ht===Z),0,jt)}for(;!iv(L,et);)for(const[Z,it]of L.entries()){const jt=et.findIndex(Ht=>Ht===it);if(jt!==Z){var xt;(xt=ut.current)==null||xt.moveView(tt.current.get(it),jt,Z);const Ht=et[jt];et.splice(jt,1),et.splice(Z,0,Ht);break}}for(const Z of yt){var Oe;const it=L.findIndex(Ht=>Ht===Z),jt=w.current[it].preferredSize;jt!==void 0&&((Oe=ut.current)==null||Oe.resizeView(it,jt))}for(const Z of[...yt,...lt]){var g,N;const it=k.current.get(Z),jt=L.findIndex(Ht=>Ht===Z);it&&_h(it)&&it.visible!==void 0&&((g=ut.current)==null?void 0:g.isViewVisible(jt))!==it.visible&&((N=ut.current)==null||N.setViewVisible(jt,it.visible))}for(const Z of lt){const it=k.current.get(Z),jt=L.findIndex(Ht=>Ht===Z);if(it&&_h(it)){var Q;it.preferredSize!==void 0&&w.current[jt].preferredSize!==it.preferredSize&&(w.current[jt].preferredSize=it.preferredSize);let Ht=!1;it.minSize!==void 0&&w.current[jt].minimumSize!==it.minSize&&(w.current[jt].minimumSize=it.minSize,Ht=!0),it.maxSize!==void 0&&w.current[jt].maximumSize!==it.maxSize&&(w.current[jt].maximumSize=it.maxSize,Ht=!0),Ht&&((Q=ut.current)==null||Q.layout())}}(yt.length>0||kt.length>0)&&($.current=L)}},[Dt,st,f,_,h]),W.useEffect(()=>{ut.current&&(ut.current.onDidChange=O)},[O]),W.useEffect(()=>{ut.current&&(ut.current.onDidDragStart=C)},[C]),W.useEffect(()=>{ut.current&&(ut.current.onDidDragEnd=Y)},[Y]),cv({ref:X,onResize:({width:rt,height:ht})=>{var At;rt&&ht&&((At=ut.current)!=null&&At.layout(z?ht:rt),Ct.current.setSize(z?ht:rt),Vt(!0))}}),W.useEffect(()=>{Rh&&xv(20)},[]),Hl.createElement("div",{ref:X,className:bs("split-view",z?"split-view-vertical":"split-view-horizontal",{"split-view-separator-border":j},fv,z?ov:hv,{[dv]:j},s),id:o},Hl.createElement("div",{className:bs("split-view-container",rv)},Hl.Children.toArray(d).map(rt=>{if(!Hl.isValidElement(rt))return null;const ht=rt.key;return rt.type.displayName==="Allotment.Pane"?(k.current.set(ht,rt.props),Hl.cloneElement(rt,{key:ht,ref:At=>{const ft=rt.ref;ft&&(ft.current=At),At?tt.current.set(ht,At):tt.current.delete(ht)}})):Hl.createElement(Es,{key:ht,ref:At=>{At?tt.current.set(ht,At):tt.current.delete(ht)}},rt)})))});function xv(d){const s=ul(d,4,20),o=ul(d,1,8);document.documentElement.style.setProperty("--sash-size",s+"px"),document.documentElement.style.setProperty("--sash-hover-size",o+"px"),(function(f){Uh=f,jh.emit("onDidChangeGlobalSize",f)})(s)}Hh.displayName="Allotment";var gs=Object.assign(Hh,{Pane:Es});const Bu=d=>{const s=d.split(".").pop()||"";return["js","jsx"].includes(s)?"javascript":["ts","tsx"].includes(s)?"typescript":["json"].includes(s)?"json":["css"].includes(s)?"css":"javascript"};function Ov(d){try{const s=xh(d),o=K2(s,{level:9}),f=Oh(o,!0);return btoa(f)}catch(s){throw console.error("压缩失败:",s),new Error("数据压缩失败")}}function Mv(d){try{if(!d||typeof d!="string")throw new Error("无效的 base64 字符串");const s=atob(d),o=xh(s,!0);if(o.length===0)throw new Error("解压缩数据为空");const f=Z2(o);return Oh(f)}catch(s){throw console.error("解压缩失败:",s),new Error("数据解压缩失败: "+(s instanceof Error?s.message:"未知错误"))}}async function Dv(d){const s=new J2;Object.keys(d).forEach(f=>{s.file(f,d[f].value)});const o=await s.generateAsync({type:"blob"});$2.saveAs(o,`code${Math.random().toString().slice(2,8)}.zip`)}const Nv=`{
  "imports": {
    "react": "https://esm.sh/react@19.1.0",
    "react-dom/client": "https://esm.sh/react-dom@19.1.0/client"
  }
}
`,Rv=`:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  line-height: 1.5;
  color: rgb(255 255 255 / 87%);
  text-rendering: optimizelegibility;
  text-size-adjust: 100%;
  background-color: #242424;
  color-scheme: light dark;
  font-synthesis: none;
}

#root {
  max-width: 1280px;
  padding: 2rem;
  margin: 0 auto;
  text-align: center;
}

body {
  display: flex;
  min-width: 320px;
  min-height: 100vh;
  margin: 0;
  place-items: center;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  padding: 0.6em 1.2em;
  font-family: inherit;
  font-size: 1em;
  font-weight: 500;
  cursor: pointer;
  background-color: #1a1a1a;
  border: 1px solid transparent;
  border-radius: 8px;
  transition: border-color 0.25s;
}

button:hover {
  border-color: #646cff;
}

button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #fff;
  }

  button {
    background-color: #f9f9f9;
  }
}
.author {
  color: red;
}`,Uv=`import { useState } from "react";
import "./App.css";

function App() {
  const [count, setCount] = useState(0);

  return (
    <>
      <h1>Copyer</h1>
      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          计数： {count}
        </button>
      </div>
    </>
  );
}

export default App;
`,jv=`import ReactDOM from "react-dom/client";

import App from "./App";

ReactDOM.createRoot(document.getElementById("root")!).render(<App />);
`,ec="App.tsx",wn="import-map.json",La="main.tsx",wv={[La]:{name:La,language:Bu(La),value:jv},[ec]:{name:ec,language:Bu(ec),value:Uv},"App.css":{name:"App.css",language:"css",value:Rv},[wn]:{name:wn,language:Bu(wn),value:Nv}},Ga=W.createContext({selectedFileName:"App.tsx"}),Hv=d=>{const{children:s}=d,[o,f]=W.useState(O()||wv),[_,M]=W.useState("light"),[j,U]=W.useState("App.tsx"),y=H=>{o[H]={name:H,language:Bu(H),value:""},f({...o})},h=H=>{delete o[H],f({...o})},z=(H,B)=>{if(!o[H]||B===void 0||B===null||H===B)return;const{[H]:C,...Y}=o,G={[B]:{...C,language:Bu(B),name:B}};f({...Y,...G})};function O(){let H;try{const B=Mv(window.location.hash.slice(1));H=JSON.parse(B)}catch(B){console.error(B)}return console.log("files======>",H),H}return W.useEffect(()=>{const H=Ov(JSON.stringify(o));window.location.hash=H},[o]),P.jsx(Ga.Provider,{value:{theme:_,setTheme:M,files:o,selectedFileName:j,setSelectedFileName:U,setFiles:f,addFile:y,removeFile:h,updateFileName:z},children:s})},Bv="_header_16kaf_1",Cv="_logo_16kaf_13",Yv="_link_16kaf_23",ps={header:Bv,logo:Cv,link:Yv},qv="data:image/svg+xml,%3csvg%20t='1755572492147'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='11955'%20width='256'%20height='256'%3e%3cpath%20d='M832%20768v64H192v-64h-64v128h768V768z%20m-9.376-329.376l-45.248-45.248L544%20626.752V128h-64v498.752L246.624%20393.376l-45.248%2045.248L512%20749.248z'%20fill='%23ffffff'%20p-id='11956'%3e%3c/path%3e%3c/svg%3e",Vv="data:image/svg+xml,%3csvg%20t='1755572492147'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='11955'%20width='256'%20height='256'%3e%3cpath%20d='M832%20768v64H192v-64h-64v128h768V768z%20m-9.376-329.376l-45.248-45.248L544%20626.752V128h-64v498.752L246.624%20393.376l-45.248%2045.248L512%20749.248z'%20fill='%23181818'%20p-id='11956'%3e%3c/path%3e%3c/svg%3e",Lv="data:image/svg+xml,%3csvg%20t='1755572446398'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='10074'%20width='256'%20height='256'%3e%3cpath%20d='M778.602987%20653.725862c-43.735084%200-82.508155%2020.923542-107.114576%2053.234435l-283.026028-168.531368c3.213181-12.398378%205.100158-25.314549%205.100158-38.715767%200-3.89061-0.302899-7.686053-0.573051-11.494799l283.161105-170.612773c24.713868%2028.935006%2061.412698%2047.344285%20102.452393%2047.344285%2074.43427%200%20134.771473-60.338227%20134.771473-134.76738%200-74.42506-60.338227-134.763286-134.771473-134.763286-74.424037%200-134.759193%2060.334133-134.759193%20134.763286%200%2014.767332%202.445702%2028.916587%206.837732%2042.222637L369.926214%20417.98579c-27.26497-43.37795-75.391061-72.290443-130.376373-72.290443-85.053118%200-154.017816%2068.959581-154.017816%20154.017816s68.963675%20154.012699%20154.017816%20154.012699c45.589314%200%2086.426395-19.925818%20114.624621-51.3976L648.347364%20754.476497c-2.829442%2010.872628-4.490268%2022.23235-4.490268%2034.012651%200%2074.43427%2060.34232%20134.76738%20134.76738%20134.76738%2074.429153%200%20134.763286-60.33311%20134.763286-134.76738C913.388786%20714.059995%20853.037257%20653.725862%20778.602987%20653.725862L778.602987%20653.725862%20778.602987%20653.725862zM778.602987%20653.725862'%20fill='%23ffffff'%20p-id='10075'%3e%3c/path%3e%3c/svg%3e",Gv="data:image/svg+xml,%3csvg%20t='1755572404877'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='9912'%20width='256'%20height='256'%3e%3cpath%20d='M778.602987%20653.725862c-43.735084%200-82.508155%2020.923542-107.114576%2053.234435l-283.026028-168.531368c3.213181-12.398378%205.100158-25.314549%205.100158-38.715767%200-3.89061-0.302899-7.686053-0.573051-11.494799l283.161105-170.612773c24.713868%2028.935006%2061.412698%2047.344285%20102.452393%2047.344285%2074.43427%200%20134.771473-60.338227%20134.771473-134.76738%200-74.42506-60.338227-134.763286-134.771473-134.763286-74.424037%200-134.759193%2060.334133-134.759193%20134.763286%200%2014.767332%202.445702%2028.916587%206.837732%2042.222637L369.926214%20417.98579c-27.26497-43.37795-75.391061-72.290443-130.376373-72.290443-85.053118%200-154.017816%2068.959581-154.017816%20154.017816s68.963675%20154.012699%20154.017816%20154.012699c45.589314%200%2086.426395-19.925818%20114.624621-51.3976L648.347364%20754.476497c-2.829442%2010.872628-4.490268%2022.23235-4.490268%2034.012651%200%2074.43427%2060.34232%20134.76738%20134.76738%20134.76738%2074.429153%200%20134.763286-60.33311%20134.763286-134.76738C913.388786%20714.059995%20853.037257%20653.725862%20778.602987%20653.725862L778.602987%20653.725862%20778.602987%20653.725862zM778.602987%20653.725862'%20fill='%23272636'%20p-id='9913'%3e%3c/path%3e%3c/svg%3e",Xv="data:image/svg+xml,%3csvg%20t='1755570835589'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='8866'%20width='256'%20height='256'%3e%3cpath%20d='M508.475476%20777.927066a262.879%20262.879%200%201%200%2014.927684-525.546038%20262.879%20262.879%200%201%200-14.927684%20525.546038Z'%20p-id='8867'%20fill='%23ffffff'%3e%3c/path%3e%3cpath%20d='M512.119%20213.154c17.673%200%2032-14.327%2032-32V95.896c0-17.673-14.327-32-32-32-17.673%200-32%2014.327-32%2032v85.258c0%2017.673%2014.327%2032%2032%2032zM743.779%20308.938c8.186%200.232%2016.461-2.658%2022.884-8.727l61.973-58.553c12.845-12.137%2013.42-32.391%201.283-45.237-12.139-12.846-32.391-13.421-45.237-1.283l-61.973%2058.553c-12.845%2012.137-13.42%2032.391-1.283%2045.237%206.069%206.422%2014.167%209.777%2022.353%2010.01zM936.04%20487.929l-85.224-2.417c-17.666-0.501-32.393%2013.414-32.894%2031.08-0.501%2017.666%2013.414%2032.394%2031.08%2032.895l85.224%202.417c17.666%200.501%2032.393-13.415%2032.894-31.08%200.501-17.667-13.414-32.394-31.08-32.895zM773.672%20730.999c-12.137-12.846-32.391-13.42-45.237-1.284-12.846%2012.138-13.421%2032.391-1.284%2045.237l58.552%2061.972c6.069%206.424%2014.166%209.779%2022.353%2010.011%208.185%200.232%2016.462-2.659%2022.884-8.727%2012.846-12.138%2013.421-32.391%201.284-45.237l-58.552-61.972zM514.455%20817.14c-17.666-0.501-32.393%2013.414-32.894%2031.08l-2.417%2085.224c-0.501%2017.666%2013.414%2032.393%2031.08%2032.894%2017.666%200.501%2032.393-13.414%2032.894-31.08l2.417-85.224c0.501-17.665-13.414-32.393-31.08-32.894zM256.094%20726.369l-61.972%2058.553c-12.846%2012.138-13.42%2032.391-1.283%2045.237%206.069%206.425%2014.165%209.779%2022.353%2010.011%208.185%200.232%2016.461-2.659%2022.884-8.727l61.972-58.553c12.846-12.138%2013.42-32.391%201.283-45.237-12.138-12.848-32.391-13.42-45.237-1.284zM213.906%20513.673c0.501-17.666-13.414-32.393-31.08-32.894l-85.224-2.417c-17.666-0.501-32.393%2013.414-32.894%2031.08-0.501%2017.666%2013.414%2032.393%2031.08%2032.894l85.224%202.417c17.665%200.501%2032.393-13.414%2032.894-31.08zM258.157%20299.266c6.069%206.423%2014.166%209.778%2022.353%2010.01%208.186%200.232%2016.461-2.658%2022.884-8.727%2012.846-12.137%2013.42-32.39%201.283-45.237l-58.553-61.972c-12.138-12.846-32.39-13.421-45.237-1.283-12.846%2012.137-13.42%2032.391-1.283%2045.237l58.553%2061.972z'%20p-id='8868'%20fill='%23ffffff'%3e%3c/path%3e%3c/svg%3e",Qv="data:image/svg+xml,%3csvg%20t='1755569259782'%20class='icon'%20viewBox='0%200%201066%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='6968'%20width='256'%20height='256'%3e%3cpath%20d='M685.653333%20142.677333l-60.928%2033.322667%2060.928%2033.322667%2033.322667%2060.928%2033.322667-60.928%2060.928-33.322667-60.928-33.322667-33.28-60.928-33.365334%2060.928zM94.976%20512c0-235.648%20191.018667-426.666667%20426.666667-426.666667h73.984l-37.034667%2064c-24.704%2042.666667-36.949333%2093.397333-36.949333%20149.333334a298.666667%20298.666667%200%200%200%20356.181333%20293.12l71.765333-13.952-23.168%2069.376C869.973333%20816.512%20710.101333%20938.666667%20521.642667%20938.666667c-235.648%200-426.666667-191.018667-426.666667-426.666667z%20m789.333333-238.250667l38.997334%2071.253334L994.56%20384l-71.253333%2038.997333-38.997334%2071.253334-38.954666-71.253334L774.101333%20384l71.253334-38.997333%2038.954666-71.253334z'%20fill='%23000000'%20p-id='6969'%3e%3c/path%3e%3c/svg%3e",Zv=new URL("/subs/react-playground/svg/logo-CqE24J1b.svg",import.meta.url).href,Kv=()=>{const{theme:d,setTheme:s,files:o}=W.useContext(Ga);return P.jsxs("div",{className:ps.header,children:[P.jsxs("div",{className:ps.logo,children:[P.jsx("img",{src:Zv,alt:"logo"}),P.jsx("div",{className:"title",children:"React PlayGround"})]}),P.jsxs("div",{className:ps.link,children:[P.jsx("img",{className:"icon",src:d!=="light"?Xv:Qv,alt:"theme",onClick:()=>s(d==="light"?"dark":"light")}),P.jsx("img",{className:"icon",src:d!=="light"?qv:Vv,alt:"download",onClick:async()=>{await Dv(o)}}),P.jsx("img",{className:"icon",src:d!=="light"?Lv:Gv,alt:"share",onClick:()=>{Y2(window.location.href),alert("分享链接已复制。")}})]})]})},Jv=W.lazy(()=>q2(()=>Promise.resolve().then(()=>x1),void 0));function $v(){return P.jsxs("div",{style:{width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"#1e1e1e",color:"#cccccc",fontSize:"14px"},children:[P.jsxs("div",{children:[P.jsx("div",{style:{marginBottom:"8px"},children:"正在加载编辑器..."}),P.jsx("div",{style:{width:"200px",height:"4px",backgroundColor:"#333",borderRadius:"2px",overflow:"hidden"},children:P.jsx("div",{style:{width:"100%",height:"100%",background:"linear-gradient(90deg, transparent, #007acc, transparent)",animation:"loading 1.5s infinite"}})})]}),P.jsx("style",{children:`
        @keyframes loading {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
      `})]})}function Wv(d){return P.jsx(W.Suspense,{fallback:P.jsx($v,{}),children:P.jsx(Jv,{...d})})}const kv="_CodeEditor_1uyzt_1",Fv="_fileNameList_1uyzt_11",Iv="_tabItem_1uyzt_32",Pv="_selectTabItem_1uyzt_41",t1="_tabsItemInput_1uyzt_46",e1="_add_1uyzt_57",Hn={CodeEditor:kv,fileNameList:Fv,tabItem:Iv,selectTabItem:Pv,tabsItemInput:t1,add:e1},l1=d=>{const{value:s,selected:o,onClick:f,onEditComplete:_,created:M,onRemove:j,readonly:U}=d,[y,h]=W.useState(s),[z,O]=W.useState(M),H=W.useRef(null),B=()=>{U||(O(!0),setTimeout(()=>{H.current?.focus()},0))},C=Y=>{Y.stopPropagation(),confirm("确定删除吗？")&&j?.()};return P.jsx("div",{onClick:f,className:Th(Hn.tabItem,{[Hn.selectTabItem]:o}),children:z?P.jsx("input",{ref:H,value:y,className:Hn.tabsItemInput,onChange:Y=>h(Y.target.value),onBlur:()=>{O(!1),_?.(y)}}):P.jsxs(P.Fragment,{children:[P.jsx("span",{onDoubleClick:B,children:y}),!U&&P.jsx("span",{style:{marginLeft:5,display:"flex"},onClick:C,children:P.jsxs("svg",{width:"12",height:"12",viewBox:"0 0 24 24",children:[P.jsx("line",{stroke:"#999",x1:"18",y1:"6",x2:"6",y2:"18"}),P.jsx("line",{stroke:"#999",x1:"6",y1:"6",x2:"18",y2:"18"})]})})]})})},a1=[La,wn,ec];function n1(){const d=W.useContext(Ga),{files:s,removeFile:o,addFile:f,updateFileName:_,setSelectedFileName:M,selectedFileName:j}=d,[U,y]=W.useState([""]),[h,z]=W.useState(!1);W.useEffect(()=>{y(Object.keys(s))},[s]);const O=Y=>{M(Y)},H=(Y,G)=>{_(G,Y),M(Y),z(!1)},B=()=>{const Y=`Comp${Math.random().toString().slice(2,6)}.tsx`;f(Y),M(Y),z(!0)},C=Y=>{o(Y),M(La)};return P.jsxs("div",{className:Hn.fileNameList,children:[U.map((Y,G)=>P.jsx(l1,{value:Y,readonly:a1.includes(Y),created:h&&G===U.length-1,selected:Y===j,onClick:()=>O(Y),onEditComplete:X=>H(X,Y),onRemove:()=>{C(Y)}},Y+G)),P.jsx("div",{className:Hn.add,onClick:B,children:"+"})]})}const u1=()=>{const d=W.useContext(Ga),{files:s,selectedFileName:o,setFiles:f}=d,_=s[o];function M(j){L2(j)||(s[o].value=j,f({...s}))}return P.jsxs("div",{className:Hn.CodeEditor,children:[P.jsx("div",{className:"file-name-list",children:P.jsx(n1,{})}),P.jsx("div",{className:"editor",children:P.jsx(Wv,{file:_,onChange:Ah(M,500)})})]})},i1="_msg_v5z09_1",c1="_error_v5z09_17",f1="_warn_v5z09_21",s1="_dismiss_v5z09_33",Ss={msg:i1,error:c1,warn:f1,dismiss:s1},r1=d=>{const{type:s,content:o}=d,[f,_]=W.useState(!1);return W.useEffect(()=>{_(!!o)},[o]),f?P.jsxs("div",{className:Th(Ss.msg,Ss[s]),children:[P.jsx("pre",{dangerouslySetInnerHTML:{__html:o}}),P.jsx("button",{className:Ss.dismiss,onClick:()=>_(!1),children:"✕"})]}):null},o1=`<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Preview</title>
  </head>
  <body>
    <script>
      window.addEventListener("error", (e) => {
        // 监听到错误，传递给父元素
        window.parent.postMessage({ type: "error", message: e.message }, "*");
      });
    <\/script>
    <script type="importmap"><\/script>
    <script type="module" id="appSrc"><\/script>
    <div id="root"></div>
  </body>
</html>
`,h1=(d,s)=>{let o=s;const f=/import\s+React\b/g;return(d.endsWith(".jsx")||d.endsWith(".tsx"))&&!f.test(s)&&(o=`import React from 'react';
${s}`),o},Bh=(d,s,o)=>{let f="";try{const _=h1(d,s);f=X2.transform(_,{presets:["react","typescript"],filename:d,plugins:[y1(o)],retainLines:!0}).code}catch(_){console.error("编译出错",_)}return f},d1=d=>{const s=d[La];return Bh(La,s.value,d)};function v1(d){const s=`export default ${d.value}`;return URL.createObjectURL(new Blob([s],{type:"application/json"}))}function m1(d){const o=`(() => {
  const styleSheet = document.createElement("style");
  styleSheet.setAttribute("id", 'style_${new Date().getTime()}_${d.name}');
  document.head.appendChild(styleSheet);

  const styles = document.createTextNode(\`${d.value}\`);
  styleSheet.innerHTML = "";
  styleSheet.appendChild(styles);
  })()`;return URL.createObjectURL(new Blob([o],{type:"text/javascript"}))}function y1(d){return{visitor:{ImportDeclaration(s){const o=s.node.source.value;if(o.startsWith(".")){const f=g1(d,o);if(!f)return;f.name.endsWith(".css")?s.node.source.value=m1(f):f.name.endsWith(".json")?s.node.source.value=v1(f):s.node.source.value=URL.createObjectURL(new Blob([Bh(f.name,f.value,d)],{type:"application/javascript"}))}}}}}function g1(d,s){let o=s.split("./").pop()||"";if(!o.includes(".")){const f=Object.keys(d).filter(_=>_.endsWith(".ts")||_.endsWith(".tsx")||_.endsWith(".js")||_.endsWith(".jsx")).find(_=>_.split(".").includes(o));f&&(o=f)}return d[o]}function p1(){const{files:d}=W.useContext(Ga),[s,o]=W.useState(""),[f,_]=W.useState(""),[M,j]=W.useState("");W.useEffect(Ah(()=>{o(d1(d))},500),[d]);const U=()=>{const h=o1.replace('<script type="importmap"><\/script>',`<script type="importmap">${d[wn].value}<\/script>`).replace('<script type="module" id="appSrc"><\/script>',`<script type="module" id="appSrc">${s}<\/script>`);return URL.createObjectURL(new Blob([h],{type:"text/html"}))};W.useEffect(()=>{_(U())},[d[wn].value,s]);const y=h=>{h.data.type==="error"&&j(h.data.message)};return W.useEffect(()=>(window.addEventListener("message",y),()=>{window.removeEventListener("message",y)}),[]),P.jsxs("div",{style:{height:"100%",position:"relative"},children:[f?P.jsx("iframe",{src:f,style:{width:"100%",height:"100%",padding:0,border:"none"}}):P.jsx("div",{children:"编译中..."}),P.jsx(r1,{type:"error",content:M})]})}function S1(){const{theme:d}=W.useContext(Ga);return P.jsxs("div",{className:`react_playground_${d}`,style:{height:"100vh"},children:[P.jsx(Kv,{}),P.jsx("div",{style:{height:"calc(100vh - 60px)"},children:P.jsxs(gs,{defaultSizes:[100,100],children:[P.jsx(gs.Pane,{minSize:500,children:P.jsx(u1,{})}),P.jsx(gs.Pane,{minSize:0,children:P.jsx(p1,{})})]})})]})}function b1(){return P.jsx(Hv,{children:P.jsx(S1,{})})}function z1(){return P.jsx(b1,{})}let Bn=null;function Eh(d={}){const{container:s}=d,o=s?s.querySelector("#root"):document.querySelector("#root");Bn||(Bn=av.createRoot(o)),Bn.render(P.jsx(z1,{}))}function _1(){if(!k0.qiankunWindow.__POWERED_BY_QIANKUN__){console.log("%c 独立渲染","color: red; font-size: 20px;"),Eh();return}k0.renderWithQiankun({mount(d){console.log("%c qiankun 渲染","color: red; font-size: 20px;"),console.log("Qiankun mount",d),Eh(d)},bootstrap(){console.log("Qiankun bootstrap")},unmount(d){console.log("Qiankun unmount",d),Bn&&(Bn.unmount(),Bn=null)},update(d){console.log("Qiankun update",d)}})}_1();function E1(d){return W2({typescript:Q2,logger:console,delegate:{receivedFile:(o,f)=>{d(o,f)}}})}function T1(){W0.config({paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}}),W0.init().then(d=>{d.languages.typescript.typescriptDefaults.setCompilerOptions({target:d.languages.typescript.ScriptTarget.ES2020,allowNonTsExtensions:!0,moduleResolution:d.languages.typescript.ModuleResolutionKind.NodeJs,module:d.languages.typescript.ModuleKind.ESNext,noEmit:!0,esModuleInterop:!0,jsx:d.languages.typescript.JsxEmit.ReactJSX,allowJs:!0,typeRoots:["node_modules/@types"]}),d.languages.typescript.javascriptDefaults.setCompilerOptions({target:d.languages.typescript.ScriptTarget.ES2020,allowNonTsExtensions:!0,moduleResolution:d.languages.typescript.ModuleResolutionKind.NodeJs,module:d.languages.typescript.ModuleKind.ESNext,noEmit:!0,esModuleInterop:!0,allowJs:!0,jsx:d.languages.typescript.JsxEmit.ReactJSX}),d.languages.typescript.typescriptDefaults.setDiagnosticsOptions({noSemanticValidation:!1,noSyntaxValidation:!1,noSuggestionDiagnostics:!0}),d.languages.typescript.javascriptDefaults.setDiagnosticsOptions({noSemanticValidation:!1,noSyntaxValidation:!1,noSuggestionDiagnostics:!0})})}function A1(d){const{file:s,onChange:o,options:f={}}=d,{theme:_}=W.useContext(Ga);W.useEffect(()=>{T1()},[]);const M=(j,U)=>{j.addCommand(U.KeyMod.CtrlCmd|U.KeyCode.KeyK,()=>{j.getAction("editor.action.formatDocument")?.run()});const y=E1((h,z)=>{U.languages.typescript.typescriptDefaults.addExtraLib(h,`file://${z}`)});U.languages.typescript.typescriptDefaults.setCompilerOptions({jsx:U.languages.typescript.JsxEmit.Preserve,esModuleInterop:!0}),j.onDidChangeModelContent(()=>{y(j.getValue())}),y(j.getValue())};return P.jsx(V2,{height:"100%",path:s.name,language:s.language,onMount:M,value:s.value,onChange:o,theme:`vs-${_}`,options:{fontSize:14,scrollBeyondLastLine:!1,minimap:{enabled:!1},scrollbar:{verticalScrollbarSize:6,horizontalScrollbarSize:6},...f}})}const x1=Object.freeze(Object.defineProperty({__proto__:null,default:A1},Symbol.toStringTag,{value:"Module"}));export{Hl as W,W as r};
