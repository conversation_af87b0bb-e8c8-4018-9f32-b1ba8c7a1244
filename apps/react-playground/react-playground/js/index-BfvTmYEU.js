import{r as c,j as s}from"./<EMAIL>";import{c as T}from"./react-dom@<EMAIL>";import{x as L}from"./allotment@1.20.4_react-dom@<EMAIL>";import{s as I,u as O,a as k,z as U}from"./<EMAIL>";import{J as $}from"./<EMAIL>";import{F as D}from"./<EMAIL>";import{c as B}from"./<EMAIL>";import{F as W}from"./@monaco-editor_react@4.7.0-rc.0_monaco-editor@0.52.2_react-dom@<EMAIL>";import"./<EMAIL>";import{s as J}from"./@typescript_ata@<EMAIL>";import{t as V}from"./<EMAIL>";import{c as R}from"./<EMAIL>";import{d as A,i as q}from"./<EMAIL>";import{b as H}from"./@<EMAIL>";import{h as S}from"./vite-plugin-qiankun@1.0.15_typescript@<EMAIL>";import"./<EMAIL>";import"./<EMAIL>";import"./@<EMAIL>";import"./<EMAIL>";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))o(r);new MutationObserver(r=>{for(const i of r)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&o(l)}).observe(document,{childList:!0,subtree:!0});function n(r){const i={};return r.integrity&&(i.integrity=r.integrity),r.referrerPolicy&&(i.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?i.credentials="include":r.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function o(r){if(r.ep)return;r.ep=!0;const i=n(r);fetch(r.href,i)}})();const C=e=>{const t=e.split(".").pop()||"";return["js","jsx"].includes(t)?"javascript":["ts","tsx"].includes(t)?"typescript":["json"].includes(t)?"json":["css"].includes(t)?"css":"javascript"};function K(e){try{const t=I(e),n=U(t,{level:9}),o=k(n,!0);return btoa(o)}catch(t){throw console.error("压缩失败:",t),new Error("数据压缩失败")}}function Q(e){try{if(!e||typeof e!="string")throw new Error("无效的 base64 字符串");const t=atob(e),n=I(t,!0);if(n.length===0)throw new Error("解压缩数据为空");const o=O(n);return k(o)}catch(t){throw console.error("解压缩失败:",t),new Error("数据解压缩失败: "+(t instanceof Error?t.message:"未知错误"))}}async function Y(e){const t=new $;Object.keys(e).forEach(o=>{t.file(o,e[o].value)});const n=await t.generateAsync({type:"blob"});D.saveAs(n,`code${Math.random().toString().slice(2,8)}.zip`)}const Z=`{
  "imports": {
    "react": "https://esm.sh/react@19.1.0",
    "react-dom/client": "https://esm.sh/react-dom@19.1.0/client"
  }
}
`,G=`:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  line-height: 1.5;
  color: rgb(255 255 255 / 87%);
  text-rendering: optimizelegibility;
  text-size-adjust: 100%;
  background-color: #242424;
  color-scheme: light dark;
  font-synthesis: none;
}

#root {
  max-width: 1280px;
  padding: 2rem;
  margin: 0 auto;
  text-align: center;
}

body {
  display: flex;
  min-width: 320px;
  min-height: 100vh;
  margin: 0;
  place-items: center;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  padding: 0.6em 1.2em;
  font-family: inherit;
  font-size: 1em;
  font-weight: 500;
  cursor: pointer;
  background-color: #1a1a1a;
  border: 1px solid transparent;
  border-radius: 8px;
  transition: border-color 0.25s;
}

button:hover {
  border-color: #646cff;
}

button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #fff;
  }

  button {
    background-color: #f9f9f9;
  }
}
.author {
  color: red;
}`,X=`import { useState } from "react";
import "./App.css";

function App() {
  const [count, setCount] = useState(0);

  return (
    <>
      <h1>Copyer</h1>
      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          计数： {count}
        </button>
      </div>
    </>
  );
}

export default App;
`,ee=`import ReactDOM from "react-dom/client";

import App from "./App";

ReactDOM.createRoot(document.getElementById("root")!).render(<App />);
`,E="App.tsx",w="import-map.json",g="main.tsx",te={[g]:{name:g,language:C(g),value:ee},[E]:{name:E,language:C(E),value:X},"App.css":{name:"App.css",language:"css",value:G},[w]:{name:w,language:C(w),value:Z}},v=c.createContext({selectedFileName:"App.tsx"}),ne=e=>{const{children:t}=e,[n,o]=c.useState(x()||te),[r,i]=c.useState("light"),[l,u]=c.useState("App.tsx"),m=d=>{n[d]={name:d,language:C(d),value:""},o({...n})},f=d=>{delete n[d],o({...n})},h=(d,p)=>{if(!n[d]||p===void 0||p===null||d===p)return;const{[d]:_,...a}=n,y={[p]:{..._,language:C(p),name:p}};o({...a,...y})};function x(){let d;try{const p=Q(window.location.hash.slice(1));d=JSON.parse(p)}catch(p){console.error(p)}return console.log("files======>",d),d}return c.useEffect(()=>{const d=K(JSON.stringify(n));window.location.hash=d},[n]),s.jsx(v.Provider,{value:{theme:r,setTheme:i,files:n,selectedFileName:l,setSelectedFileName:u,setFiles:o,addFile:m,removeFile:f,updateFileName:h},children:t})},se="_header_16kaf_1",oe="_logo_16kaf_13",re="_link_16kaf_23",z={header:se,logo:oe,link:re},ie="data:image/svg+xml,%3csvg%20t='1755572492147'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='11955'%20width='256'%20height='256'%3e%3cpath%20d='M832%20768v64H192v-64h-64v128h768V768z%20m-9.376-329.376l-45.248-45.248L544%20626.752V128h-64v498.752L246.624%20393.376l-45.248%2045.248L512%20749.248z'%20fill='%23ffffff'%20p-id='11956'%3e%3c/path%3e%3c/svg%3e",ce="data:image/svg+xml,%3csvg%20t='1755572492147'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='11955'%20width='256'%20height='256'%3e%3cpath%20d='M832%20768v64H192v-64h-64v128h768V768z%20m-9.376-329.376l-45.248-45.248L544%20626.752V128h-64v498.752L246.624%20393.376l-45.248%2045.248L512%20749.248z'%20fill='%23181818'%20p-id='11956'%3e%3c/path%3e%3c/svg%3e",ae="data:image/svg+xml,%3csvg%20t='1755572446398'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='10074'%20width='256'%20height='256'%3e%3cpath%20d='M778.602987%20653.725862c-43.735084%200-82.508155%2020.923542-107.114576%2053.234435l-283.026028-168.531368c3.213181-12.398378%205.100158-25.314549%205.100158-38.715767%200-3.89061-0.302899-7.686053-0.573051-11.494799l283.161105-170.612773c24.713868%2028.935006%2061.412698%2047.344285%20102.452393%2047.344285%2074.43427%200%20134.771473-60.338227%20134.771473-134.76738%200-74.42506-60.338227-134.763286-134.771473-134.763286-74.424037%200-134.759193%2060.334133-134.759193%20134.763286%200%2014.767332%202.445702%2028.916587%206.837732%2042.222637L369.926214%20417.98579c-27.26497-43.37795-75.391061-72.290443-130.376373-72.290443-85.053118%200-154.017816%2068.959581-154.017816%20154.017816s68.963675%20154.012699%20154.017816%20154.012699c45.589314%200%2086.426395-19.925818%20114.624621-51.3976L648.347364%20754.476497c-2.829442%2010.872628-4.490268%2022.23235-4.490268%2034.012651%200%2074.43427%2060.34232%20134.76738%20134.76738%20134.76738%2074.429153%200%20134.763286-60.33311%20134.763286-134.76738C913.388786%20714.059995%20853.037257%20653.725862%20778.602987%20653.725862L778.602987%20653.725862%20778.602987%20653.725862zM778.602987%20653.725862'%20fill='%23ffffff'%20p-id='10075'%3e%3c/path%3e%3c/svg%3e",le="data:image/svg+xml,%3csvg%20t='1755572404877'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='9912'%20width='256'%20height='256'%3e%3cpath%20d='M778.602987%20653.725862c-43.735084%200-82.508155%2020.923542-107.114576%2053.234435l-283.026028-168.531368c3.213181-12.398378%205.100158-25.314549%205.100158-38.715767%200-3.89061-0.302899-7.686053-0.573051-11.494799l283.161105-170.612773c24.713868%2028.935006%2061.412698%2047.344285%20102.452393%2047.344285%2074.43427%200%20134.771473-60.338227%20134.771473-134.76738%200-74.42506-60.338227-134.763286-134.771473-134.763286-74.424037%200-134.759193%2060.334133-134.759193%20134.763286%200%2014.767332%202.445702%2028.916587%206.837732%2042.222637L369.926214%20417.98579c-27.26497-43.37795-75.391061-72.290443-130.376373-72.290443-85.053118%200-154.017816%2068.959581-154.017816%20154.017816s68.963675%20154.012699%20154.017816%20154.012699c45.589314%200%2086.426395-19.925818%20114.624621-51.3976L648.347364%20754.476497c-2.829442%2010.872628-4.490268%2022.23235-4.490268%2034.012651%200%2074.43427%2060.34232%20134.76738%20134.76738%20134.76738%2074.429153%200%20134.763286-60.33311%20134.763286-134.76738C913.388786%20714.059995%20853.037257%20653.725862%20778.602987%20653.725862L778.602987%20653.725862%20778.602987%20653.725862zM778.602987%20653.725862'%20fill='%23272636'%20p-id='9913'%3e%3c/path%3e%3c/svg%3e",de="data:image/svg+xml,%3csvg%20t='1755570835589'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='8866'%20width='256'%20height='256'%3e%3cpath%20d='M508.475476%20777.927066a262.879%20262.879%200%201%200%2014.927684-525.546038%20262.879%20262.879%200%201%200-14.927684%20525.546038Z'%20p-id='8867'%20fill='%23ffffff'%3e%3c/path%3e%3cpath%20d='M512.119%20213.154c17.673%200%2032-14.327%2032-32V95.896c0-17.673-14.327-32-32-32-17.673%200-32%2014.327-32%2032v85.258c0%2017.673%2014.327%2032%2032%2032zM743.779%20308.938c8.186%200.232%2016.461-2.658%2022.884-8.727l61.973-58.553c12.845-12.137%2013.42-32.391%201.283-45.237-12.139-12.846-32.391-13.421-45.237-1.283l-61.973%2058.553c-12.845%2012.137-13.42%2032.391-1.283%2045.237%206.069%206.422%2014.167%209.777%2022.353%2010.01zM936.04%20487.929l-85.224-2.417c-17.666-0.501-32.393%2013.414-32.894%2031.08-0.501%2017.666%2013.414%2032.394%2031.08%2032.895l85.224%202.417c17.666%200.501%2032.393-13.415%2032.894-31.08%200.501-17.667-13.414-32.394-31.08-32.895zM773.672%20730.999c-12.137-12.846-32.391-13.42-45.237-1.284-12.846%2012.138-13.421%2032.391-1.284%2045.237l58.552%2061.972c6.069%206.424%2014.166%209.779%2022.353%2010.011%208.185%200.232%2016.462-2.659%2022.884-8.727%2012.846-12.138%2013.421-32.391%201.284-45.237l-58.552-61.972zM514.455%20817.14c-17.666-0.501-32.393%2013.414-32.894%2031.08l-2.417%2085.224c-0.501%2017.666%2013.414%2032.393%2031.08%2032.894%2017.666%200.501%2032.393-13.414%2032.894-31.08l2.417-85.224c0.501-17.665-13.414-32.393-31.08-32.894zM256.094%20726.369l-61.972%2058.553c-12.846%2012.138-13.42%2032.391-1.283%2045.237%206.069%206.425%2014.165%209.779%2022.353%2010.011%208.185%200.232%2016.461-2.659%2022.884-8.727l61.972-58.553c12.846-12.138%2013.42-32.391%201.283-45.237-12.138-12.848-32.391-13.42-45.237-1.284zM213.906%20513.673c0.501-17.666-13.414-32.393-31.08-32.894l-85.224-2.417c-17.666-0.501-32.393%2013.414-32.894%2031.08-0.501%2017.666%2013.414%2032.393%2031.08%2032.894l85.224%202.417c17.665%200.501%2032.393-13.414%2032.894-31.08zM258.157%20299.266c6.069%206.423%2014.166%209.778%2022.353%2010.01%208.186%200.232%2016.461-2.658%2022.884-8.727%2012.846-12.137%2013.42-32.39%201.283-45.237l-58.553-61.972c-12.138-12.846-32.39-13.421-45.237-1.283-12.846%2012.137-13.42%2032.391-1.283%2045.237l58.553%2061.972z'%20p-id='8868'%20fill='%23ffffff'%3e%3c/path%3e%3c/svg%3e",ue="data:image/svg+xml,%3csvg%20t='1755569259782'%20class='icon'%20viewBox='0%200%201066%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='6968'%20width='256'%20height='256'%3e%3cpath%20d='M685.653333%20142.677333l-60.928%2033.322667%2060.928%2033.322667%2033.322667%2060.928%2033.322667-60.928%2060.928-33.322667-60.928-33.322667-33.28-60.928-33.365334%2060.928zM94.976%20512c0-235.648%20191.018667-426.666667%20426.666667-426.666667h73.984l-37.034667%2064c-24.704%2042.666667-36.949333%2093.397333-36.949333%20149.333334a298.666667%20298.666667%200%200%200%20356.181333%20293.12l71.765333-13.952-23.168%2069.376C869.973333%20816.512%20710.101333%20938.666667%20521.642667%20938.666667c-235.648%200-426.666667-191.018667-426.666667-426.666667z%20m789.333333-238.250667l38.997334%2071.253334L994.56%20384l-71.253333%2038.997333-38.997334%2071.253334-38.954666-71.253334L774.101333%20384l71.253334-38.997333%2038.954666-71.253334z'%20fill='%23000000'%20p-id='6969'%3e%3c/path%3e%3c/svg%3e",pe=new URL("/subs/react-playground/svg/logo-CqE24J1b.svg",import.meta.url).href,me=()=>{const{theme:e,setTheme:t,files:n}=c.useContext(v);return s.jsxs("div",{className:z.header,children:[s.jsxs("div",{className:z.logo,children:[s.jsx("img",{src:pe,alt:"logo"}),s.jsx("div",{className:"title",children:"React PlayGround"})]}),s.jsxs("div",{className:z.link,children:[s.jsx("img",{className:"icon",src:e!=="light"?de:ue,alt:"theme",onClick:()=>t(e==="light"?"dark":"light")}),s.jsx("img",{className:"icon",src:e!=="light"?ie:ce,alt:"download",onClick:async()=>{await Y(n)}}),s.jsx("img",{className:"icon",src:e!=="light"?ae:le,alt:"share",onClick:()=>{B(window.location.href),alert("分享链接已复制。")}})]})]})};function fe(e){return J({typescript:V,logger:console,delegate:{receivedFile:(n,o)=>{e(n,o)}}})}function he(e){const{file:t,onChange:n,options:o={}}=e,{theme:r}=c.useContext(v),i=(l,u)=>{l.addCommand(u.KeyMod.CtrlCmd|u.KeyCode.KeyK,()=>{l.getAction("editor.action.formatDocument")?.run()});const m=fe((f,h)=>{u.languages.typescript.typescriptDefaults.addExtraLib(f,`file://${h}`)});u.languages.typescript.typescriptDefaults.setCompilerOptions({jsx:u.languages.typescript.JsxEmit.Preserve,esModuleInterop:!0}),l.onDidChangeModelContent(()=>{m(l.getValue())}),m(l.getValue())};return s.jsx(W,{height:"100%",path:t.name,language:t.language,onMount:i,value:t.value,onChange:n,theme:`vs-${r}`,options:{fontSize:14,scrollBeyondLastLine:!1,minimap:{enabled:!1},scrollbar:{verticalScrollbarSize:6,horizontalScrollbarSize:6},...o}})}const ge="_CodeEditor_1uyzt_1",ve="_fileNameList_1uyzt_11",xe="_tabItem_1uyzt_32",ye="_selectTabItem_1uyzt_41",we="_tabsItemInput_1uyzt_46",be="_add_1uyzt_57",b={CodeEditor:ge,fileNameList:ve,tabItem:xe,selectTabItem:ye,tabsItemInput:we,add:be},je=e=>{const{value:t,selected:n,onClick:o,onEditComplete:r,created:i,onRemove:l,readonly:u}=e,[m,f]=c.useState(t),[h,x]=c.useState(i),d=c.useRef(null),p=()=>{u||(x(!0),setTimeout(()=>{d.current?.focus()},0))},_=a=>{a.stopPropagation(),confirm("确定删除吗？")&&l?.()};return s.jsx("div",{onClick:o,className:R(b.tabItem,{[b.selectTabItem]:n}),children:h?s.jsx("input",{ref:d,value:m,className:b.tabsItemInput,onChange:a=>f(a.target.value),onBlur:()=>{x(!1),r?.(m)}}):s.jsxs(s.Fragment,{children:[s.jsx("span",{onDoubleClick:p,children:m}),!u&&s.jsx("span",{style:{marginLeft:5,display:"flex"},onClick:_,children:s.jsxs("svg",{width:"12",height:"12",viewBox:"0 0 24 24",children:[s.jsx("line",{stroke:"#999",x1:"18",y1:"6",x2:"6",y2:"18"}),s.jsx("line",{stroke:"#999",x1:"6",y1:"6",x2:"18",y2:"18"})]})})]})})},_e=[g,w,E];function Ce(){const e=c.useContext(v),{files:t,removeFile:n,addFile:o,updateFileName:r,setSelectedFileName:i,selectedFileName:l}=e,[u,m]=c.useState([""]),[f,h]=c.useState(!1);c.useEffect(()=>{m(Object.keys(t))},[t]);const x=a=>{i(a)},d=(a,y)=>{r(y,a),i(a),h(!1)},p=()=>{const a=`Comp${Math.random().toString().slice(2,6)}.tsx`;o(a),i(a),h(!0)},_=a=>{n(a),i(g)};return s.jsxs("div",{className:b.fileNameList,children:[u.map((a,y)=>s.jsx(je,{value:a,readonly:_e.includes(a),created:f&&y===u.length-1,selected:a===l,onClick:()=>x(a),onEditComplete:P=>d(P,a),onRemove:()=>{_(a)}},a+y)),s.jsx("div",{className:b.add,onClick:p,children:"+"})]})}const Ee=()=>{const e=c.useContext(v),{files:t,selectedFileName:n,setFiles:o}=e,r=t[n];function i(l){q(l)||(t[n].value=l,o({...t}))}return s.jsxs("div",{className:b.CodeEditor,children:[s.jsx("div",{className:"file-name-list",children:s.jsx(Ce,{})}),s.jsx("div",{className:"editor",children:s.jsx(he,{file:r,onChange:A(i,500)})})]})},Le="_msg_v5z09_1",ze="_error_v5z09_17",Ne="_warn_v5z09_21",Se="_dismiss_v5z09_33",N={msg:Le,error:ze,warn:Ne,dismiss:Se},Me=e=>{const{type:t,content:n}=e,[o,r]=c.useState(!1);return c.useEffect(()=>{r(!!n)},[n]),o?s.jsxs("div",{className:R(N.msg,N[t]),children:[s.jsx("pre",{dangerouslySetInnerHTML:{__html:n}}),s.jsx("button",{className:N.dismiss,onClick:()=>r(!1),children:"✕"})]}):null},Ie=`<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Preview</title>
  </head>
  <body>
    <script>
      window.addEventListener("error", (e) => {
        // 监听到错误，传递给父元素
        window.parent.postMessage({ type: "error", message: e.message }, "*");
      });
    <\/script>
    <script type="importmap"><\/script>
    <script type="module" id="appSrc"><\/script>
    <div id="root"></div>
  </body>
</html>
`,ke=(e,t)=>{let n=t;const o=/import\s+React\b/g;return(e.endsWith(".jsx")||e.endsWith(".tsx"))&&!o.test(t)&&(n=`import React from 'react';
${t}`),n},F=(e,t,n)=>{let o="";try{const r=ke(e,t);o=H.transform(r,{presets:["react","typescript"],filename:e,plugins:[Pe(n)],retainLines:!0}).code}catch(r){console.error("编译出错",r)}return o},Re=e=>{const t=e[g];return F(g,t.value,e)};function Ae(e){const t=`export default ${e.value}`;return URL.createObjectURL(new Blob([t],{type:"application/json"}))}function Fe(e){const n=`(() => {
  const styleSheet = document.createElement("style");
  styleSheet.setAttribute("id", 'style_${new Date().getTime()}_${e.name}');
  document.head.appendChild(styleSheet);

  const styles = document.createTextNode(\`${e.value}\`);
  styleSheet.innerHTML = "";
  styleSheet.appendChild(styles);
  })()`;return URL.createObjectURL(new Blob([n],{type:"text/javascript"}))}function Pe(e){return{visitor:{ImportDeclaration(t){const n=t.node.source.value;if(n.startsWith(".")){const o=Te(e,n);if(!o)return;o.name.endsWith(".css")?t.node.source.value=Fe(o):o.name.endsWith(".json")?t.node.source.value=Ae(o):t.node.source.value=URL.createObjectURL(new Blob([F(o.name,o.value,e)],{type:"application/javascript"}))}}}}}function Te(e,t){let n=t.split("./").pop()||"";if(!n.includes(".")){const o=Object.keys(e).filter(r=>r.endsWith(".ts")||r.endsWith(".tsx")||r.endsWith(".js")||r.endsWith(".jsx")).find(r=>r.split(".").includes(n));o&&(n=o)}return e[n]}function Oe(){const{files:e}=c.useContext(v),[t,n]=c.useState(""),[o,r]=c.useState(""),[i,l]=c.useState("");c.useEffect(A(()=>{n(Re(e))},500),[e]);const u=()=>{const f=Ie.replace('<script type="importmap"><\/script>',`<script type="importmap">${e[w].value}<\/script>`).replace('<script type="module" id="appSrc"><\/script>',`<script type="module" id="appSrc">${t}<\/script>`);return URL.createObjectURL(new Blob([f],{type:"text/html"}))};c.useEffect(()=>{r(u())},[e[w].value,t]);const m=f=>{f.data.type==="error"&&l(f.data.message)};return c.useEffect(()=>(window.addEventListener("message",m),()=>{window.removeEventListener("message",m)}),[]),s.jsxs("div",{style:{height:"100%",position:"relative"},children:[o?s.jsx("iframe",{src:o,style:{width:"100%",height:"100%",padding:0,border:"none"}}):s.jsx("div",{children:"编译中..."}),s.jsx(Me,{type:"error",content:i})]})}function Ue(){const{theme:e}=c.useContext(v);return s.jsxs("div",{className:`react_playground_${e}`,style:{height:"100vh"},children:[s.jsx(me,{}),s.jsx("div",{style:{height:"calc(100vh - 60px)"},children:s.jsxs(L,{defaultSizes:[100,100],children:[s.jsx(L.Pane,{minSize:500,children:s.jsx(Ee,{})}),s.jsx(L.Pane,{minSize:0,children:s.jsx(Oe,{})})]})})]})}function $e(){return s.jsx(ne,{children:s.jsx(Ue,{})})}function De(){return s.jsx($e,{})}let j=null;function M(e={}){const{container:t}=e,n=t?t.querySelector("#root"):document.querySelector("#root");j||(j=T.createRoot(n)),j.render(s.jsx(De,{}))}function Be(){if(!S.qiankunWindow.__POWERED_BY_QIANKUN__){console.log("%c 独立渲染","color: red; font-size: 20px;"),M();return}S.renderWithQiankun({mount(e){console.log("%c qiankun 渲染","color: red; font-size: 20px;"),console.log("Qiankun mount",e),M(e)},bootstrap(){console.log("Qiankun bootstrap")},unmount(e){console.log("Qiankun unmount",e),j&&(j.unmount(),j=null)},update(e){console.log("Qiankun update",e)}})}Be();
