import{g as v}from"./babel-standalone-mTViEsrM.js";import{r as w}from"./<EMAIL>";var i,f;function D(){if(f)return i;f=1;var y=w(),d={"text/plain":"Text","text/html":"Url",default:"Text"},m="Copy to clipboard: #{key}, Enter";function g(r){var a=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C";return r.replace(/#{\s*key\s*}/g,a)}function b(r,a){var o,p,u,l,c,e,n=!1;a||(a={}),o=a.debug||!1;try{u=y(),l=document.createRange(),c=document.getSelection(),e=document.createElement("span"),e.textContent=r,e.ariaHidden="true",e.style.all="unset",e.style.position="fixed",e.style.top=0,e.style.clip="rect(0, 0, 0, 0)",e.style.whiteSpace="pre",e.style.webkitUserSelect="text",e.style.MozUserSelect="text",e.style.msUserSelect="text",e.style.userSelect="text",e.addEventListener("copy",function(t){if(t.stopPropagation(),a.format)if(t.preventDefault(),typeof t.clipboardData>"u"){o&&console.warn("unable to use e.clipboardData"),o&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var s=d[a.format]||d.default;window.clipboardData.setData(s,r)}else t.clipboardData.clearData(),t.clipboardData.setData(a.format,r);a.onCopy&&(t.preventDefault(),a.onCopy(t.clipboardData))}),document.body.appendChild(e),l.selectNodeContents(e),c.addRange(l);var C=document.execCommand("copy");if(!C)throw new Error("copy command was unsuccessful");n=!0}catch(t){o&&console.error("unable to copy using execCommand: ",t),o&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(a.format||"text",r),a.onCopy&&a.onCopy(window.clipboardData),n=!0}catch(s){o&&console.error("unable to copy using clipboardData: ",s),o&&console.error("falling back to prompt"),p=g("message"in a?a.message:m),window.prompt(p,r)}}finally{c&&(typeof c.removeRange=="function"?c.removeRange(l):c.removeAllRanges()),e&&document.body.removeChild(e),u()}return n}return i=b,i}var x=D();const T=v(x);export{T as c};
