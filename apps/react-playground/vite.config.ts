import { defineConfig, loadEnv } from "vite";
// import react from "@vitejs/plugin-react";
import qiankun from "vite-plugin-qiankun";
// https://vite.dev/config/

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "VITE_")
  return {
    base: env.VITE_BASE_PATH,
    plugins: [
      // react(), 
      qiankun("react-playground", { useDevMode: true }),
    ].filter(Boolean),
    server: {
      port: 6601,
      // cors: true,

      headers: {
        // "Access-Control-Allow-Origin": "*",
      }
    },
    build: {
      outDir: "react-playground",
      rollupOptions: {
        output: {
          chunkFileNames: "js/[name]-[hash].js",
          entryFileNames: "js/[name]-[hash].js",
          assetFileNames: "[ext]/[name]-[hash].[ext]",
          manualChunks: (id) => {
            // 分包策略：将 node_modules 中的代码单独打包成一个 JS 文件，利用缓存机制
            if (id.includes("node_modules")) {
              return id
                .toString()
                .split("node_modules/.pnpm/")[1]
                .split("/")[0]
                .toString()
            }
          },
        },
      },
    },
  }
  
});
