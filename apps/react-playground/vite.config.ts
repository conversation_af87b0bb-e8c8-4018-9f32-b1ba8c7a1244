import { defineConfig, loadEnv } from "vite";
// import react from "@vitejs/plugin-react";
import qiankun from "vite-plugin-qiankun";
// https://vite.dev/config/

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "VITE_");
  return {
    base: env.VITE_BASE_PATH,
    plugins: [
      // react(),
      qiankun("react-playground", { useDevMode: true }),
    ].filter(Boolean),
    server: {
      port: 6601,
      // cors: true,

      headers: {
        // "Access-Control-Allow-Origin": "*",
      },
    },
    build: {
      outDir: "react-playground",
      rollupOptions: {
        output: {
          chunkFileNames: "js/[name]-[hash].js",
          entryFileNames: "js/[name]-[hash].js",
          assetFileNames: "[ext]/[name]-[hash].[ext]",
          manualChunks: (id) => {
            // Monaco Editor 单独分包
            if (id.includes("monaco-editor")) {
              return "monaco-editor";
            }
            // TypeScript 单独分包
            if (id.includes("typescript") && !id.includes("@typescript")) {
              return "typescript";
            }
            // Babel 单独分包
            if (id.includes("@babel/standalone")) {
              return "babel-standalone";
            }
            // React 相关分包
            if (id.includes("react") || id.includes("react-dom")) {
              return "react-vendor";
            }
            // 其他 node_modules 分包
            if (id.includes("node_modules")) {
              return id
                .toString()
                .split("node_modules/.pnpm/")[1]
                .split("/")[0]
                .toString();
            }
          },
        },
      },
    },
    // 优化依赖预构建
    optimizeDeps: {
      include: [
        "react",
        "react-dom",
        "classnames",
        "copy-to-clipboard",
        "file-saver",
        "lodash-es",
        "fflate",
        "jszip",
      ],
      exclude: [
        "monaco-editor",
        "@monaco-editor/react",
        "typescript",
        "@babel/standalone",
        "@typescript/ata",
      ],
    },
  };
});
