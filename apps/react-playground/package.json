{"name": "react-playground", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build --mode production", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@babel/core": "^7.28.3", "@babel/standalone": "^7.28.3", "@monaco-editor/react": "4.7.0-rc.0", "@typescript/ata": "^0.9.8", "allotment": "^1.20.4", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "fflate": "^0.8.2", "file-saver": "^2.0.5", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "monaco-editor": "^0.52.2", "react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/babel__core": "^7.20.5", "@types/babel__standalone": "^7.1.9", "@types/file-saver": "^2.0.7", "@types/lodash-es": "^4.17.12", "@types/node": "^24.3.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "sass": "^1.90.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-qiankun": "^1.0.15"}}