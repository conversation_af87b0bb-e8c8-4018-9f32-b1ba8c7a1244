import { useState, createContext, useEffect } from "react";
import type { PropsWithChildren } from "react";
import { fileNameToLanguage, compress, uncompress } from "./helper/utils";
import { initFiles } from "./helper/file";
import type { Files, PlaygroundProviderProps, Theme } from "./types";
/**
 * ```ts 数据结构
 const defaultFiles = {
  "App.tsx": {
    name: "App.tsx",
    value: "import React from 'react';\n\nfunction App() {\n  return <div>Hello, world!</div>;\n}\n\nexport default App;",
    language: "typescriptreact",
  },
  "index.tsx": {
    name: "index.tsx",
    value: "import React from 'react';\nimport ReactDOM from 'react-dom';\nimport App from './App';\n\nReactDOM.render(<App />, document.getElementById('root'));",
    language: "typescriptreact",
  }
}
 * ```
 */

export const PlaygroundContext = createContext<PlaygroundProviderProps>({
  selectedFileName: "App.tsx",
} as PlaygroundProviderProps);

export const PlaygroundProvider = (props: PropsWithChildren) => {
  const { children } = props;

  const [files, setFiles] = useState<Files>(getFilesFromUrl() || initFiles);
  const [theme, setTheme] = useState<Theme>("light");
  const [selectedFileName, setSelectedFileName] = useState("App.tsx");

  const addFile = (name: string) => {
    files[name] = {
      name,
      language: fileNameToLanguage(name),
      value: "",
    };
    setFiles({ ...files });
  };

  const removeFile = (name: string) => {
    delete files[name];
    setFiles({ ...files });
  };

  const updateFileName = (oldFieldName: string, newFieldName: string) => {
    if (
      !files[oldFieldName] ||
      newFieldName === undefined ||
      newFieldName === null
    )
      return;

    if (oldFieldName === newFieldName) return;

    const { [oldFieldName]: value, ...rest } = files;

    const newFile = {
      [newFieldName]: {
        ...value,
        language: fileNameToLanguage(newFieldName),
        name: newFieldName,
      },
    };

    setFiles({
      ...rest,
      ...newFile,
    });
  };

  function getFilesFromUrl() {
    let files: Files | undefined;
    try {
      const hash = uncompress(window.location.hash.slice(1));
      files = JSON.parse(hash);
    } catch (error) {
      console.error(error);
    }
    console.log("files======>", files)
    return files;
  }

  /** 用于分享链接 */
  useEffect(() => {
    const hash = compress(JSON.stringify(files));
    window.location.hash = hash;
  }, [files]);

  return (
    <PlaygroundContext.Provider
      value={{
        theme,
        setTheme,
        files,
        selectedFileName,
        setSelectedFileName,
        setFiles,
        addFile,
        removeFile,
        updateFileName,
      }}
    >
      {children}
    </PlaygroundContext.Provider>
  );
};
