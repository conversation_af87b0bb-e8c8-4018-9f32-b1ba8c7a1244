import React, { useContext } from "react";
import LazyEditor from "./lazy-editor";
import FileNameList from "./file-name-list";
import styles from "./index.module.scss";
import { PlaygroundContext } from "../../playgroundContext";
import { debounce, isNil } from "lodash-es";

const CodeEditor: React.FC = () => {
  const context = useContext(PlaygroundContext);

  const { files, selectedFileName, setFiles } = context;

  const selectFileInfo = files[selectedFileName];

  function onEditorChange(value?: string) {
    if (isNil(value)) return;
    files[selectedFileName].value = value;
    setFiles({ ...files });
  }
  return (
    <div className={styles.CodeEditor}>
      <div className="file-name-list">
        <FileNameList />
      </div>
      <div className="editor">
        <LazyEditor
          file={selectFileInfo}
          onChange={debounce(onEditorChange, 500)}
        />
      </div>
    </div>
  );
};

export default CodeEditor;
