import { useContext } from "react";
import MonacoEditor from "@monaco-editor/react";
import type {
  EditorProps,
  OnMount,
} from "@monaco-editor/react";
import { PlaygroundContext } from "../../playgroundContext";
import { editor } from "monaco-editor";
import { createATA } from "./ata";

export interface EditorFile {
  name: string;
  value: string;
  language: string;
}

interface Props {
  file: EditorFile;
  onChange?: EditorProps["onChange"];
  options?: editor.IStandaloneEditorConstructionOptions;
}

export default function Editor(props: Props) {
  const { file, onChange, options = {} } = props;
  const { theme } = useContext(PlaygroundContext);

  const handleEditorMount: OnMount = (editor, monaco) => {
    // cmd + j 格式化代码（无效）
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyK, () => {
      editor.getAction("editor.action.formatDocument")?.run();
    });
    // 自动下载类型定义文件
    const ata = createATA((code, path) => {
      monaco.languages.typescript.typescriptDefaults.addExtraLib(
        code,
        `file://${path}`
      );
    });
    // 设置 ts 的默认 compilerOptions
    monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
      jsx: monaco.languages.typescript.JsxEmit.Preserve,
      esModuleInterop: true,
    });

    // 监听代码变化，下载类型定义文件
    editor.onDidChangeModelContent(() => {
      ata(editor.getValue());
    });

    // 初始化时下载类型定义文件
    ata(editor.getValue());
  };

  return (
    <MonacoEditor
      height="100%"
      path={file.name}
      language={file.language}
      // onMount 也就是编辑器加载完的回调，设置 ts 的默认 compilerOptions。
      onMount={handleEditorMount}
      value={file.value}
      onChange={onChange}
      theme={`vs-${theme}`}
      options={{
        fontSize: 14,
        // 关闭滚动条
        scrollBeyondLastLine: false,
        // 不需要缩略图
        minimap: {
          enabled: false,
        },
        // 设置滚动条参数
        scrollbar: {
          verticalScrollbarSize: 6,
          horizontalScrollbarSize: 6,
        },
        ...options,
      }}
    />
  );
}
