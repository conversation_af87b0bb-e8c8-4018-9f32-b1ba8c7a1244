import { lazy, Suspense } from "react";
import type { EditorFile } from "./editor";
import type { editor } from "monaco-editor";

// 懒加载 Monaco Editor 组件
const LazyMonacoEditor = lazy(() => import("./editor"));

interface Props {
  file: EditorFile;
  onChange?: (value?: string) => void;
  options?: editor.IStandaloneEditorConstructionOptions;
}

// 加载中的占位组件
function EditorSkeleton() {
  return (
    <div 
      style={{
        width: "100%",
        height: "100%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#1e1e1e",
        color: "#cccccc",
        fontSize: "14px"
      }}
    >
      <div>
        <div style={{ marginBottom: "8px" }}>正在加载编辑器...</div>
        <div style={{ 
          width: "200px", 
          height: "4px", 
          backgroundColor: "#333", 
          borderRadius: "2px",
          overflow: "hidden"
        }}>
          <div 
            style={{
              width: "100%",
              height: "100%",
              background: "linear-gradient(90deg, transparent, #007acc, transparent)",
              animation: "loading 1.5s infinite"
            }}
          />
        </div>
      </div>
      <style>{`
        @keyframes loading {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
      `}</style>
    </div>
  );
}

export default function LazyEditor(props: Props) {
  return (
    <Suspense fallback={<EditorSkeleton />}>
      <LazyMonacoEditor {...props} />
    </Suspense>
  );
}
