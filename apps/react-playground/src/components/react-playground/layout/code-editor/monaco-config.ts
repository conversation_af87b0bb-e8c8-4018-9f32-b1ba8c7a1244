import { loader } from "@monaco-editor/react";

// 配置 Monaco Editor 的 CDN 路径，减少打包体积
export function configureMonaco() {
  // 使用 CDN 加载 Monaco Editor
  loader.config({
    paths: {
      vs: "https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs",
    },
  });

  // 预加载 Monaco Editor
  loader.init().then((monacoInstance) => {
    // 配置 TypeScript 编译选项
    monacoInstance.languages.typescript.typescriptDefaults.setCompilerOptions({
      target: monacoInstance.languages.typescript.ScriptTarget.ES2020,
      allowNonTsExtensions: true,
      moduleResolution:
        monacoInstance.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monacoInstance.languages.typescript.ModuleKind.ESNext,
      noEmit: true,
      esModuleInterop: true,
      jsx: monacoInstance.languages.typescript.JsxEmit.ReactJSX,
      allowJs: true,
      typeRoots: ["node_modules/@types"],
    });

    // 配置 JavaScript 编译选项
    monacoInstance.languages.typescript.javascriptDefaults.setCompilerOptions({
      target: monacoInstance.languages.typescript.ScriptTarget.ES2020,
      allowNonTsExtensions: true,
      moduleResolution:
        monacoInstance.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monacoInstance.languages.typescript.ModuleKind.ESNext,
      noEmit: true,
      esModuleInterop: true,
      allowJs: true,
      jsx: monacoInstance.languages.typescript.JsxEmit.ReactJSX,
    });

    // 禁用不需要的功能以减少体积
    monacoInstance.languages.typescript.typescriptDefaults.setDiagnosticsOptions(
      {
        noSemanticValidation: false,
        noSyntaxValidation: false,
        noSuggestionDiagnostics: true,
      }
    );

    monacoInstance.languages.typescript.javascriptDefaults.setDiagnosticsOptions(
      {
        noSemanticValidation: false,
        noSyntaxValidation: false,
        noSuggestionDiagnostics: true,
      }
    );
  });
}

// 懒加载 Monaco Editor 的函数
export async function loadMonacoEditor() {
  const monacoInstance = await loader.init();
  return monacoInstance;
}
