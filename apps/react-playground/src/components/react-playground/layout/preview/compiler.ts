import { transform } from "@babel/standalone";
import type { Files, File } from "../../types";
import { ENTRY_FILE_NAME } from "../../helper/file";

import type { PluginObj } from "@babel/core";

/**
 * 编译前处理代码（判断是否存在 import React from 'react)
 * @param filename 文件名
 * @param code 代码
 * @returns
 */
export const beforeTransformCode = (filename: string, code: string) => {
  let _code = code;
  const regexReact = /import\s+React\b/g;
  if (
    (filename.endsWith(".jsx") || filename.endsWith(".tsx")) &&
    !regexReact.test(code)
  ) {
    _code = `import React from 'react';\n${code}`;
  }
  return _code;
};

/**
 * 编译代码
 * @param filename 文件名
 * @param code 代码
 * @param files 文件列表
 * @returns
 */
export const babelTransform = (
  filename: string,
  code: string,
  files: Files
) => {
  let result = "";
  try {
    const _code = beforeTransformCode(filename, code);
    result = transform(_code, {
      presets: ["react", "typescript"],
      filename,
      plugins: [customResolver(files)],
      // 编译后保持原有行列号不变
      retainLines: true,
    }).code!;
  } catch (e) {
    console.error("编译出错", e);
  }
  return result;
};

/**
 * 编译文件
 * @param files 文件列表
 * @returns
 */
export const compile = (files: Files) => {
  const main = files[ENTRY_FILE_NAME];
  const code = babelTransform(ENTRY_FILE_NAME, main.value, files);
  return code;
};

/**
 * 编译 json 文件
 * @param file json 文件
 * @returns
 */
function jsonToJs(file: File) {
  const js = `export default ${file.value}`;
  return URL.createObjectURL(
    new Blob([js], {
      type: "application/json",
    })
  );
}

/**
 * 编译 css 文件
 * @param file css 文件
 * @returns
 */
function cssToJs(file: File) {
  const randomId = new Date().getTime();
  const js = `(() => {
  const styleSheet = document.createElement("style");
  styleSheet.setAttribute("id", 'style_${randomId}_${file.name}');
  document.head.appendChild(styleSheet);

  const styles = document.createTextNode(\`${file.value}\`);
  styleSheet.innerHTML = "";
  styleSheet.appendChild(styles);
  })()`;
  return URL.createObjectURL(
    new Blob([js], {
      type: "text/javascript",
    })
  );
}

/**
 * 自定义解析器, 用于解析 import 语句中的文件路径为 Blob Url
 * @param file
 * @returns
 */
export function customResolver(files: Files): PluginObj {
  return {
    visitor: {
      ImportDeclaration(path) {
        const modulePath = path.node.source.value;
        if (modulePath.startsWith(".")) {
          const file = getModuleFile(files, modulePath);
          if (!file) return;
          if (file.name.endsWith(".css")) {
            path.node.source.value = cssToJs(file);
          } else if (file.name.endsWith(".json")) {
            path.node.source.value = jsonToJs(file);
          } else {
            path.node.source.value = URL.createObjectURL(
              new Blob([babelTransform(file.name, file.value, files)], {
                type: "application/javascript",
              })
            );
          }
        }
      },
    },
  };
}

/**
 * 获取模块文件
 * @param files 所有文件
 * @param modulePath 模块路径
 * @returns
 */
export function getModuleFile(files: Files, modulePath: string) {
  let moduleName = modulePath.split("./").pop() || "";
  // 针对没有后缀名的
  if (!moduleName.includes(".")) {
    const realModuleName = Object.keys(files)
      .filter((key) => {
        return (
          key.endsWith(".ts") ||
          key.endsWith(".tsx") ||
          key.endsWith(".js") ||
          key.endsWith(".jsx")
        );
      })
      .find((key) => {
        return key.split(".").includes(moduleName);
      });
    if (realModuleName) {
      moduleName = realModuleName;
    }
  }
  return files[moduleName];
}
