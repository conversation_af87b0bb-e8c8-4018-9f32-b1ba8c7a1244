import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.tsx";
import {
  renderWithQiankun,
  qiankunWindow,
  type QiankunProps,
} from "vite-plugin-qiankun/dist/helper";


let root: ReturnType<typeof createRoot> | null = null;

function render(props: QiankunProps = {}) {
  const { container } = props;
  const dom = container
    ? container.querySelector("#root")
    : document.querySelector("#root");

  if (!root) {
    root = createRoot(dom!);
  }

  root.render(<App />);
}

function initApp() {
  if (!qiankunWindow.__POWERED_BY_QIANKUN__) {
    console.log("%c 独立渲染", "color: red; font-size: 20px;");
    render();
    return;
  }
  renderWithQiankun({
    mount(props) {
      console.log("%c qiankun 渲染", "color: red; font-size: 20px;");
      console.log("Qiankun mount", props);
      render(props);
    },
    bootstrap() {
      console.log("Qiankun bootstrap");
    },
    unmount(props) {
      console.log("Qiankun unmount", props);
      if (root) {
        root.unmount();
        root = null;
      }
    },
    update(props) {
      console.log("Qiankun update", props);
    },
  });
}

initApp();
