<script setup lang="ts">
import { ElRow, ElCol, ElCard, ElTimeline, ElTimelineItem } from "element-plus";
import { MoreFilled } from "@element-plus/icons-vue";
import { ref } from "vue";
// import mainQiankunApp from "./qiankunApp";
import "./qiankun";

const subProjectList = ref([
  {
    title: "React Playground",
    desc: "在线编辑，在线预览",
    path: import.meta.env.VITE_REACT_PLAYGROUND_ROUTE_PATH,
  },
  {
    title: "乾坤通信",
    desc: "主应用与微应用之间的联系",
    path: "/qiankun-communication",
  },
]);

const loadProject = (path: string) => {
  // mainQiankunApp.navigateTo(path);
  console.log("path======>", path, history);
  history.pushState({}, "", path);
};
</script>

<template>
  <div class="app">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="card-common">
          <template #header>
            <div>
              <span>Copyer 项目集合</span>
            </div>
          </template>
          <div class="card-body">
            <el-timeline>
              <el-timeline-item
                v-for="item in subProjectList"
                :key="item.title"
                color="#0bbd87"
                center
                placement="top"
              >
                <el-card class="timeline-card" @click="loadProject(item.path)">
                  <h4>{{ item.title }}</h4>
                  <p>{{ item.desc }}</p>
                </el-card>
              </el-timeline-item>
              <el-timeline-item type="primary" :icon="MoreFilled">
                敬请期待更多...
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card class="card-common">
          <template #header>
            <div style="text-align: right">
              <span>项目演示</span>
            </div>
          </template>
          <div class="card-body">
            <div id="__qiankun_container"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped>
.app {
  height: 100vh;
  width: 100vw;
  padding: 10px;
  box-sizing: border-box;
}
.card-left {
  height: 100%;
  overflow: visible;
}
.card-common :deep(.el-card__header) {
  font-size: 20px;
  height: 60px;
  line-height: 60px;
  background-color: #64b687;
  color: #fff;
  font-weight: 700;
  padding: 0 20px 0 20px;
  user-select: none;
  box-sizing: border-box;
}
.card-common ::deep(.el-card__body) {
  padding: 10px;
  box-sizing: border-box;
}
.card-body {
  height: calc(100vh - 120px);
  overflow-y: auto;
}
.timeline-card:hover {
  background-color: #64b68720;
  cursor: pointer;
}
.timeline-card:hover * {
  /* 重置内部元素的 hover 效果 */
  background-color: transparent;
}
</style>
