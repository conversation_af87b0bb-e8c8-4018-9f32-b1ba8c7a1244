(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))n(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&n(a)}).observe(document,{childList:!0,subtree:!0});function r(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function n(o){if(o.ep)return;o.ep=!0;const i=r(o);fetch(o.href,i)}})();/**
* @vue/shared v3.5.18
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Hu(e){const t=Object.create(null);for(const r of e.split(","))t[r]=1;return r=>r in t}const de={},dr=[],ze=()=>{},pm=()=>!1,zn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ku=e=>e.startsWith("onUpdate:"),Le=Object.assign,Vu=(e,t)=>{const r=e.indexOf(t);r>-1&&e.splice(r,1)},hm=Object.prototype.hasOwnProperty,ae=(e,t)=>hm.call(e,t),K=Array.isArray,pr=e=>Xn(e)==="[object Map]",Ah=e=>Xn(e)==="[object Set]",J=e=>typeof e=="function",me=e=>typeof e=="string",Pt=e=>typeof e=="symbol",le=e=>e!==null&&typeof e=="object",Rh=e=>(le(e)||J(e))&&J(e.then)&&J(e.catch),Mh=Object.prototype.toString,Xn=e=>Mh.call(e),vm=e=>Xn(e).slice(8,-1),Ih=e=>Xn(e)==="[object Object]",Bu=e=>me(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Vr=Hu(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Jn=e=>{const t=Object.create(null);return r=>t[r]||(t[r]=e(r))},gm=/-(\w)/g,Je=Jn(e=>e.replace(gm,(t,r)=>r?r.toUpperCase():"")),mm=/\B([A-Z])/g,ir=Jn(e=>e.replace(mm,"-$1").toLowerCase()),Qn=Jn(e=>e.charAt(0).toUpperCase()+e.slice(1)),wo=Jn(e=>e?`on${Qn(e)}`:""),Nt=(e,t)=>!Object.is(e,t),So=(e,...t)=>{for(let r=0;r<e.length;r++)e[r](...t)},uu=(e,t,r,n=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:n,value:r})},ym=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Dc;const Zn=()=>Dc||(Dc=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function ar(e){if(K(e)){const t={};for(let r=0;r<e.length;r++){const n=e[r],o=me(n)?Sm(n):ar(n);if(o)for(const i in o)t[i]=o[i]}return t}else if(me(e)||le(e))return e}const bm=/;(?![^(]*\))/g,_m=/:([^]+)/,wm=/\/\*[^]*?\*\//g;function Sm(e){const t={};return e.replace(wm,"").split(bm).forEach(r=>{if(r){const n=r.split(_m);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function Ee(e){let t="";if(me(e))t=e;else if(K(e))for(let r=0;r<e.length;r++){const n=Ee(e[r]);n&&(t+=n+" ")}else if(le(e))for(const r in e)e[r]&&(t+=r+" ");return t.trim()}const Em="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Tm=Hu(Em);function Lh(e){return!!e||e===""}const qh=e=>!!(e&&e.__v_isRef===!0),nr=e=>me(e)?e:e==null?"":K(e)||le(e)&&(e.toString===Mh||!J(e.toString))?qh(e)?nr(e.value):JSON.stringify(e,jh,2):String(e),jh=(e,t)=>qh(t)?jh(e,t.value):pr(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((r,[n,o],i)=>(r[Eo(n,i)+" =>"]=o,r),{})}:Ah(t)?{[`Set(${t.size})`]:[...t.values()].map(r=>Eo(r))}:Pt(t)?Eo(t):le(t)&&!K(t)&&!Ih(t)?String(t):t,Eo=(e,t="")=>{var r;return Pt(e)?`Symbol(${(r=e.description)!=null?r:t})`:e};/**
* @vue/reactivity v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let He;class xm{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=He,!t&&He&&(this.index=(He.scopes||(He.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,r;if(this.scopes)for(t=0,r=this.scopes.length;t<r;t++)this.scopes[t].pause();for(t=0,r=this.effects.length;t<r;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,r;if(this.scopes)for(t=0,r=this.scopes.length;t<r;t++)this.scopes[t].resume();for(t=0,r=this.effects.length;t<r;t++)this.effects[t].resume()}}run(t){if(this._active){const r=He;try{return He=this,t()}finally{He=r}}}on(){++this._on===1&&(this.prevScope=He,He=this)}off(){this._on>0&&--this._on===0&&(He=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let r,n;for(r=0,n=this.effects.length;r<n;r++)this.effects[r].stop();for(this.effects.length=0,r=0,n=this.cleanups.length;r<n;r++)this.cleanups[r]();if(this.cleanups.length=0,this.scopes){for(r=0,n=this.scopes.length;r<n;r++)this.scopes[r].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function Om(){return He}let fe;const To=new WeakSet;class Dh{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,He&&He.active&&He.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,To.has(this)&&(To.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Nh(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Gc(this),Fh(this);const t=fe,r=et;fe=this,et=!0;try{return this.fn()}finally{Hh(this),fe=t,et=r,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Uu(t);this.deps=this.depsTail=void 0,Gc(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?To.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){cu(this)&&this.run()}get dirty(){return cu(this)}}let Gh=0,Br,$r;function Nh(e,t=!1){if(e.flags|=8,t){e.next=$r,$r=e;return}e.next=Br,Br=e}function $u(){Gh++}function Wu(){if(--Gh>0)return;if($r){let t=$r;for($r=void 0;t;){const r=t.next;t.next=void 0,t.flags&=-9,t=r}}let e;for(;Br;){let t=Br;for(Br=void 0;t;){const r=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(n){e||(e=n)}t=r}}if(e)throw e}function Fh(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Hh(e){let t,r=e.depsTail,n=r;for(;n;){const o=n.prevDep;n.version===-1?(n===r&&(r=o),Uu(n),Cm(n)):t=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=o}e.deps=t,e.depsTail=r}function cu(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(kh(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function kh(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Xr)||(e.globalVersion=Xr,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!cu(e))))return;e.flags|=2;const t=e.dep,r=fe,n=et;fe=e,et=!0;try{Fh(e);const o=e.fn(e._value);(t.version===0||Nt(o,e._value))&&(e.flags|=128,e._value=o,t.version++)}catch(o){throw t.version++,o}finally{fe=r,et=n,Hh(e),e.flags&=-3}}function Uu(e,t=!1){const{dep:r,prevSub:n,nextSub:o}=e;if(n&&(n.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=n,e.nextSub=void 0),r.subs===e&&(r.subs=n,!n&&r.computed)){r.computed.flags&=-5;for(let i=r.computed.deps;i;i=i.nextDep)Uu(i,!0)}!t&&!--r.sc&&r.map&&r.map.delete(r.key)}function Cm(e){const{prevDep:t,nextDep:r}=e;t&&(t.nextDep=r,e.prevDep=void 0),r&&(r.prevDep=t,e.nextDep=void 0)}let et=!0;const Vh=[];function Tt(){Vh.push(et),et=!1}function xt(){const e=Vh.pop();et=e===void 0?!0:e}function Gc(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const r=fe;fe=void 0;try{t()}finally{fe=r}}}let Xr=0;class Pm{constructor(t,r){this.sub=t,this.dep=r,this.version=r.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ku{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!fe||!et||fe===this.computed)return;let r=this.activeLink;if(r===void 0||r.sub!==fe)r=this.activeLink=new Pm(fe,this),fe.deps?(r.prevDep=fe.depsTail,fe.depsTail.nextDep=r,fe.depsTail=r):fe.deps=fe.depsTail=r,Bh(r);else if(r.version===-1&&(r.version=this.version,r.nextDep)){const n=r.nextDep;n.prevDep=r.prevDep,r.prevDep&&(r.prevDep.nextDep=n),r.prevDep=fe.depsTail,r.nextDep=void 0,fe.depsTail.nextDep=r,fe.depsTail=r,fe.deps===r&&(fe.deps=n)}return r}trigger(t){this.version++,Xr++,this.notify(t)}notify(t){$u();try{for(let r=this.subs;r;r=r.prevSub)r.sub.notify()&&r.sub.dep.notify()}finally{Wu()}}}function Bh(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let n=t.deps;n;n=n.nextDep)Bh(n)}const r=e.dep.subs;r!==e&&(e.prevSub=r,r&&(r.nextSub=e)),e.dep.subs=e}}const lu=new WeakMap,tr=Symbol(""),fu=Symbol(""),Jr=Symbol("");function Ae(e,t,r){if(et&&fe){let n=lu.get(e);n||lu.set(e,n=new Map);let o=n.get(r);o||(n.set(r,o=new Ku),o.map=n,o.key=r),o.track()}}function _t(e,t,r,n,o,i){const a=lu.get(e);if(!a){Xr++;return}const s=u=>{u&&u.trigger()};if($u(),t==="clear")a.forEach(s);else{const u=K(e),c=u&&Bu(r);if(u&&r==="length"){const l=Number(n);a.forEach((f,p)=>{(p==="length"||p===Jr||!Pt(p)&&p>=l)&&s(f)})}else switch((r!==void 0||a.has(void 0))&&s(a.get(r)),c&&s(a.get(Jr)),t){case"add":u?c&&s(a.get("length")):(s(a.get(tr)),pr(e)&&s(a.get(fu)));break;case"delete":u||(s(a.get(tr)),pr(e)&&s(a.get(fu)));break;case"set":pr(e)&&s(a.get(tr));break}}Wu()}function ur(e){const t=ue(e);return t===e?t:(Ae(t,"iterate",Jr),Xe(e)?t:t.map(xe))}function eo(e){return Ae(e=ue(e),"iterate",Jr),e}const Am={__proto__:null,[Symbol.iterator](){return xo(this,Symbol.iterator,xe)},concat(...e){return ur(this).concat(...e.map(t=>K(t)?ur(t):t))},entries(){return xo(this,"entries",e=>(e[1]=xe(e[1]),e))},every(e,t){return mt(this,"every",e,t,void 0,arguments)},filter(e,t){return mt(this,"filter",e,t,r=>r.map(xe),arguments)},find(e,t){return mt(this,"find",e,t,xe,arguments)},findIndex(e,t){return mt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return mt(this,"findLast",e,t,xe,arguments)},findLastIndex(e,t){return mt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return mt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Oo(this,"includes",e)},indexOf(...e){return Oo(this,"indexOf",e)},join(e){return ur(this).join(e)},lastIndexOf(...e){return Oo(this,"lastIndexOf",e)},map(e,t){return mt(this,"map",e,t,void 0,arguments)},pop(){return Ir(this,"pop")},push(...e){return Ir(this,"push",e)},reduce(e,...t){return Nc(this,"reduce",e,t)},reduceRight(e,...t){return Nc(this,"reduceRight",e,t)},shift(){return Ir(this,"shift")},some(e,t){return mt(this,"some",e,t,void 0,arguments)},splice(...e){return Ir(this,"splice",e)},toReversed(){return ur(this).toReversed()},toSorted(e){return ur(this).toSorted(e)},toSpliced(...e){return ur(this).toSpliced(...e)},unshift(...e){return Ir(this,"unshift",e)},values(){return xo(this,"values",xe)}};function xo(e,t,r){const n=eo(e),o=n[t]();return n!==e&&!Xe(e)&&(o._next=o.next,o.next=()=>{const i=o._next();return i.value&&(i.value=r(i.value)),i}),o}const Rm=Array.prototype;function mt(e,t,r,n,o,i){const a=eo(e),s=a!==e&&!Xe(e),u=a[t];if(u!==Rm[t]){const f=u.apply(e,i);return s?xe(f):f}let c=r;a!==e&&(s?c=function(f,p){return r.call(this,xe(f),p,e)}:r.length>2&&(c=function(f,p){return r.call(this,f,p,e)}));const l=u.call(a,c,n);return s&&o?o(l):l}function Nc(e,t,r,n){const o=eo(e);let i=r;return o!==e&&(Xe(e)?r.length>3&&(i=function(a,s,u){return r.call(this,a,s,u,e)}):i=function(a,s,u){return r.call(this,a,xe(s),u,e)}),o[t](i,...n)}function Oo(e,t,r){const n=ue(e);Ae(n,"iterate",Jr);const o=n[t](...r);return(o===-1||o===!1)&&Ju(r[0])?(r[0]=ue(r[0]),n[t](...r)):o}function Ir(e,t,r=[]){Tt(),$u();const n=ue(e)[t].apply(e,r);return Wu(),xt(),n}const Mm=Hu("__proto__,__v_isRef,__isVue"),$h=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Pt));function Im(e){Pt(e)||(e=String(e));const t=ue(this);return Ae(t,"has",e),t.hasOwnProperty(e)}class Wh{constructor(t=!1,r=!1){this._isReadonly=t,this._isShallow=r}get(t,r,n){if(r==="__v_skip")return t.__v_skip;const o=this._isReadonly,i=this._isShallow;if(r==="__v_isReactive")return!o;if(r==="__v_isReadonly")return o;if(r==="__v_isShallow")return i;if(r==="__v_raw")return n===(o?i?Vm:zh:i?Yh:Kh).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const a=K(t);if(!o){let u;if(a&&(u=Am[r]))return u;if(r==="hasOwnProperty")return Im}const s=Reflect.get(t,r,Me(t)?t:n);return(Pt(r)?$h.has(r):Mm(r))||(o||Ae(t,"get",r),i)?s:Me(s)?a&&Bu(r)?s:s.value:le(s)?o?Xh(s):zu(s):s}}class Uh extends Wh{constructor(t=!1){super(!1,t)}set(t,r,n,o){let i=t[r];if(!this._isShallow){const u=Ht(i);if(!Xe(n)&&!Ht(n)&&(i=ue(i),n=ue(n)),!K(t)&&Me(i)&&!Me(n))return u?!1:(i.value=n,!0)}const a=K(t)&&Bu(r)?Number(r)<t.length:ae(t,r),s=Reflect.set(t,r,n,Me(t)?t:o);return t===ue(o)&&(a?Nt(n,i)&&_t(t,"set",r,n):_t(t,"add",r,n)),s}deleteProperty(t,r){const n=ae(t,r);t[r];const o=Reflect.deleteProperty(t,r);return o&&n&&_t(t,"delete",r,void 0),o}has(t,r){const n=Reflect.has(t,r);return(!Pt(r)||!$h.has(r))&&Ae(t,"has",r),n}ownKeys(t){return Ae(t,"iterate",K(t)?"length":tr),Reflect.ownKeys(t)}}class Lm extends Wh{constructor(t=!1){super(!0,t)}set(t,r){return!0}deleteProperty(t,r){return!0}}const qm=new Uh,jm=new Lm,Dm=new Uh(!0);const du=e=>e,_n=e=>Reflect.getPrototypeOf(e);function Gm(e,t,r){return function(...n){const o=this.__v_raw,i=ue(o),a=pr(i),s=e==="entries"||e===Symbol.iterator&&a,u=e==="keys"&&a,c=o[e](...n),l=r?du:t?qn:xe;return!t&&Ae(i,"iterate",u?fu:tr),{next(){const{value:f,done:p}=c.next();return p?{value:f,done:p}:{value:s?[l(f[0]),l(f[1])]:l(f),done:p}},[Symbol.iterator](){return this}}}}function wn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Nm(e,t){const r={get(o){const i=this.__v_raw,a=ue(i),s=ue(o);e||(Nt(o,s)&&Ae(a,"get",o),Ae(a,"get",s));const{has:u}=_n(a),c=t?du:e?qn:xe;if(u.call(a,o))return c(i.get(o));if(u.call(a,s))return c(i.get(s));i!==a&&i.get(o)},get size(){const o=this.__v_raw;return!e&&Ae(ue(o),"iterate",tr),Reflect.get(o,"size",o)},has(o){const i=this.__v_raw,a=ue(i),s=ue(o);return e||(Nt(o,s)&&Ae(a,"has",o),Ae(a,"has",s)),o===s?i.has(o):i.has(o)||i.has(s)},forEach(o,i){const a=this,s=a.__v_raw,u=ue(s),c=t?du:e?qn:xe;return!e&&Ae(u,"iterate",tr),s.forEach((l,f)=>o.call(i,c(l),c(f),a))}};return Le(r,e?{add:wn("add"),set:wn("set"),delete:wn("delete"),clear:wn("clear")}:{add(o){!t&&!Xe(o)&&!Ht(o)&&(o=ue(o));const i=ue(this);return _n(i).has.call(i,o)||(i.add(o),_t(i,"add",o,o)),this},set(o,i){!t&&!Xe(i)&&!Ht(i)&&(i=ue(i));const a=ue(this),{has:s,get:u}=_n(a);let c=s.call(a,o);c||(o=ue(o),c=s.call(a,o));const l=u.call(a,o);return a.set(o,i),c?Nt(i,l)&&_t(a,"set",o,i):_t(a,"add",o,i),this},delete(o){const i=ue(this),{has:a,get:s}=_n(i);let u=a.call(i,o);u||(o=ue(o),u=a.call(i,o)),s&&s.call(i,o);const c=i.delete(o);return u&&_t(i,"delete",o,void 0),c},clear(){const o=ue(this),i=o.size!==0,a=o.clear();return i&&_t(o,"clear",void 0,void 0),a}}),["keys","values","entries",Symbol.iterator].forEach(o=>{r[o]=Gm(o,e,t)}),r}function Yu(e,t){const r=Nm(e,t);return(n,o,i)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?n:Reflect.get(ae(r,o)&&o in n?r:n,o,i)}const Fm={get:Yu(!1,!1)},Hm={get:Yu(!1,!0)},km={get:Yu(!0,!1)};const Kh=new WeakMap,Yh=new WeakMap,zh=new WeakMap,Vm=new WeakMap;function Bm(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function $m(e){return e.__v_skip||!Object.isExtensible(e)?0:Bm(vm(e))}function zu(e){return Ht(e)?e:Xu(e,!1,qm,Fm,Kh)}function Wm(e){return Xu(e,!1,Dm,Hm,Yh)}function Xh(e){return Xu(e,!0,jm,km,zh)}function Xu(e,t,r,n,o){if(!le(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=$m(e);if(i===0)return e;const a=o.get(e);if(a)return a;const s=new Proxy(e,i===2?n:r);return o.set(e,s),s}function hr(e){return Ht(e)?hr(e.__v_raw):!!(e&&e.__v_isReactive)}function Ht(e){return!!(e&&e.__v_isReadonly)}function Xe(e){return!!(e&&e.__v_isShallow)}function Ju(e){return e?!!e.__v_raw:!1}function ue(e){const t=e&&e.__v_raw;return t?ue(t):e}function Um(e){return!ae(e,"__v_skip")&&Object.isExtensible(e)&&uu(e,"__v_skip",!0),e}const xe=e=>le(e)?zu(e):e,qn=e=>le(e)?Xh(e):e;function Me(e){return e?e.__v_isRef===!0:!1}function jn(e){return Km(e,!1)}function Km(e,t){return Me(e)?e:new Ym(e,t)}class Ym{constructor(t,r){this.dep=new Ku,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=r?t:ue(t),this._value=r?t:xe(t),this.__v_isShallow=r}get value(){return this.dep.track(),this._value}set value(t){const r=this._rawValue,n=this.__v_isShallow||Xe(t)||Ht(t);t=n?t:ue(t),Nt(t,r)&&(this._rawValue=t,this._value=n?t:xe(t),this.dep.trigger())}}function ee(e){return Me(e)?e.value:e}const zm={get:(e,t,r)=>t==="__v_raw"?e:ee(Reflect.get(e,t,r)),set:(e,t,r,n)=>{const o=e[t];return Me(o)&&!Me(r)?(o.value=r,!0):Reflect.set(e,t,r,n)}};function Jh(e){return hr(e)?e:new Proxy(e,zm)}class Xm{constructor(t,r,n){this.fn=t,this.setter=r,this._value=void 0,this.dep=new Ku(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Xr-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!r,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&fe!==this)return Nh(this,!0),!0}get value(){const t=this.dep.track();return kh(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Jm(e,t,r=!1){let n,o;return J(e)?n=e:(n=e.get,o=e.set),new Xm(n,o,r)}const Sn={},Dn=new WeakMap;let Jt;function Qm(e,t=!1,r=Jt){if(r){let n=Dn.get(r);n||Dn.set(r,n=[]),n.push(e)}}function Zm(e,t,r=de){const{immediate:n,deep:o,once:i,scheduler:a,augmentJob:s,call:u}=r,c=E=>o?E:Xe(E)||o===!1||o===0?qt(E,1):qt(E);let l,f,p,v,y=!1,m=!1;if(Me(e)?(f=()=>e.value,y=Xe(e)):hr(e)?(f=()=>c(e),y=!0):K(e)?(m=!0,y=e.some(E=>hr(E)||Xe(E)),f=()=>e.map(E=>{if(Me(E))return E.value;if(hr(E))return c(E);if(J(E))return u?u(E,2):E()})):J(e)?t?f=u?()=>u(e,2):e:f=()=>{if(p){Tt();try{p()}finally{xt()}}const E=Jt;Jt=l;try{return u?u(e,3,[v]):e(v)}finally{Jt=E}}:f=ze,t&&o){const E=f,M=o===!0?1/0:o;f=()=>qt(E(),M)}const h=Om(),b=()=>{l.stop(),h&&h.active&&Vu(h.effects,l)};if(i&&t){const E=t;t=(...M)=>{E(...M),b()}}let _=m?new Array(e.length).fill(Sn):Sn;const O=E=>{if(!(!(l.flags&1)||!l.dirty&&!E))if(t){const M=l.run();if(o||y||(m?M.some((L,F)=>Nt(L,_[F])):Nt(M,_))){p&&p();const L=Jt;Jt=l;try{const F=[M,_===Sn?void 0:m&&_[0]===Sn?[]:_,v];_=M,u?u(t,3,F):t(...F)}finally{Jt=L}}}else l.run()};return s&&s(O),l=new Dh(f),l.scheduler=a?()=>a(O,!1):O,v=E=>Qm(E,!1,l),p=l.onStop=()=>{const E=Dn.get(l);if(E){if(u)u(E,4);else for(const M of E)M();Dn.delete(l)}},t?n?O(!0):_=l.run():a?a(O.bind(null,!0),!0):l.run(),b.pause=l.pause.bind(l),b.resume=l.resume.bind(l),b.stop=b,b}function qt(e,t=1/0,r){if(t<=0||!le(e)||e.__v_skip||(r=r||new Set,r.has(e)))return e;if(r.add(e),t--,Me(e))qt(e.value,t,r);else if(K(e))for(let n=0;n<e.length;n++)qt(e[n],t,r);else if(Ah(e)||pr(e))e.forEach(n=>{qt(n,t,r)});else if(Ih(e)){for(const n in e)qt(e[n],t,r);for(const n of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,n)&&qt(e[n],t,r)}return e}/**
* @vue/runtime-core v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function cn(e,t,r,n){try{return n?e(...n):e()}catch(o){to(o,t,r)}}function ht(e,t,r,n){if(J(e)){const o=cn(e,t,r,n);return o&&Rh(o)&&o.catch(i=>{to(i,t,r)}),o}if(K(e)){const o=[];for(let i=0;i<e.length;i++)o.push(ht(e[i],t,r,n));return o}}function to(e,t,r,n=!0){const o=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:a}=t&&t.appContext.config||de;if(t){let s=t.parent;const u=t.proxy,c=`https://vuejs.org/error-reference/#runtime-${r}`;for(;s;){const l=s.ec;if(l){for(let f=0;f<l.length;f++)if(l[f](e,u,c)===!1)return}s=s.parent}if(i){Tt(),cn(i,null,10,[e,u,c]),xt();return}}ey(e,r,o,n,a)}function ey(e,t,r,n=!0,o=!1){if(o)throw e;console.error(e)}const Ge=[];let lt=-1;const vr=[];let Mt=null,lr=0;const Qh=Promise.resolve();let Gn=null;function ty(e){const t=Gn||Qh;return e?t.then(this?e.bind(this):e):t}function ry(e){let t=lt+1,r=Ge.length;for(;t<r;){const n=t+r>>>1,o=Ge[n],i=Qr(o);i<e||i===e&&o.flags&2?t=n+1:r=n}return t}function Qu(e){if(!(e.flags&1)){const t=Qr(e),r=Ge[Ge.length-1];!r||!(e.flags&2)&&t>=Qr(r)?Ge.push(e):Ge.splice(ry(t),0,e),e.flags|=1,Zh()}}function Zh(){Gn||(Gn=Qh.then(tv))}function ny(e){K(e)?vr.push(...e):Mt&&e.id===-1?Mt.splice(lr+1,0,e):e.flags&1||(vr.push(e),e.flags|=1),Zh()}function Fc(e,t,r=lt+1){for(;r<Ge.length;r++){const n=Ge[r];if(n&&n.flags&2){if(e&&n.id!==e.uid)continue;Ge.splice(r,1),r--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function ev(e){if(vr.length){const t=[...new Set(vr)].sort((r,n)=>Qr(r)-Qr(n));if(vr.length=0,Mt){Mt.push(...t);return}for(Mt=t,lr=0;lr<Mt.length;lr++){const r=Mt[lr];r.flags&4&&(r.flags&=-2),r.flags&8||r(),r.flags&=-2}Mt=null,lr=0}}const Qr=e=>e.id==null?e.flags&2?-1:1/0:e.id;function tv(e){try{for(lt=0;lt<Ge.length;lt++){const t=Ge[lt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),cn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;lt<Ge.length;lt++){const t=Ge[lt];t&&(t.flags&=-2)}lt=-1,Ge.length=0,ev(),Gn=null,(Ge.length||vr.length)&&tv()}}let Ne=null,rv=null;function Nn(e){const t=Ne;return Ne=e,rv=e&&e.type.__scopeId||null,t}function De(e,t=Ne,r){if(!t||e._n)return e;const n=(...o)=>{n._d&&zc(-1);const i=Nn(t);let a;try{a=e(...o)}finally{Nn(i),n._d&&zc(1)}return a};return n._n=!0,n._c=!0,n._d=!0,n}function Ut(e,t,r,n){const o=e.dirs,i=t&&t.dirs;for(let a=0;a<o.length;a++){const s=o[a];i&&(s.oldValue=i[a].value);let u=s.dir[n];u&&(Tt(),ht(u,r,8,[e.el,s,e,t]),xt())}}const oy=Symbol("_vte"),iy=e=>e.__isTeleport;function Zu(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Zu(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function We(e,t){return J(e)?Le({name:e.name},t,{setup:e}):e}function nv(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Wr(e,t,r,n,o=!1){if(K(e)){e.forEach((y,m)=>Wr(y,t&&(K(t)?t[m]:t),r,n,o));return}if(gr(n)&&!o){n.shapeFlag&512&&n.type.__asyncResolved&&n.component.subTree.component&&Wr(e,t,r,n.component.subTree);return}const i=n.shapeFlag&4?ac(n.component):n.el,a=o?null:i,{i:s,r:u}=e,c=t&&t.r,l=s.refs===de?s.refs={}:s.refs,f=s.setupState,p=ue(f),v=f===de?()=>!1:y=>ae(p,y);if(c!=null&&c!==u&&(me(c)?(l[c]=null,v(c)&&(f[c]=null)):Me(c)&&(c.value=null)),J(u))cn(u,s,12,[a,l]);else{const y=me(u),m=Me(u);if(y||m){const h=()=>{if(e.f){const b=y?v(u)?f[u]:l[u]:u.value;o?K(b)&&Vu(b,i):K(b)?b.includes(i)||b.push(i):y?(l[u]=[i],v(u)&&(f[u]=l[u])):(u.value=[i],e.k&&(l[e.k]=u.value))}else y?(l[u]=a,v(u)&&(f[u]=a)):m&&(u.value=a,e.k&&(l[e.k]=a))};a?(h.id=-1,Ve(h,r)):h()}}}Zn().requestIdleCallback;Zn().cancelIdleCallback;const gr=e=>!!e.type.__asyncLoader,ov=e=>e.type.__isKeepAlive;function ay(e,t){iv(e,"a",t)}function sy(e,t){iv(e,"da",t)}function iv(e,t,r=Re){const n=e.__wdc||(e.__wdc=()=>{let o=r;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(ro(t,n,r),r){let o=r.parent;for(;o&&o.parent;)ov(o.parent.vnode)&&uy(n,t,r,o),o=o.parent}}function uy(e,t,r,n){const o=ro(t,e,n,!0);av(()=>{Vu(n[t],o)},r)}function ro(e,t,r=Re,n=!1){if(r){const o=r[e]||(r[e]=[]),i=t.__weh||(t.__weh=(...a)=>{Tt();const s=ln(r),u=ht(t,r,e,a);return s(),xt(),u});return n?o.unshift(i):o.push(i),i}}const At=e=>(t,r=Re)=>{(!tn||e==="sp")&&ro(e,(...n)=>t(...n),r)},cy=At("bm"),ly=At("m"),fy=At("bu"),dy=At("u"),py=At("bum"),av=At("um"),hy=At("sp"),vy=At("rtg"),gy=At("rtc");function my(e,t=Re){ro("ec",e,t)}const yy="components",sv=Symbol.for("v-ndc");function ec(e){return me(e)?by(yy,e,!1)||e:e||sv}function by(e,t,r=!0,n=!1){const o=Ne||Re;if(o){const i=o.type;{const s=ib(i,!1);if(s&&(s===t||s===Je(t)||s===Qn(Je(t))))return i}const a=Hc(o[e]||i[e],t)||Hc(o.appContext[e],t);return!a&&n?i:a}}function Hc(e,t){return e&&(e[t]||e[Je(t)]||e[Qn(Je(t))])}function _y(e,t,r,n){let o;const i=r,a=K(e);if(a||me(e)){const s=a&&hr(e);let u=!1,c=!1;s&&(u=!Xe(e),c=Ht(e),e=eo(e)),o=new Array(e.length);for(let l=0,f=e.length;l<f;l++)o[l]=t(u?c?qn(xe(e[l])):xe(e[l]):e[l],l,void 0,i)}else if(typeof e=="number"){o=new Array(e);for(let s=0;s<e;s++)o[s]=t(s+1,s,void 0,i)}else if(le(e))if(e[Symbol.iterator])o=Array.from(e,(s,u)=>t(s,u,void 0,i));else{const s=Object.keys(e);o=new Array(s.length);for(let u=0,c=s.length;u<c;u++){const l=s[u];o[u]=t(e[l],l,u,i)}}else o=[];return o}function St(e,t,r={},n,o){if(Ne.ce||Ne.parent&&gr(Ne.parent)&&Ne.parent.ce)return t!=="default"&&(r.name=t),Se(),kt(Be,null,[be("slot",r,n&&n())],64);let i=e[t];i&&i._c&&(i._d=!1),Se();const a=i&&uv(i(r)),s=r.key||a&&a.key,u=kt(Be,{key:(s&&!Pt(s)?s:`_${t}`)+(!a&&n?"_fb":"")},a||(n?n():[]),a&&e._===1?64:-2);return u.scopeId&&(u.slotScopeIds=[u.scopeId+"-s"]),i&&i._c&&(i._d=!0),u}function uv(e){return e.some(t=>en(t)?!(t.type===Ot||t.type===Be&&!uv(t.children)):!0)?e:null}const pu=e=>e?Av(e)?ac(e):pu(e.parent):null,Ur=Le(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>pu(e.parent),$root:e=>pu(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>lv(e),$forceUpdate:e=>e.f||(e.f=()=>{Qu(e.update)}),$nextTick:e=>e.n||(e.n=ty.bind(e.proxy)),$watch:e=>ky.bind(e)}),Co=(e,t)=>e!==de&&!e.__isScriptSetup&&ae(e,t),wy={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:r,setupState:n,data:o,props:i,accessCache:a,type:s,appContext:u}=e;let c;if(t[0]!=="$"){const v=a[t];if(v!==void 0)switch(v){case 1:return n[t];case 2:return o[t];case 4:return r[t];case 3:return i[t]}else{if(Co(n,t))return a[t]=1,n[t];if(o!==de&&ae(o,t))return a[t]=2,o[t];if((c=e.propsOptions[0])&&ae(c,t))return a[t]=3,i[t];if(r!==de&&ae(r,t))return a[t]=4,r[t];hu&&(a[t]=0)}}const l=Ur[t];let f,p;if(l)return t==="$attrs"&&Ae(e.attrs,"get",""),l(e);if((f=s.__cssModules)&&(f=f[t]))return f;if(r!==de&&ae(r,t))return a[t]=4,r[t];if(p=u.config.globalProperties,ae(p,t))return p[t]},set({_:e},t,r){const{data:n,setupState:o,ctx:i}=e;return Co(o,t)?(o[t]=r,!0):n!==de&&ae(n,t)?(n[t]=r,!0):ae(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=r,!0)},has({_:{data:e,setupState:t,accessCache:r,ctx:n,appContext:o,propsOptions:i}},a){let s;return!!r[a]||e!==de&&ae(e,a)||Co(t,a)||(s=i[0])&&ae(s,a)||ae(n,a)||ae(Ur,a)||ae(o.config.globalProperties,a)},defineProperty(e,t,r){return r.get!=null?e._.accessCache[t]=0:ae(r,"value")&&this.set(e,t,r.value,null),Reflect.defineProperty(e,t,r)}};function kc(e){return K(e)?e.reduce((t,r)=>(t[r]=null,t),{}):e}let hu=!0;function Sy(e){const t=lv(e),r=e.proxy,n=e.ctx;hu=!1,t.beforeCreate&&Vc(t.beforeCreate,e,"bc");const{data:o,computed:i,methods:a,watch:s,provide:u,inject:c,created:l,beforeMount:f,mounted:p,beforeUpdate:v,updated:y,activated:m,deactivated:h,beforeDestroy:b,beforeUnmount:_,destroyed:O,unmounted:E,render:M,renderTracked:L,renderTriggered:F,errorCaptured:N,serverPrefetch:j,expose:D,inheritAttrs:G,components:Q,directives:ce,filters:ne}=t;if(c&&Ey(c,n,null),a)for(const W in a){const $=a[W];J($)&&(n[W]=$.bind(r))}if(o){const W=o.call(r,r);le(W)&&(e.data=zu(W))}if(hu=!0,i)for(const W in i){const $=i[W],oe=J($)?$.bind(r,r):J($.get)?$.get.bind(r,r):ze,se=!J($)&&J($.set)?$.set.bind(r):ze,ye=tt({get:oe,set:se});Object.defineProperty(n,W,{enumerable:!0,configurable:!0,get:()=>ye.value,set:he=>ye.value=he})}if(s)for(const W in s)cv(s[W],n,r,W);if(u){const W=J(u)?u.call(r):u;Reflect.ownKeys(W).forEach($=>{tc($,W[$])})}l&&Vc(l,e,"c");function te(W,$){K($)?$.forEach(oe=>W(oe.bind(r))):$&&W($.bind(r))}if(te(cy,f),te(ly,p),te(fy,v),te(dy,y),te(ay,m),te(sy,h),te(my,N),te(gy,L),te(vy,F),te(py,_),te(av,E),te(hy,j),K(D))if(D.length){const W=e.exposed||(e.exposed={});D.forEach($=>{Object.defineProperty(W,$,{get:()=>r[$],set:oe=>r[$]=oe,enumerable:!0})})}else e.exposed||(e.exposed={});M&&e.render===ze&&(e.render=M),G!=null&&(e.inheritAttrs=G),Q&&(e.components=Q),ce&&(e.directives=ce),j&&nv(e)}function Ey(e,t,r=ze){K(e)&&(e=vu(e));for(const n in e){const o=e[n];let i;le(o)?"default"in o?i=rr(o.from||n,o.default,!0):i=rr(o.from||n):i=rr(o),Me(i)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>i.value,set:a=>i.value=a}):t[n]=i}}function Vc(e,t,r){ht(K(e)?e.map(n=>n.bind(t.proxy)):e.bind(t.proxy),t,r)}function cv(e,t,r,n){let o=n.includes(".")?Ev(r,n):()=>r[n];if(me(e)){const i=t[e];J(i)&&Ao(o,i)}else if(J(e))Ao(o,e.bind(r));else if(le(e))if(K(e))e.forEach(i=>cv(i,t,r,n));else{const i=J(e.handler)?e.handler.bind(r):t[e.handler];J(i)&&Ao(o,i,e)}}function lv(e){const t=e.type,{mixins:r,extends:n}=t,{mixins:o,optionsCache:i,config:{optionMergeStrategies:a}}=e.appContext,s=i.get(t);let u;return s?u=s:!o.length&&!r&&!n?u=t:(u={},o.length&&o.forEach(c=>Fn(u,c,a,!0)),Fn(u,t,a)),le(t)&&i.set(t,u),u}function Fn(e,t,r,n=!1){const{mixins:o,extends:i}=t;i&&Fn(e,i,r,!0),o&&o.forEach(a=>Fn(e,a,r,!0));for(const a in t)if(!(n&&a==="expose")){const s=Ty[a]||r&&r[a];e[a]=s?s(e[a],t[a]):t[a]}return e}const Ty={data:Bc,props:$c,emits:$c,methods:Dr,computed:Dr,beforeCreate:je,created:je,beforeMount:je,mounted:je,beforeUpdate:je,updated:je,beforeDestroy:je,beforeUnmount:je,destroyed:je,unmounted:je,activated:je,deactivated:je,errorCaptured:je,serverPrefetch:je,components:Dr,directives:Dr,watch:Oy,provide:Bc,inject:xy};function Bc(e,t){return t?e?function(){return Le(J(e)?e.call(this,this):e,J(t)?t.call(this,this):t)}:t:e}function xy(e,t){return Dr(vu(e),vu(t))}function vu(e){if(K(e)){const t={};for(let r=0;r<e.length;r++)t[e[r]]=e[r];return t}return e}function je(e,t){return e?[...new Set([].concat(e,t))]:t}function Dr(e,t){return e?Le(Object.create(null),e,t):t}function $c(e,t){return e?K(e)&&K(t)?[...new Set([...e,...t])]:Le(Object.create(null),kc(e),kc(t??{})):t}function Oy(e,t){if(!e)return t;if(!t)return e;const r=Le(Object.create(null),e);for(const n in t)r[n]=je(e[n],t[n]);return r}function fv(){return{app:null,config:{isNativeTag:pm,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Cy=0;function Py(e,t){return function(n,o=null){J(n)||(n=Le({},n)),o!=null&&!le(o)&&(o=null);const i=fv(),a=new WeakSet,s=[];let u=!1;const c=i.app={_uid:Cy++,_component:n,_props:o,_container:null,_context:i,_instance:null,version:ub,get config(){return i.config},set config(l){},use(l,...f){return a.has(l)||(l&&J(l.install)?(a.add(l),l.install(c,...f)):J(l)&&(a.add(l),l(c,...f))),c},mixin(l){return i.mixins.includes(l)||i.mixins.push(l),c},component(l,f){return f?(i.components[l]=f,c):i.components[l]},directive(l,f){return f?(i.directives[l]=f,c):i.directives[l]},mount(l,f,p){if(!u){const v=c._ceVNode||be(n,o);return v.appContext=i,p===!0?p="svg":p===!1&&(p=void 0),e(v,l,p),u=!0,c._container=l,l.__vue_app__=c,ac(v.component)}},onUnmount(l){s.push(l)},unmount(){u&&(ht(s,c._instance,16),e(null,c._container),delete c._container.__vue_app__)},provide(l,f){return i.provides[l]=f,c},runWithContext(l){const f=mr;mr=c;try{return l()}finally{mr=f}}};return c}}let mr=null;function tc(e,t){if(Re){let r=Re.provides;const n=Re.parent&&Re.parent.provides;n===r&&(r=Re.provides=Object.create(n)),r[e]=t}}function rr(e,t,r=!1){const n=ic();if(n||mr){let o=mr?mr._context.provides:n?n.parent==null||n.ce?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return r&&J(t)?t.call(n&&n.proxy):t}}const dv={},pv=()=>Object.create(dv),hv=e=>Object.getPrototypeOf(e)===dv;function Ay(e,t,r,n=!1){const o={},i=pv();e.propsDefaults=Object.create(null),vv(e,t,o,i);for(const a in e.propsOptions[0])a in o||(o[a]=void 0);r?e.props=n?o:Wm(o):e.type.props?e.props=o:e.props=i,e.attrs=i}function Ry(e,t,r,n){const{props:o,attrs:i,vnode:{patchFlag:a}}=e,s=ue(o),[u]=e.propsOptions;let c=!1;if((n||a>0)&&!(a&16)){if(a&8){const l=e.vnode.dynamicProps;for(let f=0;f<l.length;f++){let p=l[f];if(no(e.emitsOptions,p))continue;const v=t[p];if(u)if(ae(i,p))v!==i[p]&&(i[p]=v,c=!0);else{const y=Je(p);o[y]=gu(u,s,y,v,e,!1)}else v!==i[p]&&(i[p]=v,c=!0)}}}else{vv(e,t,o,i)&&(c=!0);let l;for(const f in s)(!t||!ae(t,f)&&((l=ir(f))===f||!ae(t,l)))&&(u?r&&(r[f]!==void 0||r[l]!==void 0)&&(o[f]=gu(u,s,f,void 0,e,!0)):delete o[f]);if(i!==s)for(const f in i)(!t||!ae(t,f))&&(delete i[f],c=!0)}c&&_t(e.attrs,"set","")}function vv(e,t,r,n){const[o,i]=e.propsOptions;let a=!1,s;if(t)for(let u in t){if(Vr(u))continue;const c=t[u];let l;o&&ae(o,l=Je(u))?!i||!i.includes(l)?r[l]=c:(s||(s={}))[l]=c:no(e.emitsOptions,u)||(!(u in n)||c!==n[u])&&(n[u]=c,a=!0)}if(i){const u=ue(r),c=s||de;for(let l=0;l<i.length;l++){const f=i[l];r[f]=gu(o,u,f,c[f],e,!ae(c,f))}}return a}function gu(e,t,r,n,o,i){const a=e[r];if(a!=null){const s=ae(a,"default");if(s&&n===void 0){const u=a.default;if(a.type!==Function&&!a.skipFactory&&J(u)){const{propsDefaults:c}=o;if(r in c)n=c[r];else{const l=ln(o);n=c[r]=u.call(null,t),l()}}else n=u;o.ce&&o.ce._setProp(r,n)}a[0]&&(i&&!s?n=!1:a[1]&&(n===""||n===ir(r))&&(n=!0))}return n}const My=new WeakMap;function gv(e,t,r=!1){const n=r?My:t.propsCache,o=n.get(e);if(o)return o;const i=e.props,a={},s=[];let u=!1;if(!J(e)){const l=f=>{u=!0;const[p,v]=gv(f,t,!0);Le(a,p),v&&s.push(...v)};!r&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}if(!i&&!u)return le(e)&&n.set(e,dr),dr;if(K(i))for(let l=0;l<i.length;l++){const f=Je(i[l]);Wc(f)&&(a[f]=de)}else if(i)for(const l in i){const f=Je(l);if(Wc(f)){const p=i[l],v=a[f]=K(p)||J(p)?{type:p}:Le({},p),y=v.type;let m=!1,h=!0;if(K(y))for(let b=0;b<y.length;++b){const _=y[b],O=J(_)&&_.name;if(O==="Boolean"){m=!0;break}else O==="String"&&(h=!1)}else m=J(y)&&y.name==="Boolean";v[0]=m,v[1]=h,(m||ae(v,"default"))&&s.push(f)}}const c=[a,s];return le(e)&&n.set(e,c),c}function Wc(e){return e[0]!=="$"&&!Vr(e)}const rc=e=>e==="_"||e==="__"||e==="_ctx"||e==="$stable",nc=e=>K(e)?e.map(ft):[ft(e)],Iy=(e,t,r)=>{if(t._n)return t;const n=De((...o)=>nc(t(...o)),r);return n._c=!1,n},mv=(e,t,r)=>{const n=e._ctx;for(const o in e){if(rc(o))continue;const i=e[o];if(J(i))t[o]=Iy(o,i,n);else if(i!=null){const a=nc(i);t[o]=()=>a}}},yv=(e,t)=>{const r=nc(t);e.slots.default=()=>r},bv=(e,t,r)=>{for(const n in t)(r||!rc(n))&&(e[n]=t[n])},Ly=(e,t,r)=>{const n=e.slots=pv();if(e.vnode.shapeFlag&32){const o=t.__;o&&uu(n,"__",o,!0);const i=t._;i?(bv(n,t,r),r&&uu(n,"_",i,!0)):mv(t,n)}else t&&yv(e,t)},qy=(e,t,r)=>{const{vnode:n,slots:o}=e;let i=!0,a=de;if(n.shapeFlag&32){const s=t._;s?r&&s===1?i=!1:bv(o,t,r):(i=!t.$stable,mv(t,o)),a=t}else t&&(yv(e,t),a={default:1});if(i)for(const s in o)!rc(s)&&a[s]==null&&delete o[s]},Ve=Yy;function jy(e){return Dy(e)}function Dy(e,t){const r=Zn();r.__VUE__=!0;const{insert:n,remove:o,patchProp:i,createElement:a,createText:s,createComment:u,setText:c,setElementText:l,parentNode:f,nextSibling:p,setScopeId:v=ze,insertStaticContent:y}=e,m=(d,g,S,T=null,x=null,w=null,q=void 0,R=null,I=!!g.dynamicChildren)=>{if(d===g)return;d&&!Lr(d,g)&&(T=Ke(d),he(d,x,w,!0),d=null),g.patchFlag===-2&&(I=!1,g.dynamicChildren=null);const{type:C,ref:H,shapeFlag:P}=g;switch(C){case oo:h(d,g,S,T);break;case Ot:b(d,g,S,T);break;case Ro:d==null&&_(g,S,T,q);break;case Be:Q(d,g,S,T,x,w,q,R,I);break;default:P&1?M(d,g,S,T,x,w,q,R,I):P&6?ce(d,g,S,T,x,w,q,R,I):(P&64||P&128)&&C.process(d,g,S,T,x,w,q,R,I,st)}H!=null&&x?Wr(H,d&&d.ref,w,g||d,!g):H==null&&d&&d.ref!=null&&Wr(d.ref,null,w,d,!0)},h=(d,g,S,T)=>{if(d==null)n(g.el=s(g.children),S,T);else{const x=g.el=d.el;g.children!==d.children&&c(x,g.children)}},b=(d,g,S,T)=>{d==null?n(g.el=u(g.children||""),S,T):g.el=d.el},_=(d,g,S,T)=>{[d.el,d.anchor]=y(d.children,g,S,T,d.el,d.anchor)},O=({el:d,anchor:g},S,T)=>{let x;for(;d&&d!==g;)x=p(d),n(d,S,T),d=x;n(g,S,T)},E=({el:d,anchor:g})=>{let S;for(;d&&d!==g;)S=p(d),o(d),d=S;o(g)},M=(d,g,S,T,x,w,q,R,I)=>{g.type==="svg"?q="svg":g.type==="math"&&(q="mathml"),d==null?L(g,S,T,x,w,q,R,I):j(d,g,x,w,q,R,I)},L=(d,g,S,T,x,w,q,R)=>{let I,C;const{props:H,shapeFlag:P,transition:B,dirs:k}=d;if(I=d.el=a(d.type,w,H&&H.is,H),P&8?l(I,d.children):P&16&&N(d.children,I,null,T,x,Po(d,w),q,R),k&&Ut(d,null,T,"created"),F(I,d,d.scopeId,q,T),H){for(const Z in H)Z!=="value"&&!Vr(Z)&&i(I,Z,null,H[Z],w,T);"value"in H&&i(I,"value",null,H.value,w),(C=H.onVnodeBeforeMount)&&ct(C,T,d)}k&&Ut(d,null,T,"beforeMount");const Y=Gy(x,B);Y&&B.beforeEnter(I),n(I,g,S),((C=H&&H.onVnodeMounted)||Y||k)&&Ve(()=>{C&&ct(C,T,d),Y&&B.enter(I),k&&Ut(d,null,T,"mounted")},x)},F=(d,g,S,T,x)=>{if(S&&v(d,S),T)for(let w=0;w<T.length;w++)v(d,T[w]);if(x){let w=x.subTree;if(g===w||xv(w.type)&&(w.ssContent===g||w.ssFallback===g)){const q=x.vnode;F(d,q,q.scopeId,q.slotScopeIds,x.parent)}}},N=(d,g,S,T,x,w,q,R,I=0)=>{for(let C=I;C<d.length;C++){const H=d[C]=R?It(d[C]):ft(d[C]);m(null,H,g,S,T,x,w,q,R)}},j=(d,g,S,T,x,w,q)=>{const R=g.el=d.el;let{patchFlag:I,dynamicChildren:C,dirs:H}=g;I|=d.patchFlag&16;const P=d.props||de,B=g.props||de;let k;if(S&&Kt(S,!1),(k=B.onVnodeBeforeUpdate)&&ct(k,S,g,d),H&&Ut(g,d,S,"beforeUpdate"),S&&Kt(S,!0),(P.innerHTML&&B.innerHTML==null||P.textContent&&B.textContent==null)&&l(R,""),C?D(d.dynamicChildren,C,R,S,T,Po(g,x),w):q||$(d,g,R,null,S,T,Po(g,x),w,!1),I>0){if(I&16)G(R,P,B,S,x);else if(I&2&&P.class!==B.class&&i(R,"class",null,B.class,x),I&4&&i(R,"style",P.style,B.style,x),I&8){const Y=g.dynamicProps;for(let Z=0;Z<Y.length;Z++){const U=Y[Z],_e=P[U],we=B[U];(we!==_e||U==="value")&&i(R,U,_e,we,x,S)}}I&1&&d.children!==g.children&&l(R,g.children)}else!q&&C==null&&G(R,P,B,S,x);((k=B.onVnodeUpdated)||H)&&Ve(()=>{k&&ct(k,S,g,d),H&&Ut(g,d,S,"updated")},T)},D=(d,g,S,T,x,w,q)=>{for(let R=0;R<g.length;R++){const I=d[R],C=g[R],H=I.el&&(I.type===Be||!Lr(I,C)||I.shapeFlag&198)?f(I.el):S;m(I,C,H,null,T,x,w,q,!0)}},G=(d,g,S,T,x)=>{if(g!==S){if(g!==de)for(const w in g)!Vr(w)&&!(w in S)&&i(d,w,g[w],null,x,T);for(const w in S){if(Vr(w))continue;const q=S[w],R=g[w];q!==R&&w!=="value"&&i(d,w,R,q,x,T)}"value"in S&&i(d,"value",g.value,S.value,x)}},Q=(d,g,S,T,x,w,q,R,I)=>{const C=g.el=d?d.el:s(""),H=g.anchor=d?d.anchor:s("");let{patchFlag:P,dynamicChildren:B,slotScopeIds:k}=g;k&&(R=R?R.concat(k):k),d==null?(n(C,S,T),n(H,S,T),N(g.children||[],S,H,x,w,q,R,I)):P>0&&P&64&&B&&d.dynamicChildren?(D(d.dynamicChildren,B,S,x,w,q,R),(g.key!=null||x&&g===x.subTree)&&_v(d,g,!0)):$(d,g,S,H,x,w,q,R,I)},ce=(d,g,S,T,x,w,q,R,I)=>{g.slotScopeIds=R,d==null?g.shapeFlag&512?x.ctx.activate(g,S,T,q,I):ne(g,S,T,x,w,q,I):pe(d,g,I)},ne=(d,g,S,T,x,w,q)=>{const R=d.component=eb(d,T,x);if(ov(d)&&(R.ctx.renderer=st),tb(R,!1,q),R.asyncDep){if(x&&x.registerDep(R,te,q),!d.el){const I=R.subTree=be(Ot);b(null,I,g,S),d.placeholder=I.el}}else te(R,d,g,S,x,w,q)},pe=(d,g,S)=>{const T=g.component=d.component;if(Uy(d,g,S))if(T.asyncDep&&!T.asyncResolved){W(T,g,S);return}else T.next=g,T.update();else g.el=d.el,T.vnode=g},te=(d,g,S,T,x,w,q)=>{const R=()=>{if(d.isMounted){let{next:P,bu:B,u:k,parent:Y,vnode:Z}=d;{const re=wv(d);if(re){P&&(P.el=Z.el,W(d,P,q)),re.asyncDep.then(()=>{d.isUnmounted||R()});return}}let U=P,_e;Kt(d,!1),P?(P.el=Z.el,W(d,P,q)):P=Z,B&&So(B),(_e=P.props&&P.props.onVnodeBeforeUpdate)&&ct(_e,Y,P,Z),Kt(d,!0);const we=Kc(d),z=d.subTree;d.subTree=we,m(z,we,f(z.el),Ke(z),d,x,w),P.el=we.el,U===null&&Ky(d,we.el),k&&Ve(k,x),(_e=P.props&&P.props.onVnodeUpdated)&&Ve(()=>ct(_e,Y,P,Z),x)}else{let P;const{el:B,props:k}=g,{bm:Y,m:Z,parent:U,root:_e,type:we}=d,z=gr(g);Kt(d,!1),Y&&So(Y),!z&&(P=k&&k.onVnodeBeforeMount)&&ct(P,U,g),Kt(d,!0);{_e.ce&&_e.ce._def.shadowRoot!==!1&&_e.ce._injectChildStyle(we);const re=d.subTree=Kc(d);m(null,re,S,T,d,x,w),g.el=re.el}if(Z&&Ve(Z,x),!z&&(P=k&&k.onVnodeMounted)){const re=g;Ve(()=>ct(P,U,re),x)}(g.shapeFlag&256||U&&gr(U.vnode)&&U.vnode.shapeFlag&256)&&d.a&&Ve(d.a,x),d.isMounted=!0,g=S=T=null}};d.scope.on();const I=d.effect=new Dh(R);d.scope.off();const C=d.update=I.run.bind(I),H=d.job=I.runIfDirty.bind(I);H.i=d,H.id=d.uid,I.scheduler=()=>Qu(H),Kt(d,!0),C()},W=(d,g,S)=>{g.component=d;const T=d.vnode.props;d.vnode=g,d.next=null,Ry(d,g.props,T,S),qy(d,g.children,S),Tt(),Fc(d),xt()},$=(d,g,S,T,x,w,q,R,I=!1)=>{const C=d&&d.children,H=d?d.shapeFlag:0,P=g.children,{patchFlag:B,shapeFlag:k}=g;if(B>0){if(B&128){se(C,P,S,T,x,w,q,R,I);return}else if(B&256){oe(C,P,S,T,x,w,q,R,I);return}}k&8?(H&16&&qe(C,x,w),P!==C&&l(S,P)):H&16?k&16?se(C,P,S,T,x,w,q,R,I):qe(C,x,w,!0):(H&8&&l(S,""),k&16&&N(P,S,T,x,w,q,R,I))},oe=(d,g,S,T,x,w,q,R,I)=>{d=d||dr,g=g||dr;const C=d.length,H=g.length,P=Math.min(C,H);let B;for(B=0;B<P;B++){const k=g[B]=I?It(g[B]):ft(g[B]);m(d[B],k,S,null,x,w,q,R,I)}C>H?qe(d,x,w,!0,!1,P):N(g,S,T,x,w,q,R,I,P)},se=(d,g,S,T,x,w,q,R,I)=>{let C=0;const H=g.length;let P=d.length-1,B=H-1;for(;C<=P&&C<=B;){const k=d[C],Y=g[C]=I?It(g[C]):ft(g[C]);if(Lr(k,Y))m(k,Y,S,null,x,w,q,R,I);else break;C++}for(;C<=P&&C<=B;){const k=d[P],Y=g[B]=I?It(g[B]):ft(g[B]);if(Lr(k,Y))m(k,Y,S,null,x,w,q,R,I);else break;P--,B--}if(C>P){if(C<=B){const k=B+1,Y=k<H?g[k].el:T;for(;C<=B;)m(null,g[C]=I?It(g[C]):ft(g[C]),S,Y,x,w,q,R,I),C++}}else if(C>B)for(;C<=P;)he(d[C],x,w,!0),C++;else{const k=C,Y=C,Z=new Map;for(C=Y;C<=B;C++){const ge=g[C]=I?It(g[C]):ft(g[C]);ge.key!=null&&Z.set(ge.key,C)}let U,_e=0;const we=B-Y+1;let z=!1,re=0;const A=new Array(we);for(C=0;C<we;C++)A[C]=0;for(C=k;C<=P;C++){const ge=d[C];if(_e>=we){he(ge,x,w,!0);continue}let ut;if(ge.key!=null)ut=Z.get(ge.key);else for(U=Y;U<=B;U++)if(A[U-Y]===0&&Lr(ge,g[U])){ut=U;break}ut===void 0?he(ge,x,w,!0):(A[ut-Y]=C+1,ut>=re?re=ut:z=!0,m(ge,g[ut],S,null,x,w,q,R,I),_e++)}const Ce=z?Ny(A):dr;for(U=Ce.length-1,C=we-1;C>=0;C--){const ge=Y+C,ut=g[ge],qc=g[ge+1],jc=ge+1<H?qc.el||qc.placeholder:T;A[C]===0?m(null,ut,S,jc,x,w,q,R,I):z&&(U<0||C!==Ce[U]?ye(ut,S,jc,2):U--)}}},ye=(d,g,S,T,x=null)=>{const{el:w,type:q,transition:R,children:I,shapeFlag:C}=d;if(C&6){ye(d.component.subTree,g,S,T);return}if(C&128){d.suspense.move(g,S,T);return}if(C&64){q.move(d,g,S,st);return}if(q===Be){n(w,g,S);for(let P=0;P<I.length;P++)ye(I[P],g,S,T);n(d.anchor,g,S);return}if(q===Ro){O(d,g,S);return}if(T!==2&&C&1&&R)if(T===0)R.beforeEnter(w),n(w,g,S),Ve(()=>R.enter(w),x);else{const{leave:P,delayLeave:B,afterLeave:k}=R,Y=()=>{d.ctx.isUnmounted?o(w):n(w,g,S)},Z=()=>{P(w,()=>{Y(),k&&k()})};B?B(w,Y,Z):Z()}else n(w,g,S)},he=(d,g,S,T=!1,x=!1)=>{const{type:w,props:q,ref:R,children:I,dynamicChildren:C,shapeFlag:H,patchFlag:P,dirs:B,cacheIndex:k}=d;if(P===-2&&(x=!1),R!=null&&(Tt(),Wr(R,null,S,d,!0),xt()),k!=null&&(g.renderCache[k]=void 0),H&256){g.ctx.deactivate(d);return}const Y=H&1&&B,Z=!gr(d);let U;if(Z&&(U=q&&q.onVnodeBeforeUnmount)&&ct(U,g,d),H&6)Ue(d.component,S,T);else{if(H&128){d.suspense.unmount(S,T);return}Y&&Ut(d,null,g,"beforeUnmount"),H&64?d.type.remove(d,g,S,st,T):C&&!C.hasOnce&&(w!==Be||P>0&&P&64)?qe(C,g,S,!1,!0):(w===Be&&P&384||!x&&H&16)&&qe(I,g,S),T&&Fe(d)}(Z&&(U=q&&q.onVnodeUnmounted)||Y)&&Ve(()=>{U&&ct(U,g,d),Y&&Ut(d,null,g,"unmounted")},S)},Fe=d=>{const{type:g,el:S,anchor:T,transition:x}=d;if(g===Be){Qe(S,T);return}if(g===Ro){E(d);return}const w=()=>{o(S),x&&!x.persisted&&x.afterLeave&&x.afterLeave()};if(d.shapeFlag&1&&x&&!x.persisted){const{leave:q,delayLeave:R}=x,I=()=>q(S,w);R?R(d.el,w,I):I()}else w()},Qe=(d,g)=>{let S;for(;d!==g;)S=p(d),o(d),d=S;o(g)},Ue=(d,g,S)=>{const{bum:T,scope:x,job:w,subTree:q,um:R,m:I,a:C,parent:H,slots:{__:P}}=d;Uc(I),Uc(C),T&&So(T),H&&K(P)&&P.forEach(B=>{H.renderCache[B]=void 0}),x.stop(),w&&(w.flags|=8,he(q,d,g,S)),R&&Ve(R,g),Ve(()=>{d.isUnmounted=!0},g),g&&g.pendingBranch&&!g.isUnmounted&&d.asyncDep&&!d.asyncResolved&&d.suspenseId===g.pendingId&&(g.deps--,g.deps===0&&g.resolve())},qe=(d,g,S,T=!1,x=!1,w=0)=>{for(let q=w;q<d.length;q++)he(d[q],g,S,T,x)},Ke=d=>{if(d.shapeFlag&6)return Ke(d.component.subTree);if(d.shapeFlag&128)return d.suspense.next();const g=p(d.anchor||d.el),S=g&&g[oy];return S?p(S):g};let at=!1;const Ze=(d,g,S)=>{d==null?g._vnode&&he(g._vnode,null,null,!0):m(g._vnode||null,d,g,null,null,null,S),g._vnode=d,at||(at=!0,Fc(),ev(),at=!1)},st={p:m,um:he,m:ye,r:Fe,mt:ne,mc:N,pc:$,pbc:D,n:Ke,o:e};return{render:Ze,hydrate:void 0,createApp:Py(Ze)}}function Po({type:e,props:t},r){return r==="svg"&&e==="foreignObject"||r==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:r}function Kt({effect:e,job:t},r){r?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Gy(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function _v(e,t,r=!1){const n=e.children,o=t.children;if(K(n)&&K(o))for(let i=0;i<n.length;i++){const a=n[i];let s=o[i];s.shapeFlag&1&&!s.dynamicChildren&&((s.patchFlag<=0||s.patchFlag===32)&&(s=o[i]=It(o[i]),s.el=a.el),!r&&s.patchFlag!==-2&&_v(a,s)),s.type===oo&&(s.el=a.el),s.type===Ot&&!s.el&&(s.el=a.el)}}function Ny(e){const t=e.slice(),r=[0];let n,o,i,a,s;const u=e.length;for(n=0;n<u;n++){const c=e[n];if(c!==0){if(o=r[r.length-1],e[o]<c){t[n]=o,r.push(n);continue}for(i=0,a=r.length-1;i<a;)s=i+a>>1,e[r[s]]<c?i=s+1:a=s;c<e[r[i]]&&(i>0&&(t[n]=r[i-1]),r[i]=n)}}for(i=r.length,a=r[i-1];i-- >0;)r[i]=a,a=t[a];return r}function wv(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:wv(t)}function Uc(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Fy=Symbol.for("v-scx"),Hy=()=>rr(Fy);function Ao(e,t,r){return Sv(e,t,r)}function Sv(e,t,r=de){const{immediate:n,deep:o,flush:i,once:a}=r,s=Le({},r),u=t&&n||!t&&i!=="post";let c;if(tn){if(i==="sync"){const v=Hy();c=v.__watcherHandles||(v.__watcherHandles=[])}else if(!u){const v=()=>{};return v.stop=ze,v.resume=ze,v.pause=ze,v}}const l=Re;s.call=(v,y,m)=>ht(v,l,y,m);let f=!1;i==="post"?s.scheduler=v=>{Ve(v,l&&l.suspense)}:i!=="sync"&&(f=!0,s.scheduler=(v,y)=>{y?v():Qu(v)}),s.augmentJob=v=>{t&&(v.flags|=4),f&&(v.flags|=2,l&&(v.id=l.uid,v.i=l))};const p=Zm(e,t,s);return tn&&(c?c.push(p):u&&p()),p}function ky(e,t,r){const n=this.proxy,o=me(e)?e.includes(".")?Ev(n,e):()=>n[e]:e.bind(n,n);let i;J(t)?i=t:(i=t.handler,r=t);const a=ln(this),s=Sv(o,i.bind(n),r);return a(),s}function Ev(e,t){const r=t.split(".");return()=>{let n=e;for(let o=0;o<r.length&&n;o++)n=n[r[o]];return n}}const Vy=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Je(t)}Modifiers`]||e[`${ir(t)}Modifiers`];function By(e,t,...r){if(e.isUnmounted)return;const n=e.vnode.props||de;let o=r;const i=t.startsWith("update:"),a=i&&Vy(n,t.slice(7));a&&(a.trim&&(o=r.map(l=>me(l)?l.trim():l)),a.number&&(o=r.map(ym)));let s,u=n[s=wo(t)]||n[s=wo(Je(t))];!u&&i&&(u=n[s=wo(ir(t))]),u&&ht(u,e,6,o);const c=n[s+"Once"];if(c){if(!e.emitted)e.emitted={};else if(e.emitted[s])return;e.emitted[s]=!0,ht(c,e,6,o)}}function Tv(e,t,r=!1){const n=t.emitsCache,o=n.get(e);if(o!==void 0)return o;const i=e.emits;let a={},s=!1;if(!J(e)){const u=c=>{const l=Tv(c,t,!0);l&&(s=!0,Le(a,l))};!r&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}return!i&&!s?(le(e)&&n.set(e,null),null):(K(i)?i.forEach(u=>a[u]=null):Le(a,i),le(e)&&n.set(e,a),a)}function no(e,t){return!e||!zn(t)?!1:(t=t.slice(2).replace(/Once$/,""),ae(e,t[0].toLowerCase()+t.slice(1))||ae(e,ir(t))||ae(e,t))}function Kc(e){const{type:t,vnode:r,proxy:n,withProxy:o,propsOptions:[i],slots:a,attrs:s,emit:u,render:c,renderCache:l,props:f,data:p,setupState:v,ctx:y,inheritAttrs:m}=e,h=Nn(e);let b,_;try{if(r.shapeFlag&4){const E=o||n,M=E;b=ft(c.call(M,E,l,f,v,p,y)),_=s}else{const E=t;b=ft(E.length>1?E(f,{attrs:s,slots:a,emit:u}):E(f,null)),_=t.props?s:$y(s)}}catch(E){Kr.length=0,to(E,e,1),b=be(Ot)}let O=b;if(_&&m!==!1){const E=Object.keys(_),{shapeFlag:M}=O;E.length&&M&7&&(i&&E.some(ku)&&(_=Wy(_,i)),O=_r(O,_,!1,!0))}return r.dirs&&(O=_r(O,null,!1,!0),O.dirs=O.dirs?O.dirs.concat(r.dirs):r.dirs),r.transition&&Zu(O,r.transition),b=O,Nn(h),b}const $y=e=>{let t;for(const r in e)(r==="class"||r==="style"||zn(r))&&((t||(t={}))[r]=e[r]);return t},Wy=(e,t)=>{const r={};for(const n in e)(!ku(n)||!(n.slice(9)in t))&&(r[n]=e[n]);return r};function Uy(e,t,r){const{props:n,children:o,component:i}=e,{props:a,children:s,patchFlag:u}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(r&&u>=0){if(u&1024)return!0;if(u&16)return n?Yc(n,a,c):!!a;if(u&8){const l=t.dynamicProps;for(let f=0;f<l.length;f++){const p=l[f];if(a[p]!==n[p]&&!no(c,p))return!0}}}else return(o||s)&&(!s||!s.$stable)?!0:n===a?!1:n?a?Yc(n,a,c):!0:!!a;return!1}function Yc(e,t,r){const n=Object.keys(t);if(n.length!==Object.keys(e).length)return!0;for(let o=0;o<n.length;o++){const i=n[o];if(t[i]!==e[i]&&!no(r,i))return!0}return!1}function Ky({vnode:e,parent:t},r){for(;t;){const n=t.subTree;if(n.suspense&&n.suspense.activeBranch===e&&(n.el=e.el),n===e)(e=t.vnode).el=r,t=t.parent;else break}}const xv=e=>e.__isSuspense;function Yy(e,t){t&&t.pendingBranch?K(e)?t.effects.push(...e):t.effects.push(e):ny(e)}const Be=Symbol.for("v-fgt"),oo=Symbol.for("v-txt"),Ot=Symbol.for("v-cmt"),Ro=Symbol.for("v-stc"),Kr=[];let $e=null;function Se(e=!1){Kr.push($e=e?null:[])}function zy(){Kr.pop(),$e=Kr[Kr.length-1]||null}let Zr=1;function zc(e,t=!1){Zr+=e,e<0&&$e&&t&&($e.hasOnce=!0)}function Ov(e){return e.dynamicChildren=Zr>0?$e||dr:null,zy(),Zr>0&&$e&&$e.push(e),e}function Ye(e,t,r,n,o,i){return Ov(Pe(e,t,r,n,o,i,!0))}function kt(e,t,r,n,o){return Ov(be(e,t,r,n,o,!0))}function en(e){return e?e.__v_isVNode===!0:!1}function Lr(e,t){return e.type===t.type&&e.key===t.key}const Cv=({key:e})=>e??null,An=({ref:e,ref_key:t,ref_for:r})=>(typeof e=="number"&&(e=""+e),e!=null?me(e)||Me(e)||J(e)?{i:Ne,r:e,k:t,f:!!r}:e:null);function Pe(e,t=null,r=null,n=0,o=null,i=e===Be?0:1,a=!1,s=!1){const u={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Cv(t),ref:t&&An(t),scopeId:rv,slotScopeIds:null,children:r,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:n,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:Ne};return s?(oc(u,r),i&128&&e.normalize(u)):r&&(u.shapeFlag|=me(r)?8:16),Zr>0&&!a&&$e&&(u.patchFlag>0||i&6)&&u.patchFlag!==32&&$e.push(u),u}const be=Xy;function Xy(e,t=null,r=null,n=0,o=null,i=!1){if((!e||e===sv)&&(e=Ot),en(e)){const s=_r(e,t,!0);return r&&oc(s,r),Zr>0&&!i&&$e&&(s.shapeFlag&6?$e[$e.indexOf(e)]=s:$e.push(s)),s.patchFlag=-2,s}if(ab(e)&&(e=e.__vccOpts),t){t=Jy(t);let{class:s,style:u}=t;s&&!me(s)&&(t.class=Ee(s)),le(u)&&(Ju(u)&&!K(u)&&(u=Le({},u)),t.style=ar(u))}const a=me(e)?1:xv(e)?128:iy(e)?64:le(e)?4:J(e)?2:0;return Pe(e,t,r,n,o,a,i,!0)}function Jy(e){return e?Ju(e)||hv(e)?Le({},e):e:null}function _r(e,t,r=!1,n=!1){const{props:o,ref:i,patchFlag:a,children:s,transition:u}=e,c=t?Pv(o||{},t):o,l={__v_isVNode:!0,__v_skip:!0,type:e.type,props:c,key:c&&Cv(c),ref:t&&t.ref?r&&i?K(i)?i.concat(An(t)):[i,An(t)]:An(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Be?a===-1?16:a|16:a,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:u,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&_r(e.ssContent),ssFallback:e.ssFallback&&_r(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return u&&n&&Zu(l,u.clone(l)),l}function Hn(e=" ",t=0){return be(oo,null,e,t)}function Qt(e="",t=!1){return t?(Se(),kt(Ot,null,e)):be(Ot,null,e)}function ft(e){return e==null||typeof e=="boolean"?be(Ot):K(e)?be(Be,null,e.slice()):en(e)?It(e):be(oo,null,String(e))}function It(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:_r(e)}function oc(e,t){let r=0;const{shapeFlag:n}=e;if(t==null)t=null;else if(K(t))r=16;else if(typeof t=="object")if(n&65){const o=t.default;o&&(o._c&&(o._d=!1),oc(e,o()),o._c&&(o._d=!0));return}else{r=32;const o=t._;!o&&!hv(t)?t._ctx=Ne:o===3&&Ne&&(Ne.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else J(t)?(t={default:t,_ctx:Ne},r=32):(t=String(t),n&64?(r=16,t=[Hn(t)]):r=8);e.children=t,e.shapeFlag|=r}function Pv(...e){const t={};for(let r=0;r<e.length;r++){const n=e[r];for(const o in n)if(o==="class")t.class!==n.class&&(t.class=Ee([t.class,n.class]));else if(o==="style")t.style=ar([t.style,n.style]);else if(zn(o)){const i=t[o],a=n[o];a&&i!==a&&!(K(i)&&i.includes(a))&&(t[o]=i?[].concat(i,a):a)}else o!==""&&(t[o]=n[o])}return t}function ct(e,t,r,n=null){ht(e,t,7,[r,n])}const Qy=fv();let Zy=0;function eb(e,t,r){const n=e.type,o=(t?t.appContext:e.appContext)||Qy,i={uid:Zy++,vnode:e,type:n,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new xm(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:gv(n,o),emitsOptions:Tv(n,o),emit:null,emitted:null,propsDefaults:de,inheritAttrs:n.inheritAttrs,ctx:de,data:de,props:de,attrs:de,slots:de,refs:de,setupState:de,setupContext:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=By.bind(null,i),e.ce&&e.ce(i),i}let Re=null;const ic=()=>Re||Ne;let kn,mu;{const e=Zn(),t=(r,n)=>{let o;return(o=e[r])||(o=e[r]=[]),o.push(n),i=>{o.length>1?o.forEach(a=>a(i)):o[0](i)}};kn=t("__VUE_INSTANCE_SETTERS__",r=>Re=r),mu=t("__VUE_SSR_SETTERS__",r=>tn=r)}const ln=e=>{const t=Re;return kn(e),e.scope.on(),()=>{e.scope.off(),kn(t)}},Xc=()=>{Re&&Re.scope.off(),kn(null)};function Av(e){return e.vnode.shapeFlag&4}let tn=!1;function tb(e,t=!1,r=!1){t&&mu(t);const{props:n,children:o}=e.vnode,i=Av(e);Ay(e,n,i,t),Ly(e,o,r||t);const a=i?rb(e,t):void 0;return t&&mu(!1),a}function rb(e,t){const r=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,wy);const{setup:n}=r;if(n){Tt();const o=e.setupContext=n.length>1?ob(e):null,i=ln(e),a=cn(n,e,0,[e.props,o]),s=Rh(a);if(xt(),i(),(s||e.sp)&&!gr(e)&&nv(e),s){if(a.then(Xc,Xc),t)return a.then(u=>{Jc(e,u)}).catch(u=>{to(u,e,0)});e.asyncDep=a}else Jc(e,a)}else Rv(e)}function Jc(e,t,r){J(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:le(t)&&(e.setupState=Jh(t)),Rv(e)}function Rv(e,t,r){const n=e.type;e.render||(e.render=n.render||ze);{const o=ln(e);Tt();try{Sy(e)}finally{xt(),o()}}}const nb={get(e,t){return Ae(e,"get",""),e[t]}};function ob(e){const t=r=>{e.exposed=r||{}};return{attrs:new Proxy(e.attrs,nb),slots:e.slots,emit:e.emit,expose:t}}function ac(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Jh(Um(e.exposed)),{get(t,r){if(r in t)return t[r];if(r in Ur)return Ur[r](e)},has(t,r){return r in t||r in Ur}})):e.proxy}function ib(e,t=!0){return J(e)?e.displayName||e.name:e.name||t&&e.__name}function ab(e){return J(e)&&"__vccOpts"in e}const tt=(e,t)=>Jm(e,t,tn);function sb(e,t,r){const n=arguments.length;return n===2?le(t)&&!K(t)?en(t)?be(e,null,[t]):be(e,t):be(e,null,t):(n>3?r=Array.prototype.slice.call(arguments,2):n===3&&en(r)&&(r=[r]),be(e,t,r))}const ub="3.5.18",cb=ze;/**
* @vue/runtime-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let yu;const Qc=typeof window<"u"&&window.trustedTypes;if(Qc)try{yu=Qc.createPolicy("vue",{createHTML:e=>e})}catch{}const Mv=yu?e=>yu.createHTML(e):e=>e,lb="http://www.w3.org/2000/svg",fb="http://www.w3.org/1998/Math/MathML",bt=typeof document<"u"?document:null,Zc=bt&&bt.createElement("template"),db={insert:(e,t,r)=>{t.insertBefore(e,r||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,r,n)=>{const o=t==="svg"?bt.createElementNS(lb,e):t==="mathml"?bt.createElementNS(fb,e):r?bt.createElement(e,{is:r}):bt.createElement(e);return e==="select"&&n&&n.multiple!=null&&o.setAttribute("multiple",n.multiple),o},createText:e=>bt.createTextNode(e),createComment:e=>bt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>bt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,r,n,o,i){const a=r?r.previousSibling:t.lastChild;if(o&&(o===i||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),r),!(o===i||!(o=o.nextSibling)););else{Zc.innerHTML=Mv(n==="svg"?`<svg>${e}</svg>`:n==="mathml"?`<math>${e}</math>`:e);const s=Zc.content;if(n==="svg"||n==="mathml"){const u=s.firstChild;for(;u.firstChild;)s.appendChild(u.firstChild);s.removeChild(u)}t.insertBefore(s,r)}return[a?a.nextSibling:t.firstChild,r?r.previousSibling:t.lastChild]}},pb=Symbol("_vtc");function hb(e,t,r){const n=e[pb];n&&(t=(t?[t,...n]:[...n]).join(" ")),t==null?e.removeAttribute("class"):r?e.setAttribute("class",t):e.className=t}const el=Symbol("_vod"),vb=Symbol("_vsh"),gb=Symbol(""),mb=/(^|;)\s*display\s*:/;function yb(e,t,r){const n=e.style,o=me(r);let i=!1;if(r&&!o){if(t)if(me(t))for(const a of t.split(";")){const s=a.slice(0,a.indexOf(":")).trim();r[s]==null&&Rn(n,s,"")}else for(const a in t)r[a]==null&&Rn(n,a,"");for(const a in r)a==="display"&&(i=!0),Rn(n,a,r[a])}else if(o){if(t!==r){const a=n[gb];a&&(r+=";"+a),n.cssText=r,i=mb.test(r)}}else t&&e.removeAttribute("style");el in e&&(e[el]=i?n.display:"",e[vb]&&(n.display="none"))}const tl=/\s*!important$/;function Rn(e,t,r){if(K(r))r.forEach(n=>Rn(e,t,n));else if(r==null&&(r=""),t.startsWith("--"))e.setProperty(t,r);else{const n=bb(e,t);tl.test(r)?e.setProperty(ir(n),r.replace(tl,""),"important"):e[n]=r}}const rl=["Webkit","Moz","ms"],Mo={};function bb(e,t){const r=Mo[t];if(r)return r;let n=Je(t);if(n!=="filter"&&n in e)return Mo[t]=n;n=Qn(n);for(let o=0;o<rl.length;o++){const i=rl[o]+n;if(i in e)return Mo[t]=i}return t}const nl="http://www.w3.org/1999/xlink";function ol(e,t,r,n,o,i=Tm(t)){n&&t.startsWith("xlink:")?r==null?e.removeAttributeNS(nl,t.slice(6,t.length)):e.setAttributeNS(nl,t,r):r==null||i&&!Lh(r)?e.removeAttribute(t):e.setAttribute(t,i?"":Pt(r)?String(r):r)}function il(e,t,r,n,o){if(t==="innerHTML"||t==="textContent"){r!=null&&(e[t]=t==="innerHTML"?Mv(r):r);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const s=i==="OPTION"?e.getAttribute("value")||"":e.value,u=r==null?e.type==="checkbox"?"on":"":String(r);(s!==u||!("_value"in e))&&(e.value=u),r==null&&e.removeAttribute(t),e._value=r;return}let a=!1;if(r===""||r==null){const s=typeof e[t];s==="boolean"?r=Lh(r):r==null&&s==="string"?(r="",a=!0):s==="number"&&(r=0,a=!0)}try{e[t]=r}catch{}a&&e.removeAttribute(o||t)}function _b(e,t,r,n){e.addEventListener(t,r,n)}function wb(e,t,r,n){e.removeEventListener(t,r,n)}const al=Symbol("_vei");function Sb(e,t,r,n,o=null){const i=e[al]||(e[al]={}),a=i[t];if(n&&a)a.value=n;else{const[s,u]=Eb(t);if(n){const c=i[t]=Ob(n,o);_b(e,s,c,u)}else a&&(wb(e,s,a,u),i[t]=void 0)}}const sl=/(?:Once|Passive|Capture)$/;function Eb(e){let t;if(sl.test(e)){t={};let n;for(;n=e.match(sl);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):ir(e.slice(2)),t]}let Io=0;const Tb=Promise.resolve(),xb=()=>Io||(Tb.then(()=>Io=0),Io=Date.now());function Ob(e,t){const r=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=r.attached)return;ht(Cb(n,r.value),t,5,[n])};return r.value=e,r.attached=xb(),r}function Cb(e,t){if(K(t)){const r=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{r.call(e),e._stopped=!0},t.map(n=>o=>!o._stopped&&n&&n(o))}else return t}const ul=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Pb=(e,t,r,n,o,i)=>{const a=o==="svg";t==="class"?hb(e,n,a):t==="style"?yb(e,r,n):zn(t)?ku(t)||Sb(e,t,r,n,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Ab(e,t,n,a))?(il(e,t,n),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&ol(e,t,n,a,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!me(n))?il(e,Je(t),n,i,t):(t==="true-value"?e._trueValue=n:t==="false-value"&&(e._falseValue=n),ol(e,t,n,a))};function Ab(e,t,r,n){if(n)return!!(t==="innerHTML"||t==="textContent"||t in e&&ul(t)&&J(r));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return ul(t)&&me(r)?!1:t in e}const Rb=Le({patchProp:Pb},db);let cl;function Mb(){return cl||(cl=jy(Rb))}const Ib=(...e)=>{const t=Mb().createApp(...e),{mount:r}=t;return t.mount=n=>{const o=qb(n);if(!o)return;const i=t._component;!J(i)&&!i.render&&!i.template&&(i.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const a=r(o,!1,Lb(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),a},t};function Lb(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function qb(e){return me(e)?document.querySelector(e):e}const jb=Symbol(),Lo="el",Db="is-",Yt=(e,t,r,n,o)=>{let i=`${e}-${t}`;return r&&(i+=`-${r}`),n&&(i+=`__${n}`),o&&(i+=`--${o}`),i},Gb=Symbol("namespaceContextKey"),Nb=e=>{const t=ic()?rr(Gb,jn(Lo)):jn(Lo);return tt(()=>ee(t)||Lo)},Or=(e,t)=>{const r=Nb();return{namespace:r,b:(m="")=>Yt(r.value,e,m,"",""),e:m=>m?Yt(r.value,e,"",m,""):"",m:m=>m?Yt(r.value,e,"","",m):"",be:(m,h)=>m&&h?Yt(r.value,e,m,h,""):"",em:(m,h)=>m&&h?Yt(r.value,e,"",m,h):"",bm:(m,h)=>m&&h?Yt(r.value,e,m,"",h):"",bem:(m,h,b)=>m&&h&&b?Yt(r.value,e,m,h,b):"",is:(m,...h)=>{const b=h.length>=1?h[0]:!0;return m&&b?`${Db}${m}`:""},cssVar:m=>{const h={};for(const b in m)m[b]&&(h[`--${r.value}-${b}`]=m[b]);return h},cssVarName:m=>`--${r.value}-${m}`,cssVarBlock:m=>{const h={};for(const b in m)m[b]&&(h[`--${r.value}-${e}-${b}`]=m[b]);return h},cssVarBlockName:m=>`--${r.value}-${e}-${m}`}};function Fb(e){for(var t=-1,r=e==null?0:e.length,n={};++t<r;){var o=e[t];n[o[0]]=o[1]}return n}const Hb=e=>e===void 0,bu=e=>typeof e=="number",kb=e=>me(e)?!Number.isNaN(Number(e)):!1,Iv="__epPropKey",jt=e=>e,Vb=e=>le(e)&&!!e[Iv],Bb=(e,t)=>{if(!le(e)||Vb(e))return e;const{values:r,required:n,default:o,type:i,validator:a}=e,u={type:i,required:!!n,validator:r||a?c=>{let l=!1,f=[];if(r&&(f=Array.from(r),ae(e,"default")&&f.push(o),l||(l=f.includes(c))),a&&(l||(l=a(c))),!l&&f.length>0){const p=[...new Set(f)].map(v=>JSON.stringify(v)).join(", ");cb(`Invalid prop: validation failed${t?` for prop "${t}"`:""}. Expected one of [${p}], got value ${JSON.stringify(c)}.`)}return l}:void 0,[Iv]:!0};return ae(e,"default")&&(u.default=o),u},fn=e=>Fb(Object.entries(e).map(([t,r])=>[t,Bb(r,t)])),ll=jn();function $b(e,t=void 0){const r=ic()?rr(jb,ll):ll;return tt(()=>{var n,o;return(o=(n=r.value)==null?void 0:n[e])!=null?o:t})}var dn=(e,t)=>{const r=e.__vccOpts||e;for(const[n,o]of t)r[n]=o;return r};function Wb(e,t="px"){if(!e)return"";if(bu(e)||kb(e))return`${e}${t}`;if(me(e))return e}const pn=(e,t)=>{if(e.install=r=>{for(const n of[e,...Object.values(t??{})])r.component(n.name,n)},t)for(const[r,n]of Object.entries(t))e[r]=n;return e},Ub=e=>(e.install=ze,e),Kb=fn({size:{type:jt([Number,String])},color:{type:String}}),Yb=We({name:"ElIcon",inheritAttrs:!1}),zb=We({...Yb,props:Kb,setup(e){const t=e,r=Or("icon"),n=tt(()=>{const{size:o,color:i}=t;return!o&&!i?{}:{fontSize:Hb(o)?void 0:Wb(o),"--color":i}});return(o,i)=>(Se(),Ye("i",Pv({class:ee(r).b(),style:ee(n)},o.$attrs),[St(o.$slots,"default")],16))}});var Xb=dn(zb,[["__file","icon.vue"]]);const Jb=pn(Xb);/*! Element Plus Icons Vue v2.3.2 */var Qb=We({name:"MoreFilled",__name:"more-filled",setup(e){return(t,r)=>(Se(),Ye("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Pe("path",{fill:"currentColor",d:"M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224m336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224m336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224"})]))}}),Zb=Qb;const e_=jt([String,Object,Function]),qr=e=>e;var En=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function it(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}const t_=fn({header:{type:String,default:""},footer:{type:String,default:""},bodyStyle:{type:jt([String,Object,Array]),default:""},headerClass:String,bodyClass:String,footerClass:String,shadow:{type:String,values:["always","hover","never"],default:void 0}}),r_=We({name:"ElCard"}),n_=We({...r_,props:t_,setup(e){const t=$b("card"),r=Or("card");return(n,o)=>{var i;return Se(),Ye("div",{class:Ee([ee(r).b(),ee(r).is(`${n.shadow||((i=ee(t))==null?void 0:i.shadow)||"always"}-shadow`)])},[n.$slots.header||n.header?(Se(),Ye("div",{key:0,class:Ee([ee(r).e("header"),n.headerClass])},[St(n.$slots,"header",{},()=>[Hn(nr(n.header),1)])],2)):Qt("v-if",!0),Pe("div",{class:Ee([ee(r).e("body"),n.bodyClass]),style:ar(n.bodyStyle)},[St(n.$slots,"default")],6),n.$slots.footer||n.footer?(Se(),Ye("div",{key:1,class:Ee([ee(r).e("footer"),n.footerClass])},[St(n.$slots,"footer",{},()=>[Hn(nr(n.footer),1)])],2)):Qt("v-if",!0)],2)}}});var o_=dn(n_,[["__file","card.vue"]]);const qo=pn(o_),i_=fn({tag:{type:String,default:"div"},span:{type:Number,default:24},offset:{type:Number,default:0},pull:{type:Number,default:0},push:{type:Number,default:0},xs:{type:jt([Number,Object]),default:()=>qr({})},sm:{type:jt([Number,Object]),default:()=>qr({})},md:{type:jt([Number,Object]),default:()=>qr({})},lg:{type:jt([Number,Object]),default:()=>qr({})},xl:{type:jt([Number,Object]),default:()=>qr({})}}),Lv=Symbol("rowContextKey"),a_=We({name:"ElCol"}),s_=We({...a_,props:i_,setup(e){const t=e,{gutter:r}=rr(Lv,{gutter:tt(()=>0)}),n=Or("col"),o=tt(()=>{const a={};return r.value&&(a.paddingLeft=a.paddingRight=`${r.value/2}px`),a}),i=tt(()=>{const a=[];return["span","offset","pull","push"].forEach(c=>{const l=t[c];bu(l)&&(c==="span"?a.push(n.b(`${t[c]}`)):l>0&&a.push(n.b(`${c}-${t[c]}`)))}),["xs","sm","md","lg","xl"].forEach(c=>{bu(t[c])?a.push(n.b(`${c}-${t[c]}`)):le(t[c])&&Object.entries(t[c]).forEach(([l,f])=>{a.push(l!=="span"?n.b(`${c}-${l}-${f}`):n.b(`${c}-${f}`))})}),r.value&&a.push(n.is("guttered")),[n.b(),a]});return(a,s)=>(Se(),kt(ec(a.tag),{class:Ee(ee(i)),style:ar(ee(o))},{default:De(()=>[St(a.$slots,"default")]),_:3},8,["class","style"]))}});var u_=dn(s_,[["__file","col.vue"]]);const fl=pn(u_),c_=["start","center","end","space-around","space-between","space-evenly"],l_=["top","middle","bottom"],f_=fn({tag:{type:String,default:"div"},gutter:{type:Number,default:0},justify:{type:String,values:c_,default:"start"},align:{type:String,values:l_}}),d_=We({name:"ElRow"}),p_=We({...d_,props:f_,setup(e){const t=e,r=Or("row"),n=tt(()=>t.gutter);tc(Lv,{gutter:n});const o=tt(()=>{const a={};return t.gutter&&(a.marginRight=a.marginLeft=`-${t.gutter/2}px`),a}),i=tt(()=>[r.b(),r.is(`justify-${t.justify}`,t.justify!=="start"),r.is(`align-${t.align}`,!!t.align)]);return(a,s)=>(Se(),kt(ec(a.tag),{class:Ee(ee(i)),style:ar(ee(o))},{default:De(()=>[St(a.$slots,"default")]),_:3},8,["class","style"]))}});var h_=dn(p_,[["__file","row.vue"]]);const v_=pn(h_),g_="timeline",m_=We({name:"ElTimeline",setup(e,{slots:t}){const r=Or("timeline");return tc(g_,t),()=>sb("ul",{class:[r.b()]},[St(t,"default")])}}),y_=fn({timestamp:{type:String,default:""},hideTimestamp:Boolean,center:Boolean,placement:{type:String,values:["top","bottom"],default:"bottom"},type:{type:String,values:["primary","success","warning","danger","info"],default:""},color:{type:String,default:""},size:{type:String,values:["normal","large"],default:"normal"},icon:{type:e_},hollow:Boolean}),b_=We({name:"ElTimelineItem"}),__=We({...b_,props:y_,setup(e){const t=e,r=Or("timeline-item"),n=tt(()=>[r.e("node"),r.em("node",t.size||""),r.em("node",t.type||""),r.is("hollow",t.hollow)]);return(o,i)=>(Se(),Ye("li",{class:Ee([ee(r).b(),{[ee(r).e("center")]:o.center}])},[Pe("div",{class:Ee(ee(r).e("tail"))},null,2),o.$slots.dot?Qt("v-if",!0):(Se(),Ye("div",{key:0,class:Ee(ee(n)),style:ar({backgroundColor:o.color})},[o.icon?(Se(),kt(ee(Jb),{key:0,class:Ee(ee(r).e("icon"))},{default:De(()=>[(Se(),kt(ec(o.icon)))]),_:1},8,["class"])):Qt("v-if",!0)],6)),o.$slots.dot?(Se(),Ye("div",{key:1,class:Ee(ee(r).e("dot"))},[St(o.$slots,"dot")],2)):Qt("v-if",!0),Pe("div",{class:Ee(ee(r).e("wrapper"))},[!o.hideTimestamp&&o.placement==="top"?(Se(),Ye("div",{key:0,class:Ee([ee(r).e("timestamp"),ee(r).is("top")])},nr(o.timestamp),3)):Qt("v-if",!0),Pe("div",{class:Ee(ee(r).e("content"))},[St(o.$slots,"default")],2),!o.hideTimestamp&&o.placement==="bottom"?(Se(),Ye("div",{key:1,class:Ee([ee(r).e("timestamp"),ee(r).is("bottom")])},nr(o.timestamp),3)):Qt("v-if",!0)],2)],2))}});var qv=dn(__,[["__file","timeline-item.vue"]]);const w_=pn(m_,{TimelineItem:qv}),dl=Ub(qv);function pl(e,t,r,n,o,i,a){try{var s=e[i](a),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,o)}function ie(e){return function(){var t=this,r=arguments;return new Promise(function(n,o){var i=e.apply(t,r);function a(u){pl(i,n,o,a,s,"next",u)}function s(u){pl(i,n,o,a,s,"throw",u)}a(void 0)})}}var jo,hl;function S_(){if(hl)return jo;hl=1;function e(){}return jo=e,jo}var E_=S_();const rn=it(E_);function T_(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function Vn(e,t){if(e==null)return{};var r,n,o=T_(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function _u(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function x_(e){if(Array.isArray(e))return _u(e)}function O_(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function jv(e,t){if(e){if(typeof e=="string")return _u(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_u(e,t):void 0}}function C_(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function pt(e){return x_(e)||O_(e)||jv(e)||C_()}function Ie(e){"@babel/helpers - typeof";return Ie=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ie(e)}function P_(e,t){if(Ie(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ie(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Dv(e){var t=P_(e,"string");return Ie(t)=="symbol"?t:t+""}function Et(e,t,r){return(t=Dv(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function Te(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?vl(Object(r),!0).forEach(function(n){Et(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}var Do={exports:{}},Go={exports:{}},gl;function Gv(){return gl||(gl=1,(function(e){function t(r,n){this.v=r,this.k=n}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(Go)),Go.exports}var No={exports:{}},Fo={exports:{}},ml;function Nv(){return ml||(ml=1,(function(e){function t(r,n,o,i){var a=Object.defineProperty;try{a({},"",{})}catch{a=0}e.exports=t=function(u,c,l,f){function p(v,y){t(u,v,function(m){return this._invoke(v,y,m)})}c?a?a(u,c,{value:l,enumerable:!f,configurable:!f,writable:!f}):u[c]=l:(p("next",0),p("throw",1),p("return",2))},e.exports.__esModule=!0,e.exports.default=e.exports,t(r,n,o,i)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(Fo)),Fo.exports}var yl;function Fv(){return yl||(yl=1,(function(e){var t=Nv();function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,o,i=typeof Symbol=="function"?Symbol:{},a=i.iterator||"@@iterator",s=i.toStringTag||"@@toStringTag";function u(h,b,_,O){var E=b&&b.prototype instanceof l?b:l,M=Object.create(E.prototype);return t(M,"_invoke",(function(L,F,N){var j,D,G,Q=0,ce=N||[],ne=!1,pe={p:0,n:0,v:n,a:te,f:te.bind(n,4),d:function($,oe){return j=$,D=0,G=n,pe.n=oe,c}};function te(W,$){for(D=W,G=$,o=0;!ne&&Q&&!oe&&o<ce.length;o++){var oe,se=ce[o],ye=pe.p,he=se[2];W>3?(oe=he===$)&&(G=se[(D=se[4])?5:(D=3,3)],se[4]=se[5]=n):se[0]<=ye&&((oe=W<2&&ye<se[1])?(D=0,pe.v=$,pe.n=se[1]):ye<he&&(oe=W<3||se[0]>$||$>he)&&(se[4]=W,se[5]=$,pe.n=he,D=0))}if(oe||W>1)return c;throw ne=!0,$}return function(W,$,oe){if(Q>1)throw TypeError("Generator is already running");for(ne&&$===1&&te($,oe),D=$,G=oe;(o=D<2?n:G)||!ne;){j||(D?D<3?(D>1&&(pe.n=-1),te(D,G)):pe.n=G:pe.v=G);try{if(Q=2,j){if(D||(W="next"),o=j[W]){if(!(o=o.call(j,G)))throw TypeError("iterator result is not an object");if(!o.done)return o;G=o.value,D<2&&(D=0)}else D===1&&(o=j.return)&&o.call(j),D<2&&(G=TypeError("The iterator does not provide a '"+W+"' method"),D=1);j=n}else if((o=(ne=pe.n<0)?G:L.call(F,pe))!==c)break}catch(se){j=n,D=1,G=se}finally{Q=1}}return{value:o,done:ne}}})(h,_,O),!0),M}var c={};function l(){}function f(){}function p(){}o=Object.getPrototypeOf;var v=[][a]?o(o([][a]())):(t(o={},a,function(){return this}),o),y=p.prototype=l.prototype=Object.create(v);function m(h){return Object.setPrototypeOf?Object.setPrototypeOf(h,p):(h.__proto__=p,t(h,s,"GeneratorFunction")),h.prototype=Object.create(y),h}return f.prototype=p,t(y,"constructor",p),t(p,"constructor",f),f.displayName="GeneratorFunction",t(p,s,"GeneratorFunction"),t(y),t(y,s,"Generator"),t(y,a,function(){return this}),t(y,"toString",function(){return"[object Generator]"}),(e.exports=r=function(){return{w:u,m}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports})(No)),No.exports}var Ho={exports:{}},ko={exports:{}},Vo={exports:{}},bl;function Hv(){return bl||(bl=1,(function(e){var t=Gv(),r=Nv();function n(o,i){function a(u,c,l,f){try{var p=o[u](c),v=p.value;return v instanceof t?i.resolve(v.v).then(function(y){a("next",y,l,f)},function(y){a("throw",y,l,f)}):i.resolve(v).then(function(y){p.value=y,l(p)},function(y){return a("throw",y,l,f)})}catch(y){f(y)}}var s;this.next||(r(n.prototype),r(n.prototype,typeof Symbol=="function"&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),r(this,"_invoke",function(u,c,l){function f(){return new i(function(p,v){a(u,l,p,v)})}return s=s?s.then(f,f):f()},!0)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})(Vo)),Vo.exports}var _l;function kv(){return _l||(_l=1,(function(e){var t=Fv(),r=Hv();function n(o,i,a,s,u){return new r(t().w(o,i,a,s),u||Promise)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})(ko)),ko.exports}var wl;function A_(){return wl||(wl=1,(function(e){var t=kv();function r(n,o,i,a,s){var u=t(n,o,i,a,s);return u.next().then(function(c){return c.done?c.value:u.next()})}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports})(Ho)),Ho.exports}var Bo={exports:{}},Sl;function R_(){return Sl||(Sl=1,(function(e){function t(r){var n=Object(r),o=[];for(var i in n)o.unshift(i);return function a(){for(;o.length;)if((i=o.pop())in n)return a.value=i,a.done=!1,a;return a.done=!0,a}}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(Bo)),Bo.exports}var $o={exports:{}},Wo={exports:{}},El;function M_(){return El||(El=1,(function(e){function t(r){"@babel/helpers - typeof";return e.exports=t=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(Wo)),Wo.exports}var Tl;function I_(){return Tl||(Tl=1,(function(e){var t=M_().default;function r(n){if(n!=null){var o=n[typeof Symbol=="function"&&Symbol.iterator||"@@iterator"],i=0;if(o)return o.call(n);if(typeof n.next=="function")return n;if(!isNaN(n.length))return{next:function(){return n&&i>=n.length&&(n=void 0),{value:n&&n[i++],done:!n}}}}throw new TypeError(t(n)+" is not iterable")}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports})($o)),$o.exports}var xl;function L_(){return xl||(xl=1,(function(e){var t=Gv(),r=Fv(),n=A_(),o=kv(),i=Hv(),a=R_(),s=I_();function u(){var c=r(),l=c.m(u),f=(Object.getPrototypeOf?Object.getPrototypeOf(l):l.__proto__).constructor;function p(m){var h=typeof m=="function"&&m.constructor;return!!h&&(h===f||(h.displayName||h.name)==="GeneratorFunction")}var v={throw:1,return:2,break:3,continue:3};function y(m){var h,b;return function(_){h||(h={stop:function(){return b(_.a,2)},catch:function(){return _.v},abrupt:function(E,M){return b(_.a,v[E],M)},delegateYield:function(E,M,L){return h.resultName=M,b(_.d,s(E),L)},finish:function(E){return b(_.f,E)}},b=function(E,M,L){_.p=h.prev,_.n=h.next;try{return E(M,L)}finally{h.next=_.n}}),h.resultName&&(h[h.resultName]=_.v,h.resultName=void 0),h.sent=_.v,h.next=_.n;try{return m.call(this,h)}finally{_.p=h.prev,_.n=h.next}}}return(e.exports=u=function(){return{wrap:function(b,_,O,E){return c.w(y(b),_,O,E&&E.reverse())},isGeneratorFunction:p,mark:c.m,awrap:function(b,_){return new t(b,_)},AsyncIterator:i,async:function(b,_,O,E,M){return(p(_)?o:n)(y(b),_,O,E,M)},keys:a,values:s}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=u,e.exports.__esModule=!0,e.exports.default=e.exports})(Do)),Do.exports}var Uo,Ol;function q_(){if(Ol)return Uo;Ol=1;var e=L_()();Uo=e;try{regeneratorRuntime=e}catch{typeof globalThis=="object"?globalThis.regeneratorRuntime=e:Function("r","regeneratorRuntime = r")(e)}return Uo}var j_=q_();const V=it(j_);var D_=Object.freeze({__proto__:null,get start(){return ag},get ensureJQuerySupport(){return Jv},get setBootstrapMaxTime(){return $_},get setMountMaxTime(){return W_},get setUnmountMaxTime(){return U_},get setUnloadMaxTime(){return K_},get registerApplication(){return tg},get unregisterApplication(){return ng},get getMountedApps(){return Zv},get getAppStatus(){return cc},get unloadApplication(){return og},get checkActivityFunctions(){return rg},get getAppNames(){return eg},get pathToActiveWhen(){return ig},get navigateToUrl(){return uc},get triggerAppChange(){return Q_},get addErrorHandler(){return G_},get removeErrorHandler(){return N_},get mountRootParcel(){return B_},get NOT_LOADED(){return vt},get LOADING_SOURCE_CODE(){return io},get NOT_BOOTSTRAPPED(){return Sr},get BOOTSTRAPPING(){return Vv},get NOT_MOUNTED(){return Ct},get MOUNTING(){return F_},get UPDATING(){return Bv},get LOAD_ERROR(){return Er},get MOUNTED(){return rt},get UNLOADING(){return wu},get UNMOUNTING(){return $v},get SKIP_BECAUSE_BROKEN(){return Oe}});function dt(e){return(dt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function Tn(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Cl=(typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{}).CustomEvent,Lt=(function(){try{var e=new Cl("cat",{detail:{foo:"bar"}});return e.type==="cat"&&e.detail.foo==="bar"}catch{}return!1})()?Cl:typeof document<"u"&&typeof document.createEvent=="function"?function(e,t){var r=document.createEvent("CustomEvent");return t?r.initCustomEvent(e,t.bubbles,t.cancelable,t.detail):r.initCustomEvent(e,!1,!1,void 0),r}:function(e,t){var r=document.createEventObject();return r.type=e,t?(r.bubbles=!!t.bubbles,r.cancelable=!!t.cancelable,r.detail=t.detail):(r.bubbles=!1,r.cancelable=!1,r.detail=void 0),r},nn=[];function Vt(e,t,r){var n=wr(e,t,r);nn.length?nn.forEach((function(o){return o(n)})):setTimeout((function(){throw n}))}function G_(e){if(typeof e!="function")throw Error(X(28,!1));nn.push(e)}function N_(e){if(typeof e!="function")throw Error(X(29,!1));var t=!1;return nn=nn.filter((function(r){var n=r===e;return t=t||n,!n})),t}function X(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o];return"single-spa minified message #".concat(e,": ").concat("","See https://single-spa.js.org/error/?code=").concat(e).concat(n.length?"&arg=".concat(n.join("&arg=")):"")}function wr(e,t,r){var n,o="".concat(ao(t)," '").concat(ve(t),"' died in status ").concat(t.status,": ");if(e instanceof Error){try{e.message=o+e.message}catch{}n=e}else{console.warn(X(30,!1,t.status,ve(t)));try{n=Error(o+JSON.stringify(e))}catch{n=e}}return n.appOrParcelName=ve(t),t.status=r,n}var vt="NOT_LOADED",io="LOADING_SOURCE_CODE",Sr="NOT_BOOTSTRAPPED",Vv="BOOTSTRAPPING",Ct="NOT_MOUNTED",F_="MOUNTING",rt="MOUNTED",Bv="UPDATING",$v="UNMOUNTING",wu="UNLOADING",Er="LOAD_ERROR",Oe="SKIP_BECAUSE_BROKEN";function H_(e){return e.status===rt}function Su(e){try{return e.activeWhen(window.location)}catch(t){return Vt(t,e,Oe),!1}}function ve(e){return e.name}function Wv(e){return!!e.unmountThisParcel}function ao(e){return Wv(e)?"parcel":"application"}function hn(){for(var e=arguments.length-1;e>0;e--)for(var t in arguments[e])t!=="__proto__"&&(arguments[e-1][t]=arguments[e][t]);return arguments[0]}function so(e,t){for(var r=0;r<e.length;r++)if(t(e[r]))return e[r];return null}function er(e){return e&&(typeof e=="function"||(t=e,Array.isArray(t)&&!so(t,(function(r){return typeof r!="function"}))));var t}function Dt(e,t){var r=e[t]||[];(r=Array.isArray(r)?r:[r]).length===0&&(r=[function(){return Promise.resolve()}]);var n=ao(e),o=ve(e);return function(i){return r.reduce((function(a,s,u){return a.then((function(){var c=s(i);return Uv(c)?c:Promise.reject(X(15,!1,n,o,t,u))}))}),Promise.resolve())}}function Uv(e){return e&&typeof e.then=="function"&&typeof e.catch=="function"}function sc(e,t){return Promise.resolve().then((function(){return e.status!==Sr?e:(e.status=Vv,e.bootstrap?vn(e,"bootstrap").then(r).catch((function(n){if(t)throw wr(n,e,Oe);return Vt(n,e,Oe),e})):Promise.resolve().then(r))}));function r(){return e.status=Ct,e}}function uo(e,t){return Promise.resolve().then((function(){if(e.status!==rt)return e;e.status=$v;var r=Object.keys(e.parcels).map((function(o){return e.parcels[o].unmountThisParcel()}));return Promise.all(r).then(n,(function(o){return n().then((function(){var i=Error(o.message);if(t)throw wr(i,e,Oe);Vt(i,e,Oe)}))})).then((function(){return e}));function n(){return vn(e,"unmount").then((function(){e.status=Ct})).catch((function(o){if(t)throw wr(o,e,Oe);Vt(o,e,Oe)}))}}))}var Pl=!1,Al=!1;function Eu(e,t){return Promise.resolve().then((function(){return e.status!==Ct?e:(Pl||(window.dispatchEvent(new Lt("single-spa:before-first-mount")),Pl=!0),vn(e,"mount").then((function(){return e.status=rt,Al||(window.dispatchEvent(new Lt("single-spa:first-mount")),Al=!0),e})).catch((function(r){return e.status=rt,uo(e,!0).then(n,n);function n(){if(t)throw wr(r,e,Oe);return Vt(r,e,Oe),e}})))}))}var k_=0,V_={parcels:{}};function B_(){return Kv.apply(V_,arguments)}function Kv(e,t){var r=this;if(!e||dt(e)!=="object"&&typeof e!="function")throw Error(X(2,!1));if(e.name&&typeof e.name!="string")throw Error(X(3,!1,dt(e.name)));if(dt(t)!=="object")throw Error(X(4,!1,name,dt(t)));if(!t.domElement)throw Error(X(5,!1,name));var n,o=k_++,i=typeof e=="function",a=i?e:function(){return Promise.resolve(e)},s={id:o,parcels:{},status:i?io:Sr,customProps:t,parentName:ve(r),unmountThisParcel:function(){return p.then((function(){if(s.status!==rt)throw Error(X(6,!1,name,s.status));return uo(s,!0)})).then((function(y){return s.parentName&&delete r.parcels[s.id],y})).then((function(y){return c(y),y})).catch((function(y){throw s.status=Oe,l(y),y}))}};r.parcels[o]=s;var u=a();if(!u||typeof u.then!="function")throw Error(X(7,!1));var c,l,f=(u=u.then((function(y){if(!y)throw Error(X(8,!1));var m=y.name||"parcel-".concat(o);if(Object.prototype.hasOwnProperty.call(y,"bootstrap")&&!er(y.bootstrap))throw Error(X(9,!1,m));if(!er(y.mount))throw Error(X(10,!1,m));if(!er(y.unmount))throw Error(X(11,!1,m));if(y.update&&!er(y.update))throw Error(X(12,!1,m));var h=Dt(y,"bootstrap"),b=Dt(y,"mount"),_=Dt(y,"unmount");s.status=Sr,s.name=m,s.bootstrap=h,s.mount=b,s.unmount=_,s.timeouts=zv(y.timeouts),y.update&&(s.update=Dt(y,"update"),n.update=function(O){return s.customProps=O,zt((function(E){return Promise.resolve().then((function(){if(E.status!==rt)throw Error(X(32,!1,ve(E)));return E.status=Bv,vn(E,"update").then((function(){return E.status=rt,E})).catch((function(M){throw wr(M,E,Oe)}))}))})(s))})}))).then((function(){return sc(s,!0)})),p=f.then((function(){return Eu(s,!0)})),v=new Promise((function(y,m){c=y,l=m}));return n={mount:function(){return zt(Promise.resolve().then((function(){if(s.status!==Ct)throw Error(X(13,!1,name,s.status));return r.parcels[o]=s,Eu(s)})))},unmount:function(){return zt(s.unmountThisParcel())},getStatus:function(){return s.status},loadPromise:zt(u),bootstrapPromise:zt(f),mountPromise:zt(p),unmountPromise:zt(v)}}function zt(e){return e.then((function(){return null}))}function Yv(e){var t=ve(e),r=typeof e.customProps=="function"?e.customProps(t,window.location):e.customProps;(dt(r)!=="object"||r===null||Array.isArray(r))&&(r={},console.warn(X(40,!1),t,r));var n=hn({},r,{name:t,mountParcel:Kv.bind(e),singleSpa:D_});return Wv(e)&&(n.unmountSelf=e.unmountThisParcel),n}var Tr={bootstrap:{millis:4e3,dieOnTimeout:!1,warningMillis:1e3},mount:{millis:3e3,dieOnTimeout:!1,warningMillis:1e3},unmount:{millis:3e3,dieOnTimeout:!1,warningMillis:1e3},unload:{millis:3e3,dieOnTimeout:!1,warningMillis:1e3},update:{millis:3e3,dieOnTimeout:!1,warningMillis:1e3}};function $_(e,t,r){if(typeof e!="number"||e<=0)throw Error(X(16,!1));Tr.bootstrap={millis:e,dieOnTimeout:t,warningMillis:r||1e3}}function W_(e,t,r){if(typeof e!="number"||e<=0)throw Error(X(17,!1));Tr.mount={millis:e,dieOnTimeout:t,warningMillis:r||1e3}}function U_(e,t,r){if(typeof e!="number"||e<=0)throw Error(X(18,!1));Tr.unmount={millis:e,dieOnTimeout:t,warningMillis:r||1e3}}function K_(e,t,r){if(typeof e!="number"||e<=0)throw Error(X(19,!1));Tr.unload={millis:e,dieOnTimeout:t,warningMillis:r||1e3}}function vn(e,t){var r=e.timeouts[t],n=r.warningMillis,o=ao(e);return new Promise((function(i,a){var s=!1,u=!1;e[t](Yv(e)).then((function(f){s=!0,i(f)})).catch((function(f){s=!0,a(f)})),setTimeout((function(){return l(1)}),n),setTimeout((function(){return l(!0)}),r.millis);var c=X(31,!1,t,o,ve(e),r.millis);function l(f){if(!s){if(f===!0)u=!0,r.dieOnTimeout?a(Error(c)):console.error(c);else if(!u){var p=f,v=p*n;console.warn(c),v+n<r.millis&&setTimeout((function(){return l(p+1)}),n)}}}}))}function zv(e){var t={};for(var r in Tr)t[r]=hn({},Tr[r],e&&e[r]||{});return t}function Tu(e){return Promise.resolve().then((function(){return e.loadPromise?e.loadPromise:e.status!==vt&&e.status!==Er?e:(e.status=io,e.loadPromise=Promise.resolve().then((function(){var n=e.loadApp(Yv(e));if(!Uv(n))throw r=!0,Error(X(33,!1,ve(e)));return n.then((function(o){var i;e.loadErrorTime=null,dt(t=o)!=="object"&&(i=34),Object.prototype.hasOwnProperty.call(t,"bootstrap")&&!er(t.bootstrap)&&(i=35),er(t.mount)||(i=36),er(t.unmount)||(i=37);var a=ao(t);if(i){var s;try{s=JSON.stringify(t)}catch{}return console.error(X(i,!1,a,ve(e),s),t),Vt(void 0,e,Oe),e}return t.devtools&&t.devtools.overlays&&(e.devtools.overlays=hn({},e.devtools.overlays,t.devtools.overlays)),e.status=Sr,e.bootstrap=Dt(t,"bootstrap"),e.mount=Dt(t,"mount"),e.unmount=Dt(t,"unmount"),e.unload=Dt(t,"unload"),e.timeouts=zv(t.timeouts),delete e.loadPromise,e}))})).catch((function(n){var o;return delete e.loadPromise,r?o=Oe:(o=Er,e.loadErrorTime=new Date().getTime()),Vt(n,e,o),e})));var t,r}))}var Xv,Cr=typeof window<"u",Gr={hashchange:[],popstate:[]},Bn=["hashchange","popstate"];function uc(e){var t;if(typeof e=="string")t=e;else if(this&&this.href)t=this.href;else{if(!(e&&e.currentTarget&&e.currentTarget.href&&e.preventDefault))throw Error(X(14,!1));t=e.currentTarget.href,e.preventDefault()}var r=Ll(window.location.href),n=Ll(t);t.indexOf("#")===0?window.location.hash=n.hash:r.host!==n.host&&n.host?window.location.href=t:n.pathname===r.pathname&&n.search===r.search?window.location.hash=n.hash:window.history.pushState(null,null,t)}function Rl(e){var t=this;if(e){var r=e[0].type;Bn.indexOf(r)>=0&&Gr[r].forEach((function(n){try{n.apply(t,e)}catch(o){setTimeout((function(){throw o}))}}))}}function Ml(){Bt([],arguments)}function Il(e,t){return function(){var r=window.location.href,n=e.apply(this,arguments),o=window.location.href;return Xv&&r===o||(sg()?window.dispatchEvent(Y_(window.history.state,t)):Bt([])),n}}function Y_(e,t){var r;try{r=new PopStateEvent("popstate",{state:e})}catch{(r=document.createEvent("PopStateEvent")).initPopStateEvent("popstate",!1,!1,e)}return r.singleSpa=!0,r.singleSpaTrigger=t,r}if(Cr){window.addEventListener("hashchange",Ml),window.addEventListener("popstate",Ml);var z_=window.addEventListener,X_=window.removeEventListener;window.addEventListener=function(e,t){if(!(typeof t=="function"&&Bn.indexOf(e)>=0)||so(Gr[e],(function(r){return r===t})))return z_.apply(this,arguments);Gr[e].push(t)},window.removeEventListener=function(e,t){if(!(typeof t=="function"&&Bn.indexOf(e)>=0))return X_.apply(this,arguments);Gr[e]=Gr[e].filter((function(r){return r!==t}))},window.history.pushState=Il(window.history.pushState,"pushState"),window.history.replaceState=Il(window.history.replaceState,"replaceState"),window.singleSpaNavigate?console.warn(X(41,!1)):window.singleSpaNavigate=uc}function Ll(e){var t=document.createElement("a");return t.href=e,t}var ql=!1;function Jv(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.jQuery;if(e||window.$&&window.$.fn&&window.$.fn.jquery&&(e=window.$),e&&!ql){var t=e.fn.on,r=e.fn.off;e.fn.on=function(n,o){return jl.call(this,t,window.addEventListener,n,o,arguments)},e.fn.off=function(n,o){return jl.call(this,r,window.removeEventListener,n,o,arguments)},ql=!0}}function jl(e,t,r,n,o){return typeof r!="string"?e.apply(this,o):(r.split(/\s+/).forEach((function(i){Bn.indexOf(i)>=0&&(t(i,n),r=r.replace(i,""))})),r.trim()===""?this:e.apply(this,o))}var xr={};function xu(e){return Promise.resolve().then((function(){var t=xr[ve(e)];if(!t)return e;if(e.status===vt)return Dl(e,t),e;if(e.status===wu)return t.promise.then((function(){return e}));if(e.status!==Ct&&e.status!==Er)return e;var r=e.status===Er?Promise.resolve():vn(e,"unload");return e.status=wu,r.then((function(){return Dl(e,t),e})).catch((function(n){return(function(o,i,a){delete xr[ve(o)],delete o.bootstrap,delete o.mount,delete o.unmount,delete o.unload,Vt(a,o,Oe),i.reject(a)})(e,t,n),e}))}))}function Dl(e,t){delete xr[ve(e)],delete e.bootstrap,delete e.mount,delete e.unmount,delete e.unload,e.status=vt,t.resolve()}function Gl(e,t,r,n){xr[ve(e)]={app:e,resolve:r,reject:n},Object.defineProperty(xr[ve(e)],"promise",{get:t})}function Qv(e){return xr[e]}var nt=[];function J_(){var e=[],t=[],r=[],n=[],o=new Date().getTime();return nt.forEach((function(i){var a=i.status!==Oe&&Su(i);switch(i.status){case Er:a&&o-i.loadErrorTime>=200&&r.push(i);break;case vt:case io:a&&r.push(i);break;case Sr:case Ct:!a&&Qv(ve(i))?e.push(i):a&&n.push(i);break;case rt:a||t.push(i)}})),{appsToUnload:e,appsToUnmount:t,appsToLoad:r,appsToMount:n}}function Zv(){return nt.filter(H_).map(ve)}function eg(){return nt.map(ve)}function cc(e){var t=so(nt,(function(r){return ve(r)===e}));return t?t.status:null}function tg(e,t,r,n){var o=(function(i,a,s,u){var c,l={name:null,loadApp:null,activeWhen:null,customProps:null};return dt(i)==="object"?((function(f){if(Array.isArray(f)||f===null)throw Error(X(39,!1));var p=["name","app","activeWhen","customProps"],v=Object.keys(f).reduce((function(m,h){return p.indexOf(h)>=0?m:m.concat(h)}),[]);if(v.length!==0)throw Error(X(38,!1,p.join(", "),v.join(", ")));if(typeof f.name!="string"||f.name.length===0||dt(f.app)!=="object"&&typeof f.app!="function")throw Error(X(20,!1));var y=function(m){return typeof m=="string"||typeof m=="function"};if(!(y(f.activeWhen)||Array.isArray(f.activeWhen)&&f.activeWhen.every(y)))throw Error(X(24,!1));if(!Fl(f.customProps))throw Error(X(22,!1))})(i),l.name=i.name,l.loadApp=i.app,l.activeWhen=i.activeWhen,l.customProps=i.customProps):((function(f,p,v,y){if(typeof f!="string"||f.length===0)throw Error(X(20,!1));if(!p)throw Error(X(23,!1));if(typeof v!="function")throw Error(X(24,!1));if(!Fl(y))throw Error(X(22,!1))})(i,a,s,u),l.name=i,l.loadApp=a,l.activeWhen=s,l.customProps=u),l.loadApp=typeof(c=l.loadApp)!="function"?function(){return Promise.resolve(c)}:c,l.customProps=(function(f){return f||{}})(l.customProps),l.activeWhen=(function(f){var p=Array.isArray(f)?f:[f];return p=p.map((function(v){return typeof v=="function"?v:ig(v)})),function(v){return p.some((function(y){return y(v)}))}})(l.activeWhen),l})(e,t,r,n);if(eg().indexOf(o.name)!==-1)throw Error(X(21,!1,o.name));nt.push(hn({loadErrorTime:null,status:vt,parcels:{},devtools:{overlays:{options:{},selectors:[]}}},o)),Cr&&(Jv(),Bt())}function rg(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.location;return nt.filter((function(t){return t.activeWhen(e)})).map(ve)}function ng(e){if(nt.filter((function(t){return ve(t)===e})).length===0)throw Error(X(25,!1,e));return og(e).then((function(){var t=nt.map(ve).indexOf(e);nt.splice(t,1)}))}function og(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{waitForUnmount:!1};if(typeof e!="string")throw Error(X(26,!1));var r=so(nt,(function(a){return ve(a)===e}));if(!r)throw Error(X(27,!1,e));var n,o=Qv(ve(r));if(t&&t.waitForUnmount){if(o)return o.promise;var i=new Promise((function(a,s){Gl(r,(function(){return i}),a,s)}));return i}return o?(n=o.promise,Nl(r,o.resolve,o.reject)):n=new Promise((function(a,s){Gl(r,(function(){return n}),a,s),Nl(r,a,s)})),n}function Nl(e,t,r){uo(e).then(xu).then((function(){t(),setTimeout((function(){Bt()}))})).catch(r)}function Fl(e){return!e||typeof e=="function"||dt(e)==="object"&&e!==null&&!Array.isArray(e)}function ig(e,t){var r=(function(n,o){var i=0,a=!1,s="^";n[0]!=="/"&&(n="/"+n);for(var u=0;u<n.length;u++){var c=n[u];(!a&&c===":"||a&&c==="/")&&l(u)}return l(n.length),new RegExp(s,"i");function l(f){var p=n.slice(i,f).replace(/[|\\{}()[\]^$+*?.]/g,"\\$&");if(s+=a?"[^/]+/?":p,f===n.length)if(a)o&&(s+="$");else{var v=o?"":".*";s=s.charAt(s.length-1)==="/"?"".concat(s).concat(v,"$"):"".concat(s,"(/").concat(v,")?(#.*)?$")}a=!a,i=f}})(e,t);return function(n){var o=n.origin;o||(o="".concat(n.protocol,"//").concat(n.host));var i=n.href.replace(o,"").replace(n.search,"").split("?")[0];return r.test(i)}}var Ko=!1,xn=[],Hl=Cr&&window.location.href;function Q_(){return Bt()}function Bt(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0;if(Ko)return new Promise((function(b,_){xn.push({resolve:b,reject:_,eventArguments:t})}));var r,n=J_(),o=n.appsToUnload,i=n.appsToUnmount,a=n.appsToLoad,s=n.appsToMount,u=!1,c=Hl,l=Hl=window.location.href;return sg()?(Ko=!0,r=o.concat(a,i,s),v()):(r=a,p());function f(){u=!0}function p(){return Promise.resolve().then((function(){var b=a.map(Tu);return Promise.all(b).then(m).then((function(){return[]})).catch((function(_){throw m(),_}))}))}function v(){return Promise.resolve().then((function(){if(window.dispatchEvent(new Lt(r.length===0?"single-spa:before-no-app-change":"single-spa:before-app-change",h(!0))),window.dispatchEvent(new Lt("single-spa:before-routing-event",h(!0,{cancelNavigation:f}))),u)return window.dispatchEvent(new Lt("single-spa:before-mount-routing-event",h(!0))),y(),void uc(c);var b=o.map(xu),_=i.map(uo).map((function(L){return L.then(xu)})).concat(b),O=Promise.all(_);O.then((function(){window.dispatchEvent(new Lt("single-spa:before-mount-routing-event",h(!0)))}));var E=a.map((function(L){return Tu(L).then((function(F){return kl(F,O)}))})),M=s.filter((function(L){return a.indexOf(L)<0})).map((function(L){return kl(L,O)}));return O.catch((function(L){throw m(),L})).then((function(){return m(),Promise.all(E.concat(M)).catch((function(L){throw e.forEach((function(F){return F.reject(L)})),L})).then(y)}))}))}function y(){var b=Zv();e.forEach((function(E){return E.resolve(b)}));try{var _=r.length===0?"single-spa:no-app-change":"single-spa:app-change";window.dispatchEvent(new Lt(_,h())),window.dispatchEvent(new Lt("single-spa:routing-event",h()))}catch(E){setTimeout((function(){throw E}))}if(Ko=!1,xn.length>0){var O=xn;xn=[],Bt(O)}return b}function m(){e.forEach((function(b){Rl(b.eventArguments)})),Rl(t)}function h(){var b,_=arguments.length>0&&arguments[0]!==void 0&&arguments[0],O=arguments.length>1?arguments[1]:void 0,E={},M=(Tn(b={},rt,[]),Tn(b,Ct,[]),Tn(b,vt,[]),Tn(b,Oe,[]),b);_?(a.concat(s).forEach((function(N,j){F(N,rt)})),o.forEach((function(N){F(N,vt)})),i.forEach((function(N){F(N,Ct)}))):r.forEach((function(N){F(N)}));var L={detail:{newAppStatuses:E,appsByNewStatus:M,totalAppChanges:r.length,originalEvent:t?.[0],oldUrl:c,newUrl:l,navigationIsCanceled:u}};return O&&hn(L.detail,O),L;function F(N,j){var D=ve(N);j=j||cc(D),E[D]=j,(M[j]=M[j]||[]).push(D)}}}function kl(e,t){return Su(e)?sc(e).then((function(r){return t.then((function(){return Su(r)?Eu(r):r}))})):t.then((function(){return e}))}var lc=!1;function ag(e){var t;lc=!0,e&&e.urlRerouteOnly&&(t=e.urlRerouteOnly,Xv=t),Cr&&Bt()}function sg(){return lc}Cr&&setTimeout((function(){lc||console.warn(X(1,!1))}),5e3);var Z_={getRawAppData:function(){return[].concat(nt)},reroute:Bt,NOT_LOADED:vt,toLoadPromise:Tu,toBootstrapPromise:sc,unregisterApplication:ng};Cr&&window.__SINGLE_SPA_DEVTOOLS__&&(window.__SINGLE_SPA_DEVTOOLS__.exposedMethods=Z_);var Yo,Vl;function co(){if(Vl)return Yo;Vl=1;function e(t,r){for(var n=-1,o=r.length,i=t.length;++n<o;)t[i+n]=r[n];return t}return Yo=e,Yo}var zo,Bl;function ug(){if(Bl)return zo;Bl=1;var e=typeof En=="object"&&En&&En.Object===Object&&En;return zo=e,zo}var Xo,$l;function gt(){if($l)return Xo;$l=1;var e=ug(),t=typeof self=="object"&&self&&self.Object===Object&&self,r=e||t||Function("return this")();return Xo=r,Xo}var Jo,Wl;function gn(){if(Wl)return Jo;Wl=1;var e=gt(),t=e.Symbol;return Jo=t,Jo}var Qo,Ul;function ew(){if(Ul)return Qo;Ul=1;var e=gn(),t=Object.prototype,r=t.hasOwnProperty,n=t.toString,o=e?e.toStringTag:void 0;function i(a){var s=r.call(a,o),u=a[o];try{a[o]=void 0;var c=!0}catch{}var l=n.call(a);return c&&(s?a[o]=u:delete a[o]),l}return Qo=i,Qo}var Zo,Kl;function tw(){if(Kl)return Zo;Kl=1;var e=Object.prototype,t=e.toString;function r(n){return t.call(n)}return Zo=r,Zo}var ei,Yl;function Pr(){if(Yl)return ei;Yl=1;var e=gn(),t=ew(),r=tw(),n="[object Null]",o="[object Undefined]",i=e?e.toStringTag:void 0;function a(s){return s==null?s===void 0?o:n:i&&i in Object(s)?t(s):r(s)}return ei=a,ei}var ti,zl;function $t(){if(zl)return ti;zl=1;function e(t){return t!=null&&typeof t=="object"}return ti=e,ti}var ri,Xl;function rw(){if(Xl)return ri;Xl=1;var e=Pr(),t=$t(),r="[object Arguments]";function n(o){return t(o)&&e(o)==r}return ri=n,ri}var ni,Jl;function fc(){if(Jl)return ni;Jl=1;var e=rw(),t=$t(),r=Object.prototype,n=r.hasOwnProperty,o=r.propertyIsEnumerable,i=e((function(){return arguments})())?e:function(a){return t(a)&&n.call(a,"callee")&&!o.call(a,"callee")};return ni=i,ni}var oi,Ql;function Wt(){if(Ql)return oi;Ql=1;var e=Array.isArray;return oi=e,oi}var ii,Zl;function nw(){if(Zl)return ii;Zl=1;var e=gn(),t=fc(),r=Wt(),n=e?e.isConcatSpreadable:void 0;function o(i){return r(i)||t(i)||!!(n&&i&&i[n])}return ii=o,ii}var ai,ef;function ow(){if(ef)return ai;ef=1;var e=co(),t=nw();function r(n,o,i,a,s){var u=-1,c=n.length;for(i||(i=t),s||(s=[]);++u<c;){var l=n[u];o>0&&i(l)?o>1?r(l,o-1,i,a,s):e(s,l):a||(s[s.length]=l)}return s}return ai=r,ai}var si,tf;function dc(){if(tf)return si;tf=1;function e(t,r){var n=-1,o=t.length;for(r||(r=Array(o));++n<o;)r[n]=t[n];return r}return si=e,si}var ui,rf;function iw(){if(rf)return ui;rf=1;var e=co(),t=ow(),r=dc(),n=Wt();function o(){var i=arguments.length;if(!i)return[];for(var a=Array(i-1),s=arguments[0],u=i;u--;)a[u-1]=arguments[u];return e(n(s)?r(s):[s],t(a,1))}return ui=o,ui}var aw=iw();const cg=it(aw);var ci,nf;function sw(){if(nf)return ci;nf=1;function e(){this.__data__=[],this.size=0}return ci=e,ci}var li,of;function lo(){if(of)return li;of=1;function e(t,r){return t===r||t!==t&&r!==r}return li=e,li}var fi,af;function fo(){if(af)return fi;af=1;var e=lo();function t(r,n){for(var o=r.length;o--;)if(e(r[o][0],n))return o;return-1}return fi=t,fi}var di,sf;function uw(){if(sf)return di;sf=1;var e=fo(),t=Array.prototype,r=t.splice;function n(o){var i=this.__data__,a=e(i,o);if(a<0)return!1;var s=i.length-1;return a==s?i.pop():r.call(i,a,1),--this.size,!0}return di=n,di}var pi,uf;function cw(){if(uf)return pi;uf=1;var e=fo();function t(r){var n=this.__data__,o=e(n,r);return o<0?void 0:n[o][1]}return pi=t,pi}var hi,cf;function lw(){if(cf)return hi;cf=1;var e=fo();function t(r){return e(this.__data__,r)>-1}return hi=t,hi}var vi,lf;function fw(){if(lf)return vi;lf=1;var e=fo();function t(r,n){var o=this.__data__,i=e(o,r);return i<0?(++this.size,o.push([r,n])):o[i][1]=n,this}return vi=t,vi}var gi,ff;function po(){if(ff)return gi;ff=1;var e=sw(),t=uw(),r=cw(),n=lw(),o=fw();function i(a){var s=-1,u=a==null?0:a.length;for(this.clear();++s<u;){var c=a[s];this.set(c[0],c[1])}}return i.prototype.clear=e,i.prototype.delete=t,i.prototype.get=r,i.prototype.has=n,i.prototype.set=o,gi=i,gi}var mi,df;function dw(){if(df)return mi;df=1;var e=po();function t(){this.__data__=new e,this.size=0}return mi=t,mi}var yi,pf;function pw(){if(pf)return yi;pf=1;function e(t){var r=this.__data__,n=r.delete(t);return this.size=r.size,n}return yi=e,yi}var bi,hf;function hw(){if(hf)return bi;hf=1;function e(t){return this.__data__.get(t)}return bi=e,bi}var _i,vf;function vw(){if(vf)return _i;vf=1;function e(t){return this.__data__.has(t)}return _i=e,_i}var wi,gf;function Rt(){if(gf)return wi;gf=1;function e(t){var r=typeof t;return t!=null&&(r=="object"||r=="function")}return wi=e,wi}var Si,mf;function ho(){if(mf)return Si;mf=1;var e=Pr(),t=Rt(),r="[object AsyncFunction]",n="[object Function]",o="[object GeneratorFunction]",i="[object Proxy]";function a(s){if(!t(s))return!1;var u=e(s);return u==n||u==o||u==r||u==i}return Si=a,Si}var Ei,yf;function gw(){if(yf)return Ei;yf=1;var e=gt(),t=e["__core-js_shared__"];return Ei=t,Ei}var Ti,bf;function mw(){if(bf)return Ti;bf=1;var e=gw(),t=(function(){var n=/[^.]+$/.exec(e&&e.keys&&e.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""})();function r(n){return!!t&&t in n}return Ti=r,Ti}var xi,_f;function lg(){if(_f)return xi;_f=1;var e=Function.prototype,t=e.toString;function r(n){if(n!=null){try{return t.call(n)}catch{}try{return n+""}catch{}}return""}return xi=r,xi}var Oi,wf;function yw(){if(wf)return Oi;wf=1;var e=ho(),t=mw(),r=Rt(),n=lg(),o=/[\\^$.*+?()[\]{}|]/g,i=/^\[object .+?Constructor\]$/,a=Function.prototype,s=Object.prototype,u=a.toString,c=s.hasOwnProperty,l=RegExp("^"+u.call(c).replace(o,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function f(p){if(!r(p)||t(p))return!1;var v=e(p)?l:i;return v.test(n(p))}return Oi=f,Oi}var Ci,Sf;function bw(){if(Sf)return Ci;Sf=1;function e(t,r){return t?.[r]}return Ci=e,Ci}var Pi,Ef;function sr(){if(Ef)return Pi;Ef=1;var e=yw(),t=bw();function r(n,o){var i=t(n,o);return e(i)?i:void 0}return Pi=r,Pi}var Ai,Tf;function pc(){if(Tf)return Ai;Tf=1;var e=sr(),t=gt(),r=e(t,"Map");return Ai=r,Ai}var Ri,xf;function vo(){if(xf)return Ri;xf=1;var e=sr(),t=e(Object,"create");return Ri=t,Ri}var Mi,Of;function _w(){if(Of)return Mi;Of=1;var e=vo();function t(){this.__data__=e?e(null):{},this.size=0}return Mi=t,Mi}var Ii,Cf;function ww(){if(Cf)return Ii;Cf=1;function e(t){var r=this.has(t)&&delete this.__data__[t];return this.size-=r?1:0,r}return Ii=e,Ii}var Li,Pf;function Sw(){if(Pf)return Li;Pf=1;var e=vo(),t="__lodash_hash_undefined__",r=Object.prototype,n=r.hasOwnProperty;function o(i){var a=this.__data__;if(e){var s=a[i];return s===t?void 0:s}return n.call(a,i)?a[i]:void 0}return Li=o,Li}var qi,Af;function Ew(){if(Af)return qi;Af=1;var e=vo(),t=Object.prototype,r=t.hasOwnProperty;function n(o){var i=this.__data__;return e?i[o]!==void 0:r.call(i,o)}return qi=n,qi}var ji,Rf;function Tw(){if(Rf)return ji;Rf=1;var e=vo(),t="__lodash_hash_undefined__";function r(n,o){var i=this.__data__;return this.size+=this.has(n)?0:1,i[n]=e&&o===void 0?t:o,this}return ji=r,ji}var Di,Mf;function xw(){if(Mf)return Di;Mf=1;var e=_w(),t=ww(),r=Sw(),n=Ew(),o=Tw();function i(a){var s=-1,u=a==null?0:a.length;for(this.clear();++s<u;){var c=a[s];this.set(c[0],c[1])}}return i.prototype.clear=e,i.prototype.delete=t,i.prototype.get=r,i.prototype.has=n,i.prototype.set=o,Di=i,Di}var Gi,If;function Ow(){if(If)return Gi;If=1;var e=xw(),t=po(),r=pc();function n(){this.size=0,this.__data__={hash:new e,map:new(r||t),string:new e}}return Gi=n,Gi}var Ni,Lf;function Cw(){if(Lf)return Ni;Lf=1;function e(t){var r=typeof t;return r=="string"||r=="number"||r=="symbol"||r=="boolean"?t!=="__proto__":t===null}return Ni=e,Ni}var Fi,qf;function go(){if(qf)return Fi;qf=1;var e=Cw();function t(r,n){var o=r.__data__;return e(n)?o[typeof n=="string"?"string":"hash"]:o.map}return Fi=t,Fi}var Hi,jf;function Pw(){if(jf)return Hi;jf=1;var e=go();function t(r){var n=e(this,r).delete(r);return this.size-=n?1:0,n}return Hi=t,Hi}var ki,Df;function Aw(){if(Df)return ki;Df=1;var e=go();function t(r){return e(this,r).get(r)}return ki=t,ki}var Vi,Gf;function Rw(){if(Gf)return Vi;Gf=1;var e=go();function t(r){return e(this,r).has(r)}return Vi=t,Vi}var Bi,Nf;function Mw(){if(Nf)return Bi;Nf=1;var e=go();function t(r,n){var o=e(this,r),i=o.size;return o.set(r,n),this.size+=o.size==i?0:1,this}return Bi=t,Bi}var $i,Ff;function hc(){if(Ff)return $i;Ff=1;var e=Ow(),t=Pw(),r=Aw(),n=Rw(),o=Mw();function i(a){var s=-1,u=a==null?0:a.length;for(this.clear();++s<u;){var c=a[s];this.set(c[0],c[1])}}return i.prototype.clear=e,i.prototype.delete=t,i.prototype.get=r,i.prototype.has=n,i.prototype.set=o,$i=i,$i}var Wi,Hf;function Iw(){if(Hf)return Wi;Hf=1;var e=po(),t=pc(),r=hc(),n=200;function o(i,a){var s=this.__data__;if(s instanceof e){var u=s.__data__;if(!t||u.length<n-1)return u.push([i,a]),this.size=++s.size,this;s=this.__data__=new r(u)}return s.set(i,a),this.size=s.size,this}return Wi=o,Wi}var Ui,kf;function fg(){if(kf)return Ui;kf=1;var e=po(),t=dw(),r=pw(),n=hw(),o=vw(),i=Iw();function a(s){var u=this.__data__=new e(s);this.size=u.size}return a.prototype.clear=t,a.prototype.delete=r,a.prototype.get=n,a.prototype.has=o,a.prototype.set=i,Ui=a,Ui}var Ki,Vf;function dg(){if(Vf)return Ki;Vf=1;var e=sr(),t=(function(){try{var r=e(Object,"defineProperty");return r({},"",{}),r}catch{}})();return Ki=t,Ki}var Yi,Bf;function vc(){if(Bf)return Yi;Bf=1;var e=dg();function t(r,n,o){n=="__proto__"&&e?e(r,n,{configurable:!0,enumerable:!0,value:o,writable:!0}):r[n]=o}return Yi=t,Yi}var zi,$f;function pg(){if($f)return zi;$f=1;var e=vc(),t=lo();function r(n,o,i){(i!==void 0&&!t(n[o],i)||i===void 0&&!(o in n))&&e(n,o,i)}return zi=r,zi}var Xi,Wf;function Lw(){if(Wf)return Xi;Wf=1;function e(t){return function(r,n,o){for(var i=-1,a=Object(r),s=o(r),u=s.length;u--;){var c=s[t?u:++i];if(n(a[c],c,a)===!1)break}return r}}return Xi=e,Xi}var Ji,Uf;function hg(){if(Uf)return Ji;Uf=1;var e=Lw(),t=e();return Ji=t,Ji}var Nr={exports:{}};Nr.exports;var Kf;function vg(){return Kf||(Kf=1,(function(e,t){var r=gt(),n=t&&!t.nodeType&&t,o=n&&!0&&e&&!e.nodeType&&e,i=o&&o.exports===n,a=i?r.Buffer:void 0,s=a?a.allocUnsafe:void 0;function u(c,l){if(l)return c.slice();var f=c.length,p=s?s(f):new c.constructor(f);return c.copy(p),p}e.exports=u})(Nr,Nr.exports)),Nr.exports}var Qi,Yf;function qw(){if(Yf)return Qi;Yf=1;var e=gt(),t=e.Uint8Array;return Qi=t,Qi}var Zi,zf;function gc(){if(zf)return Zi;zf=1;var e=qw();function t(r){var n=new r.constructor(r.byteLength);return new e(n).set(new e(r)),n}return Zi=t,Zi}var ea,Xf;function gg(){if(Xf)return ea;Xf=1;var e=gc();function t(r,n){var o=n?e(r.buffer):r.buffer;return new r.constructor(o,r.byteOffset,r.length)}return ea=t,ea}var ta,Jf;function jw(){if(Jf)return ta;Jf=1;var e=Rt(),t=Object.create,r=(function(){function n(){}return function(o){if(!e(o))return{};if(t)return t(o);n.prototype=o;var i=new n;return n.prototype=void 0,i}})();return ta=r,ta}var ra,Qf;function mg(){if(Qf)return ra;Qf=1;function e(t,r){return function(n){return t(r(n))}}return ra=e,ra}var na,Zf;function mc(){if(Zf)return na;Zf=1;var e=mg(),t=e(Object.getPrototypeOf,Object);return na=t,na}var oa,ed;function yc(){if(ed)return oa;ed=1;var e=Object.prototype;function t(r){var n=r&&r.constructor,o=typeof n=="function"&&n.prototype||e;return r===o}return oa=t,oa}var ia,td;function yg(){if(td)return ia;td=1;var e=jw(),t=mc(),r=yc();function n(o){return typeof o.constructor=="function"&&!r(o)?e(t(o)):{}}return ia=n,ia}var aa,rd;function bg(){if(rd)return aa;rd=1;var e=9007199254740991;function t(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=e}return aa=t,aa}var sa,nd;function mn(){if(nd)return sa;nd=1;var e=ho(),t=bg();function r(n){return n!=null&&t(n.length)&&!e(n)}return sa=r,sa}var ua,od;function _g(){if(od)return ua;od=1;var e=mn(),t=$t();function r(n){return t(n)&&e(n)}return ua=r,ua}var Fr={exports:{}},ca,id;function Dw(){if(id)return ca;id=1;function e(){return!1}return ca=e,ca}Fr.exports;var ad;function bc(){return ad||(ad=1,(function(e,t){var r=gt(),n=Dw(),o=t&&!t.nodeType&&t,i=o&&!0&&e&&!e.nodeType&&e,a=i&&i.exports===o,s=a?r.Buffer:void 0,u=s?s.isBuffer:void 0,c=u||n;e.exports=c})(Fr,Fr.exports)),Fr.exports}var la,sd;function Gw(){if(sd)return la;sd=1;var e=Pr(),t=mc(),r=$t(),n="[object Object]",o=Function.prototype,i=Object.prototype,a=o.toString,s=i.hasOwnProperty,u=a.call(Object);function c(l){if(!r(l)||e(l)!=n)return!1;var f=t(l);if(f===null)return!0;var p=s.call(f,"constructor")&&f.constructor;return typeof p=="function"&&p instanceof p&&a.call(p)==u}return la=c,la}var fa,ud;function Nw(){if(ud)return fa;ud=1;var e=Pr(),t=bg(),r=$t(),n="[object Arguments]",o="[object Array]",i="[object Boolean]",a="[object Date]",s="[object Error]",u="[object Function]",c="[object Map]",l="[object Number]",f="[object Object]",p="[object RegExp]",v="[object Set]",y="[object String]",m="[object WeakMap]",h="[object ArrayBuffer]",b="[object DataView]",_="[object Float32Array]",O="[object Float64Array]",E="[object Int8Array]",M="[object Int16Array]",L="[object Int32Array]",F="[object Uint8Array]",N="[object Uint8ClampedArray]",j="[object Uint16Array]",D="[object Uint32Array]",G={};G[_]=G[O]=G[E]=G[M]=G[L]=G[F]=G[N]=G[j]=G[D]=!0,G[n]=G[o]=G[h]=G[i]=G[b]=G[a]=G[s]=G[u]=G[c]=G[l]=G[f]=G[p]=G[v]=G[y]=G[m]=!1;function Q(ce){return r(ce)&&t(ce.length)&&!!G[e(ce)]}return fa=Q,fa}var da,cd;function mo(){if(cd)return da;cd=1;function e(t){return function(r){return t(r)}}return da=e,da}var Hr={exports:{}};Hr.exports;var ld;function _c(){return ld||(ld=1,(function(e,t){var r=ug(),n=t&&!t.nodeType&&t,o=n&&!0&&e&&!e.nodeType&&e,i=o&&o.exports===n,a=i&&r.process,s=(function(){try{var u=o&&o.require&&o.require("util").types;return u||a&&a.binding&&a.binding("util")}catch{}})();e.exports=s})(Hr,Hr.exports)),Hr.exports}var pa,fd;function wg(){if(fd)return pa;fd=1;var e=Nw(),t=mo(),r=_c(),n=r&&r.isTypedArray,o=n?t(n):e;return pa=o,pa}var ha,dd;function Sg(){if(dd)return ha;dd=1;function e(t,r){if(!(r==="constructor"&&typeof t[r]=="function")&&r!="__proto__")return t[r]}return ha=e,ha}var va,pd;function Eg(){if(pd)return va;pd=1;var e=vc(),t=lo(),r=Object.prototype,n=r.hasOwnProperty;function o(i,a,s){var u=i[a];(!(n.call(i,a)&&t(u,s))||s===void 0&&!(a in i))&&e(i,a,s)}return va=o,va}var ga,hd;function yn(){if(hd)return ga;hd=1;var e=Eg(),t=vc();function r(n,o,i,a){var s=!i;i||(i={});for(var u=-1,c=o.length;++u<c;){var l=o[u],f=a?a(i[l],n[l],l,i,n):void 0;f===void 0&&(f=n[l]),s?t(i,l,f):e(i,l,f)}return i}return ga=r,ga}var ma,vd;function Fw(){if(vd)return ma;vd=1;function e(t,r){for(var n=-1,o=Array(t);++n<t;)o[n]=r(n);return o}return ma=e,ma}var ya,gd;function Tg(){if(gd)return ya;gd=1;var e=9007199254740991,t=/^(?:0|[1-9]\d*)$/;function r(n,o){var i=typeof n;return o=o??e,!!o&&(i=="number"||i!="symbol"&&t.test(n))&&n>-1&&n%1==0&&n<o}return ya=r,ya}var ba,md;function xg(){if(md)return ba;md=1;var e=Fw(),t=fc(),r=Wt(),n=bc(),o=Tg(),i=wg(),a=Object.prototype,s=a.hasOwnProperty;function u(c,l){var f=r(c),p=!f&&t(c),v=!f&&!p&&n(c),y=!f&&!p&&!v&&i(c),m=f||p||v||y,h=m?e(c.length,String):[],b=h.length;for(var _ in c)(l||s.call(c,_))&&!(m&&(_=="length"||v&&(_=="offset"||_=="parent")||y&&(_=="buffer"||_=="byteLength"||_=="byteOffset")||o(_,b)))&&h.push(_);return h}return ba=u,ba}var _a,yd;function Hw(){if(yd)return _a;yd=1;function e(t){var r=[];if(t!=null)for(var n in Object(t))r.push(n);return r}return _a=e,_a}var wa,bd;function kw(){if(bd)return wa;bd=1;var e=Rt(),t=yc(),r=Hw(),n=Object.prototype,o=n.hasOwnProperty;function i(a){if(!e(a))return r(a);var s=t(a),u=[];for(var c in a)c=="constructor"&&(s||!o.call(a,c))||u.push(c);return u}return wa=i,wa}var Sa,_d;function bn(){if(_d)return Sa;_d=1;var e=xg(),t=kw(),r=mn();function n(o){return r(o)?e(o,!0):t(o)}return Sa=n,Sa}var Ea,wd;function Vw(){if(wd)return Ea;wd=1;var e=yn(),t=bn();function r(n){return e(n,t(n))}return Ea=r,Ea}var Ta,Sd;function Bw(){if(Sd)return Ta;Sd=1;var e=pg(),t=vg(),r=gg(),n=dc(),o=yg(),i=fc(),a=Wt(),s=_g(),u=bc(),c=ho(),l=Rt(),f=Gw(),p=wg(),v=Sg(),y=Vw();function m(h,b,_,O,E,M,L){var F=v(h,_),N=v(b,_),j=L.get(N);if(j){e(h,_,j);return}var D=M?M(F,N,_+"",h,b,L):void 0,G=D===void 0;if(G){var Q=a(N),ce=!Q&&u(N),ne=!Q&&!ce&&p(N);D=N,Q||ce||ne?a(F)?D=F:s(F)?D=n(F):ce?(G=!1,D=t(N,!0)):ne?(G=!1,D=r(N,!0)):D=[]:f(N)||i(N)?(D=F,i(F)?D=y(F):(!l(F)||c(F))&&(D=o(N))):G=!1}G&&(L.set(N,D),E(D,N,O,M,L),L.delete(N)),e(h,_,D)}return Ta=m,Ta}var xa,Ed;function $w(){if(Ed)return xa;Ed=1;var e=fg(),t=pg(),r=hg(),n=Bw(),o=Rt(),i=bn(),a=Sg();function s(u,c,l,f,p){u!==c&&r(c,function(v,y){if(p||(p=new e),o(v))n(u,c,y,l,s,f,p);else{var m=f?f(a(u,y),v,y+"",u,c,p):void 0;m===void 0&&(m=v),t(u,y,m)}},i)}return xa=s,xa}var Oa,Td;function wc(){if(Td)return Oa;Td=1;function e(t){return t}return Oa=e,Oa}var Ca,xd;function Ww(){if(xd)return Ca;xd=1;function e(t,r,n){switch(n.length){case 0:return t.call(r);case 1:return t.call(r,n[0]);case 2:return t.call(r,n[0],n[1]);case 3:return t.call(r,n[0],n[1],n[2])}return t.apply(r,n)}return Ca=e,Ca}var Pa,Od;function Uw(){if(Od)return Pa;Od=1;var e=Ww(),t=Math.max;function r(n,o,i){return o=t(o===void 0?n.length-1:o,0),function(){for(var a=arguments,s=-1,u=t(a.length-o,0),c=Array(u);++s<u;)c[s]=a[o+s];s=-1;for(var l=Array(o+1);++s<o;)l[s]=a[s];return l[o]=i(c),e(n,this,l)}}return Pa=r,Pa}var Aa,Cd;function Kw(){if(Cd)return Aa;Cd=1;function e(t){return function(){return t}}return Aa=e,Aa}var Ra,Pd;function Yw(){if(Pd)return Ra;Pd=1;var e=Kw(),t=dg(),r=wc(),n=t?function(o,i){return t(o,"toString",{configurable:!0,enumerable:!1,value:e(i),writable:!0})}:r;return Ra=n,Ra}var Ma,Ad;function zw(){if(Ad)return Ma;Ad=1;var e=800,t=16,r=Date.now;function n(o){var i=0,a=0;return function(){var s=r(),u=t-(s-a);if(a=s,u>0){if(++i>=e)return arguments[0]}else i=0;return o.apply(void 0,arguments)}}return Ma=n,Ma}var Ia,Rd;function Xw(){if(Rd)return Ia;Rd=1;var e=Yw(),t=zw(),r=t(e);return Ia=r,Ia}var La,Md;function Og(){if(Md)return La;Md=1;var e=wc(),t=Uw(),r=Xw();function n(o,i){return r(t(o,i,e),o+"")}return La=n,La}var qa,Id;function Jw(){if(Id)return qa;Id=1;var e=lo(),t=mn(),r=Tg(),n=Rt();function o(i,a,s){if(!n(s))return!1;var u=typeof a;return(u=="number"?t(s)&&r(a,s.length):u=="string"&&a in s)?e(s[a],i):!1}return qa=o,qa}var ja,Ld;function Qw(){if(Ld)return ja;Ld=1;var e=Og(),t=Jw();function r(n){return e(function(o,i){var a=-1,s=i.length,u=s>1?i[s-1]:void 0,c=s>2?i[2]:void 0;for(u=n.length>3&&typeof u=="function"?(s--,u):void 0,c&&t(i[0],i[1],c)&&(u=s<3?void 0:u,s=1),o=Object(o);++a<s;){var l=i[a];l&&n(o,l,a,u)}return o})}return ja=r,ja}var Da,qd;function Zw(){if(qd)return Da;qd=1;var e=$w(),t=Qw(),r=t(function(n,o,i,a){e(n,o,i,a)});return Da=r,Da}var eS=Zw();const Cg=it(eS);var Ga,jd;function Pg(){if(jd)return Ga;jd=1;function e(t,r){for(var n=-1,o=t==null?0:t.length;++n<o&&r(t[n],n,t)!==!1;);return t}return Ga=e,Ga}var Na,Dd;function tS(){if(Dd)return Na;Dd=1;var e=mg(),t=e(Object.keys,Object);return Na=t,Na}var Fa,Gd;function rS(){if(Gd)return Fa;Gd=1;var e=yc(),t=tS(),r=Object.prototype,n=r.hasOwnProperty;function o(i){if(!e(i))return t(i);var a=[];for(var s in Object(i))n.call(i,s)&&s!="constructor"&&a.push(s);return a}return Fa=o,Fa}var Ha,Nd;function yo(){if(Nd)return Ha;Nd=1;var e=xg(),t=rS(),r=mn();function n(o){return r(o)?e(o):t(o)}return Ha=n,Ha}var ka,Fd;function nS(){if(Fd)return ka;Fd=1;var e=hg(),t=yo();function r(n,o){return n&&e(n,o,t)}return ka=r,ka}var Va,Hd;function oS(){if(Hd)return Va;Hd=1;var e=mn();function t(r,n){return function(o,i){if(o==null)return o;if(!e(o))return r(o,i);for(var a=o.length,s=n?a:-1,u=Object(o);(n?s--:++s<a)&&i(u[s],s,u)!==!1;);return o}}return Va=t,Va}var Ba,kd;function iS(){if(kd)return Ba;kd=1;var e=nS(),t=oS(),r=t(e);return Ba=r,Ba}var $a,Vd;function aS(){if(Vd)return $a;Vd=1;var e=wc();function t(r){return typeof r=="function"?r:e}return $a=t,$a}var Wa,Bd;function sS(){if(Bd)return Wa;Bd=1;var e=Pg(),t=iS(),r=aS(),n=Wt();function o(i,a){var s=n(i)?e:t;return s(i,r(a))}return Wa=o,Wa}var uS=sS();const cS=it(uS);function Ag(e,t){return Promise.all(e.map(function(r,n){return r.then(function(o){return{status:"fulfilled",value:o}}).catch(function(o){if(t!=null&&t(n))throw o;return{status:"rejected",reason:o}})}))}function lS(e){if(Array.isArray(e))return e}function fS(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,o,i,a,s=[],u=!0,c=!1;try{if(i=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);u=!0);}catch(l){c=!0,o=l}finally{try{if(!u&&r.return!=null&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}function dS(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Sc(e,t){return lS(e)||fS(e,t)||jv(e,t)||dS()}var pS=typeof navigator<"u"&&navigator.userAgent.indexOf("Trident")!==-1;function Rg(e,t){if(!e.hasOwnProperty(t)||!isNaN(t)&&t<e.length)return!0;if(pS)try{return e[t]&&typeof window<"u"&&e[t].parent===window}catch{return!0}else return!1}var Mn,In,Ou;function hS(e){var t=0,r,n=!1;for(var o in e)if(!Rg(e,o)){for(var i=0;i<window.frames.length&&!n;i++){var a=window.frames[i];if(a===e[o]){n=!0;break}}if(!n&&(t===0&&o!==Mn||t===1&&o!==In))return o;t++,r=o}if(r!==Ou)return r}function vS(e){Mn=In=void 0;for(var t in e)Rg(e,t)||(Mn?In||(In=t):Mn=t,Ou=t);return Ou}function Ec(e){var t=e.indexOf(">")+1,r=e.lastIndexOf("<");return e.substring(t,r)}function Cu(e){if(Ie(e)==="object")return"/";try{var t=new URL(e,location.href),r=t.origin,n=t.pathname,o=n.split("/");return o.pop(),"".concat(r).concat(o.join("/"),"/")}catch(i){return console.warn(i),""}}function gS(){var e=document.createElement("script");return"noModule"in e}var mS=window.requestIdleCallback||function(t){var r=Date.now();return setTimeout(function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-r))}})},1)};function yS(e,t){if(!t||!e.headers)return e.text();var r=e.headers.get("Content-Type");if(!r)return e.text();var n="utf-8",o=r.split(";");if(o.length===2){var i=o[1].split("="),a=Sc(i,2),s=a[1],u=s&&s.trim();u&&(n=u)}return n.toUpperCase()==="UTF-8"?e.text():e.blob().then(function(c){return new Promise(function(l,f){var p=new window.FileReader;p.onload=function(){l(p.result)},p.onerror=f,p.readAsText(c,n)})})}var Ua={};function bS(e,t){var r=e;if(!Ua[r]){var n="(function(){".concat(t,"})");Ua[r]=(0,eval)(n)}var o=Ua[r];o.call(window)}function $d(e){var t=new DOMParser,r='<script src="'.concat(e,'"><\/script>'),n=t.parseFromString(r,"text/html");return n.scripts[0].src}var _S=/(<script[\s\S]*?>)[\s\S]*?<\/script>/gi,wS=/<(script)\s+((?!type=('|")text\/ng\x2Dtemplate\3)[\s\S])*?>[\s\S]*?<\/\1>/i,Wd=/.*\ssrc=('|")?([^>'"\s]+)/,SS=/.*\stype=('|")?([^>'"\s]+)/,ES=/.*\sentry\s*.*/,TS=/.*\sasync\s*.*/,xS=/.*\scrossorigin=('|")?use-credentials\1/,OS=/.*\snomodule\s*.*/,CS=/.*\stype=('|")?module('|")?\s*.*/,PS=/<(link)\s+[\s\S]*?>/ig,AS=/\srel=('|")?(preload|prefetch)\1/,Ud=/.*\shref=('|")?([^>'"\s]+)/,RS=/.*\sas=('|")?font\1.*/,MS=/<style[^>]*>[\s\S]*?<\/style>/gi,IS=/\s+rel=('|")?stylesheet\1.*/,LS=/.*\shref=('|")?([^>'"\s]+)/,qS=/<!--([\s\S]*?)-->/g,jS=/<link(\s+|\s+[\s\S]+\s+)ignore(\s*|\s+[\s\S]*|=[\s\S]*)>/i,DS=/<style(\s+|\s+[\s\S]+\s+)ignore(\s*|\s+[\s\S]*|=[\s\S]*)>/i,GS=/<script(\s+|\s+[\s\S]+\s+)ignore(\s*|\s+[\s\S]*|=[\s\S]*)>/i;function Kd(e){return e.startsWith("http://")||e.startsWith("https://")}function Yd(e,t){return new URL(e,t).toString()}function NS(e){var t=["text/javascript","module","application/javascript","text/ecmascript","application/ecmascript"];return!e||t.indexOf(e)!==-1}var $n=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return"<!-- ".concat(r?"prefetch/preload":""," link ").concat(t," replaced by import-html-entry -->")},Mg=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return"<!-- ".concat(n?"cors":""," ").concat(r?"async":""," script ").concat(t," replaced by import-html-entry -->")},FS="<!-- inline scripts replaced by import-html-entry -->",On=function(t){return"<!-- ignore asset ".concat(t||"file"," replaced by import-html-entry -->")},zd=function(t,r){return"<!-- ".concat(r?"nomodule":"module"," script ").concat(t," ignored by import-html-entry -->")};function HS(e,t,r){var n=[],o=[],i=null,a=gS(),s=e.replace(qS,"").replace(PS,function(c){var l=!!c.match(IS);if(l){var f=c.match(LS),p=c.match(jS);if(f){var v=f&&f[2],y=v;return v&&!Kd(v)&&(y=Yd(v,t)),p?On(y):(y=$d(y),o.push(y),$n(y))}}var m=c.match(AS)&&c.match(Ud)&&!c.match(RS);if(m){var h=c.match(Ud),b=Sc(h,3),_=b[2];return $n(_,!0)}return c}).replace(MS,function(c){return DS.test(c)?On("style file"):c}).replace(_S,function(c,l){var f=l.match(GS),p=a&&!!l.match(OS)||!a&&!!l.match(CS),v=l.match(SS),y=v&&v[2];if(!NS(y))return c;if(wS.test(c)&&l.match(Wd)){var m=l.match(ES),h=l.match(Wd),b=h&&h[2];if(i&&m)throw new SyntaxError("You should not set multiply entry script!");if(b&&(Kd(b)||(b=Yd(b,t)),b=$d(b)),i=i||m&&b,f)return On(b||"js file");if(p)return zd(b||"js file",a);if(b){var _=!!l.match(TS),O=!!l.match(xS);return n.push(_||O?{async:_,src:b,crossOrigin:O}:b),Mg(b,_,O)}return c}else{if(f)return On("js file");if(p)return zd("js file",a);var E=Ec(c),M=E.split(/[\r\n]+/).every(function(L){return!L.trim()||L.trim().startsWith("//")});return M||n.push(c),FS}});n=n.filter(function(c){return!!c});var u={template:s,scripts:n,styles:o,entry:i||n[n.length-1]};return typeof r=="function"&&(u=r(u)),u}function Xd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function Ig(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xd(Object(r),!0).forEach(function(n){Et(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}var Jd={},Qd={},Zd={};if(!window.fetch)throw new Error('[import-html-entry] Here is no "fetch" on the window env, you need to polyfill it');var or=window.fetch.bind(window);function Pu(e){return e}function Lg(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n=r.fetch,o=n===void 0?or:n,i=e;return Tc(t,o).then(function(a){return i=a.reduce(function(s,u){var c=u.src,l=u.value;return s=s.replace($n(c),bo(c)?"".concat(c):"<style>/* ".concat(c," */").concat(l,"</style>")),s},i),i})}var bo=function(t){return t.startsWith("<")};function kS(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n=r.proxy,o=r.strictGlobal,i=r.scopedGlobalVariables,a=i===void 0?[]:i,s=bo(e)?"":"//# sourceURL=".concat(e,`
`),u=a.length?"const {".concat(a.join(","),"}=this;"):"",c=(0,eval)("window");return c.proxy=n,o?u?";(function(){with(this){".concat(u).concat(t,`
`).concat(s,"}}).bind(window.proxy)();"):";(function(window, self, globalThis){with(window){;".concat(t,`
`).concat(s,"}}).bind(window.proxy)(window.proxy, window.proxy, window.proxy);"):";(function(window, self, globalThis){;".concat(t,`
`).concat(s,"}).bind(window.proxy)(window.proxy, window.proxy, window.proxy);")}function Tc(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:or;return Ag(e.map((function(){var r=ie(V.mark(function n(o){return V.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(!bo(o)){a.next=4;break}return a.abrupt("return",Ec(o));case 4:return a.abrupt("return",Jd[o]||(Jd[o]=t(o).then(function(s){if(s.status>=400)throw new Error("".concat(o," load failed with status ").concat(s.status));return s.text()}).catch(function(s){try{s.message.indexOf(o)===-1&&(s.message="".concat(o," ").concat(s.message))}catch{}throw s})));case 5:case"end":return a.stop()}},n)}));return function(n){return r.apply(this,arguments)}})())).then(function(r){return r.map(function(n,o){return n.status==="fulfilled"&&(n.value={src:e[o],value:n.value}),n}).filter(function(n){return n.status==="rejected"&&Promise.reject(n.reason),n.status==="fulfilled"}).map(function(n){return n.value})})}function xc(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:or,r=arguments.length>2?arguments[2]:void 0,n=function(a,s){return Qd[a]||(Qd[a]=t(a,s).then(function(u){if(u.status>=400)throw new Error("".concat(a," load failed with status ").concat(u.status));return u.text()}).catch(function(u){try{u.message.indexOf(a)===-1&&(u.message="".concat(a," ").concat(u.message))}catch{}throw u}))},o=function(a){return e[a]===r};return Ag(e.map((function(){var i=ie(V.mark(function a(s){var u,c,l,f;return V.wrap(function(v){for(;;)switch(v.prev=v.next){case 0:if(typeof s!="string"){v.next=8;break}if(!bo(s)){v.next=5;break}return v.abrupt("return",Ec(s));case 5:return v.abrupt("return",n(s));case 6:v.next=13;break;case 8:if(u=s.src,c=s.async,l=s.crossOrigin,f=l?{credentials:"include"}:{},!c){v.next=12;break}return v.abrupt("return",{src:u,async:!0,content:new Promise(function(y,m){return mS(function(){return n(u,f).then(y,m)})})});case 12:return v.abrupt("return",n(u,f));case 13:case"end":return v.stop()}},a)}));return function(a){return i.apply(this,arguments)}})()),o).then(function(i){return i.map(function(a,s){return a.status==="fulfilled"&&(a.value={src:e[s],value:a.value}),a}).filter(function(a){return a.status==="rejected"&&Promise.reject(a.reason),a.status==="fulfilled"}).map(function(a){return a.value})})}function ep(e,t){setTimeout(function(){throw console.error(t),e})}function Wn(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:window,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},o=n.fetch,i=o===void 0?or:o,a=n.strictGlobal,s=a===void 0?!1:a,u=n.success,c=n.error,l=c===void 0?function(){}:c,f=n.beforeExec,p=f===void 0?function(){}:f,v=n.afterExec,y=v===void 0?function(){}:v,m=n.scopedGlobalVariables,h=m===void 0?[]:m;return xc(t,i,e).then(function(b){var _=function(L,F){var N=p(F,L)||F,j=kS(L,N,{proxy:r,strictGlobal:s,scopedGlobalVariables:h});bS(L,j),y(F,L)};function O(M,L,F){if(M===e){vS(s?r:window);try{_(M,L);var N=r[hS(s?r:window)]||{};F(N)}catch(j){throw console.error("[import-html-entry]: error occurs while executing entry script ".concat(M)),j}}else if(typeof L=="string")try{M!=null&&M.src?_(M.src,L):_(M,L)}catch(j){ep(j,"[import-html-entry]: error occurs while executing normal script ".concat(M))}else L.async&&L?.content.then(function(j){return _(L.src,j)}).catch(function(j){ep(j,"[import-html-entry]: error occurs while executing async script ".concat(L.src))})}function E(M,L){if(M<b.length){var F=b[M],N=F.src,j=F.value;O(N,j,L),!e&&M===b.length-1?L():E(M+1,L)}}return new Promise(function(M){return E(0,u||M)})}).catch(function(b){throw l(),b})}function VS(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=or,n=!1,o=Cu,i=Pu,a=t.postProcessTemplate;return typeof t=="function"?r=t:(t.fetch&&(typeof t.fetch=="function"?r=t.fetch:(r=t.fetch.fn||or,n=!!t.fetch.autoDecodeResponse)),o=t.getPublicPath||t.getDomain||Cu,i=t.getTemplate||Pu),Zd[e]||(Zd[e]=r(e).then(function(s){return yS(s,n)}).then(function(s){var u=o(e),c=HS(i(s),u,a),l=c.template,f=c.scripts,p=c.entry,v=c.styles;return Lg(l,v,{fetch:r}).then(function(y){return{template:y,assetPublicPath:u,getExternalScripts:function(){return xc(f,r)},getExternalStyleSheets:function(){return Tc(v,r)},execScripts:function(h,b){var _=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return f.length?Wn(p,f,h,Ig({fetch:r,strictGlobal:b},_)):Promise.resolve()}}})}))}function qg(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=t.fetch,n=r===void 0?or:r,o=t.getTemplate,i=o===void 0?Pu:o,a=t.postProcessTemplate,s=t.getPublicPath||t.getDomain||Cu;if(!e)throw new SyntaxError("entry should not be empty!");if(typeof e=="string")return VS(e,{fetch:n,getPublicPath:s,getTemplate:i,postProcessTemplate:a});if(Array.isArray(e.scripts)||Array.isArray(e.styles)){var u=e.scripts,c=u===void 0?[]:u,l=e.styles,f=l===void 0?[]:l,p=e.html,v=p===void 0?"":p,y=function(b){return f.reduceRight(function(_,O){return"".concat($n(O)).concat(_)},b)},m=function(b){return c.reduce(function(_,O){return"".concat(_).concat(Mg(O))},b)};return Lg(i(m(y(v))),f,{fetch:n}).then(function(h){return{template:h,assetPublicPath:s(e),getExternalScripts:function(){return xc(c,n)},getExternalStyleSheets:function(){return Tc(f,n)},execScripts:function(_,O){var E=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return c.length?Wn(c[c.length-1],c,_,Ig({fetch:n,strictGlobal:O},E)):Promise.resolve()}}})}else throw new SyntaxError("entry scripts or styles should be array!")}function BS(e){return{beforeLoad:function(){return ie(V.mark(function r(){return V.wrap(function(o){for(;;)switch(o.prev=o.next){case 0:e.__POWERED_BY_QIANKUN__=!0;case 1:case"end":return o.stop()}},r)}))()},beforeMount:function(){return ie(V.mark(function r(){return V.wrap(function(o){for(;;)switch(o.prev=o.next){case 0:e.__POWERED_BY_QIANKUN__=!0;case 1:case"end":return o.stop()}},r)}))()},beforeUnmount:function(){return ie(V.mark(function r(){return V.wrap(function(o){for(;;)switch(o.prev=o.next){case 0:delete e.__POWERED_BY_QIANKUN__;case 1:case"end":return o.stop()}},r)}))()}}}var tp=window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__;function $S(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"/",r=!1;return{beforeLoad:function(){return ie(V.mark(function o(){return V.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:e.__INJECTED_PUBLIC_PATH_BY_QIANKUN__=t;case 1:case"end":return a.stop()}},o)}))()},beforeMount:function(){return ie(V.mark(function o(){return V.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:r&&(e.__INJECTED_PUBLIC_PATH_BY_QIANKUN__=t);case 1:case"end":return a.stop()}},o)}))()},beforeUnmount:function(){return ie(V.mark(function o(){return V.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:tp===void 0?delete e.__INJECTED_PUBLIC_PATH_BY_QIANKUN__:e.__INJECTED_PUBLIC_PATH_BY_QIANKUN__=tp,r=!0;case 2:case"end":return a.stop()}},o)}))()}}}function WS(e,t){return Cg({},BS(e),$S(e,t),function(r,n){return cg(r??[],n??[])})}function US(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Dv(n.key),n)}}function Ar(e,t,r){return t&&US(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Rr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function on(e,t){return on=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,n){return r.__proto__=n,r},on(e,t)}function KS(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&on(e,t)}function an(e){return an=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},an(e)}function Oc(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Oc=function(){return!!e})()}function YS(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function zS(e,t){if(t&&(Ie(t)=="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return YS(e)}function XS(e){var t=Oc();return function(){var r,n=an(e);if(t){var o=an(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return zS(this,r)}}function JS(e){try{return Function.toString.call(e).indexOf("[native code]")!==-1}catch{return typeof e=="function"}}function QS(e,t,r){if(Oc())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,t);var o=new(e.bind.apply(e,n));return r&&on(o,r.prototype),o}function Au(e){var t=typeof Map=="function"?new Map:void 0;return Au=function(n){if(n===null||!JS(n))return n;if(typeof n!="function")throw new TypeError("Super expression must either be null or a function");if(t!==void 0){if(t.has(n))return t.get(n);t.set(n,o)}function o(){return QS(n,arguments,an(this).constructor)}return o.prototype=Object.create(n.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),on(o,n)},Au(e)}var sn=(function(e){KS(r,e);var t=XS(r);function r(n){return Rr(this,r),t.call(this,"[qiankun]: ".concat(n))}return Ar(r)})(Au(Error)),Ka,rp;function ZS(){if(rp)return Ka;rp=1;var e=yn(),t=yo();function r(n,o){return n&&e(o,t(o),n)}return Ka=r,Ka}var Ya,np;function eE(){if(np)return Ya;np=1;var e=yn(),t=bn();function r(n,o){return n&&e(o,t(o),n)}return Ya=r,Ya}var za,op;function tE(){if(op)return za;op=1;function e(t,r){for(var n=-1,o=t==null?0:t.length,i=0,a=[];++n<o;){var s=t[n];r(s,n,t)&&(a[i++]=s)}return a}return za=e,za}var Xa,ip;function jg(){if(ip)return Xa;ip=1;function e(){return[]}return Xa=e,Xa}var Ja,ap;function Cc(){if(ap)return Ja;ap=1;var e=tE(),t=jg(),r=Object.prototype,n=r.propertyIsEnumerable,o=Object.getOwnPropertySymbols,i=o?function(a){return a==null?[]:(a=Object(a),e(o(a),function(s){return n.call(a,s)}))}:t;return Ja=i,Ja}var Qa,sp;function rE(){if(sp)return Qa;sp=1;var e=yn(),t=Cc();function r(n,o){return e(n,t(n),o)}return Qa=r,Qa}var Za,up;function Dg(){if(up)return Za;up=1;var e=co(),t=mc(),r=Cc(),n=jg(),o=Object.getOwnPropertySymbols,i=o?function(a){for(var s=[];a;)e(s,r(a)),a=t(a);return s}:n;return Za=i,Za}var es,cp;function nE(){if(cp)return es;cp=1;var e=yn(),t=Dg();function r(n,o){return e(n,t(n),o)}return es=r,es}var ts,lp;function Gg(){if(lp)return ts;lp=1;var e=co(),t=Wt();function r(n,o,i){var a=o(n);return t(n)?a:e(a,i(n))}return ts=r,ts}var rs,fp;function oE(){if(fp)return rs;fp=1;var e=Gg(),t=Cc(),r=yo();function n(o){return e(o,r,t)}return rs=n,rs}var ns,dp;function iE(){if(dp)return ns;dp=1;var e=Gg(),t=Dg(),r=bn();function n(o){return e(o,r,t)}return ns=n,ns}var os,pp;function aE(){if(pp)return os;pp=1;var e=sr(),t=gt(),r=e(t,"DataView");return os=r,os}var is,hp;function sE(){if(hp)return is;hp=1;var e=sr(),t=gt(),r=e(t,"Promise");return is=r,is}var as,vp;function uE(){if(vp)return as;vp=1;var e=sr(),t=gt(),r=e(t,"Set");return as=r,as}var ss,gp;function cE(){if(gp)return ss;gp=1;var e=sr(),t=gt(),r=e(t,"WeakMap");return ss=r,ss}var us,mp;function Pc(){if(mp)return us;mp=1;var e=aE(),t=pc(),r=sE(),n=uE(),o=cE(),i=Pr(),a=lg(),s="[object Map]",u="[object Object]",c="[object Promise]",l="[object Set]",f="[object WeakMap]",p="[object DataView]",v=a(e),y=a(t),m=a(r),h=a(n),b=a(o),_=i;return(e&&_(new e(new ArrayBuffer(1)))!=p||t&&_(new t)!=s||r&&_(r.resolve())!=c||n&&_(new n)!=l||o&&_(new o)!=f)&&(_=function(O){var E=i(O),M=E==u?O.constructor:void 0,L=M?a(M):"";if(L)switch(L){case v:return p;case y:return s;case m:return c;case h:return l;case b:return f}return E}),us=_,us}var cs,yp;function lE(){if(yp)return cs;yp=1;var e=Object.prototype,t=e.hasOwnProperty;function r(n){var o=n.length,i=new n.constructor(o);return o&&typeof n[0]=="string"&&t.call(n,"index")&&(i.index=n.index,i.input=n.input),i}return cs=r,cs}var ls,bp;function fE(){if(bp)return ls;bp=1;var e=gc();function t(r,n){var o=n?e(r.buffer):r.buffer;return new r.constructor(o,r.byteOffset,r.byteLength)}return ls=t,ls}var fs,_p;function dE(){if(_p)return fs;_p=1;var e=/\w*$/;function t(r){var n=new r.constructor(r.source,e.exec(r));return n.lastIndex=r.lastIndex,n}return fs=t,fs}var ds,wp;function pE(){if(wp)return ds;wp=1;var e=gn(),t=e?e.prototype:void 0,r=t?t.valueOf:void 0;function n(o){return r?Object(r.call(o)):{}}return ds=n,ds}var ps,Sp;function hE(){if(Sp)return ps;Sp=1;var e=gc(),t=fE(),r=dE(),n=pE(),o=gg(),i="[object Boolean]",a="[object Date]",s="[object Map]",u="[object Number]",c="[object RegExp]",l="[object Set]",f="[object String]",p="[object Symbol]",v="[object ArrayBuffer]",y="[object DataView]",m="[object Float32Array]",h="[object Float64Array]",b="[object Int8Array]",_="[object Int16Array]",O="[object Int32Array]",E="[object Uint8Array]",M="[object Uint8ClampedArray]",L="[object Uint16Array]",F="[object Uint32Array]";function N(j,D,G){var Q=j.constructor;switch(D){case v:return e(j);case i:case a:return new Q(+j);case y:return t(j,G);case m:case h:case b:case _:case O:case E:case M:case L:case F:return o(j,G);case s:return new Q;case u:case f:return new Q(j);case c:return r(j);case l:return new Q;case p:return n(j)}}return ps=N,ps}var hs,Ep;function vE(){if(Ep)return hs;Ep=1;var e=Pc(),t=$t(),r="[object Map]";function n(o){return t(o)&&e(o)==r}return hs=n,hs}var vs,Tp;function gE(){if(Tp)return vs;Tp=1;var e=vE(),t=mo(),r=_c(),n=r&&r.isMap,o=n?t(n):e;return vs=o,vs}var gs,xp;function mE(){if(xp)return gs;xp=1;var e=Pc(),t=$t(),r="[object Set]";function n(o){return t(o)&&e(o)==r}return gs=n,gs}var ms,Op;function yE(){if(Op)return ms;Op=1;var e=mE(),t=mo(),r=_c(),n=r&&r.isSet,o=n?t(n):e;return ms=o,ms}var ys,Cp;function bE(){if(Cp)return ys;Cp=1;var e=fg(),t=Pg(),r=Eg(),n=ZS(),o=eE(),i=vg(),a=dc(),s=rE(),u=nE(),c=oE(),l=iE(),f=Pc(),p=lE(),v=hE(),y=yg(),m=Wt(),h=bc(),b=gE(),_=Rt(),O=yE(),E=yo(),M=bn(),L=1,F=2,N=4,j="[object Arguments]",D="[object Array]",G="[object Boolean]",Q="[object Date]",ce="[object Error]",ne="[object Function]",pe="[object GeneratorFunction]",te="[object Map]",W="[object Number]",$="[object Object]",oe="[object RegExp]",se="[object Set]",ye="[object String]",he="[object Symbol]",Fe="[object WeakMap]",Qe="[object ArrayBuffer]",Ue="[object DataView]",qe="[object Float32Array]",Ke="[object Float64Array]",at="[object Int8Array]",Ze="[object Int16Array]",st="[object Int32Array]",Mr="[object Uint8Array]",d="[object Uint8ClampedArray]",g="[object Uint16Array]",S="[object Uint32Array]",T={};T[j]=T[D]=T[Qe]=T[Ue]=T[G]=T[Q]=T[qe]=T[Ke]=T[at]=T[Ze]=T[st]=T[te]=T[W]=T[$]=T[oe]=T[se]=T[ye]=T[he]=T[Mr]=T[d]=T[g]=T[S]=!0,T[ce]=T[ne]=T[Fe]=!1;function x(w,q,R,I,C,H){var P,B=q&L,k=q&F,Y=q&N;if(R&&(P=C?R(w,I,C,H):R(w)),P!==void 0)return P;if(!_(w))return w;var Z=m(w);if(Z){if(P=p(w),!B)return a(w,P)}else{var U=f(w),_e=U==ne||U==pe;if(h(w))return i(w,B);if(U==$||U==j||_e&&!C){if(P=k||_e?{}:y(w),!B)return k?u(w,o(P,w)):s(w,n(P,w))}else{if(!T[U])return C?w:{};P=v(w,U,B)}}H||(H=new e);var we=H.get(w);if(we)return we;H.set(w,P),O(w)?w.forEach(function(A){P.add(x(A,q,R,A,w,H))}):b(w)&&w.forEach(function(A,Ce){P.set(Ce,x(A,q,R,Ce,w,H))});var z=Y?k?l:c:k?M:E,re=Z?void 0:z(w);return t(re||w,function(A,Ce){re&&(Ce=A,A=w[Ce]),r(P,Ce,x(A,q,R,Ce,w,H))}),P}return ys=x,ys}var bs,Pp;function _E(){if(Pp)return bs;Pp=1;var e=bE(),t=1,r=4;function n(o){return e(o,t|r)}return bs=n,bs}var wE=_E();const Yr=it(wE);var cr={},yr={};function SE(e,t){Object.keys(yr).forEach(function(r){yr[r]instanceof Function&&yr[r](Yr(e),Yr(t))})}function EE(e,t){return{onGlobalStateChange:function(n,o){if(!(n instanceof Function)){console.error("[qiankun] callback must be function!");return}if(yr[e]&&console.warn("[qiankun] '".concat(e,"' global listener already exists before this, new listener will overwrite it.")),yr[e]=n,o){var i=Yr(cr);n(i,i)}},setGlobalState:function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(n===cr)return console.warn("[qiankun] state has not changed！"),!1;var o=[],i=Yr(cr);return cr=Yr(Object.keys(n).reduce(function(a,s){return a.hasOwnProperty(s)?(o.push(s),Object.assign(a,Et({},s,n[s]))):(console.warn("[qiankun] '".concat(s,"' not declared when init state！")),a)},cr)),o.length===0?(console.warn("[qiankun] state has not changed！"),!1):(SE(cr,i),!0)},offGlobalStateChange:function(){return delete yr[e],!0}}}var ot;(function(e){e.Proxy="Proxy",e.Snapshot="Snapshot",e.LegacyProxy="LegacyProxy"})(ot||(ot={}));var TE=ho();const Ft=it(TE);var _s,Ap;function xE(){if(Ap)return _s;Ap=1;var e=/\s/;function t(r){for(var n=r.length;n--&&e.test(r.charAt(n)););return n}return _s=t,_s}var ws,Rp;function OE(){if(Rp)return ws;Rp=1;var e=xE(),t=/^\s+/;function r(n){return n&&n.slice(0,e(n)+1).replace(t,"")}return ws=r,ws}var Ss,Mp;function Ng(){if(Mp)return Ss;Mp=1;var e=Pr(),t=$t(),r="[object Symbol]";function n(o){return typeof o=="symbol"||t(o)&&e(o)==r}return Ss=n,Ss}var Es,Ip;function CE(){if(Ip)return Es;Ip=1;var e=OE(),t=Rt(),r=Ng(),n=NaN,o=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,a=/^0o[0-7]+$/i,s=parseInt;function u(c){if(typeof c=="number")return c;if(r(c))return n;if(t(c)){var l=typeof c.valueOf=="function"?c.valueOf():c;c=t(l)?l+"":l}if(typeof c!="string")return c===0?c:+c;c=e(c);var f=i.test(c);return f||a.test(c)?s(c.slice(2),f?2:8):o.test(c)?n:+c}return Es=u,Es}var Ts,Lp;function PE(){if(Lp)return Ts;Lp=1;var e=CE(),t=1/0,r=17976931348623157e292;function n(o){if(!o)return o===0?o:0;if(o=e(o),o===t||o===-t){var i=o<0?-1:1;return i*r}return o===o?o:0}return Ts=n,Ts}var xs,qp;function AE(){if(qp)return xs;qp=1;var e=PE();function t(r){var n=e(r),o=n%1;return n===n?o?n-o:n:0}return xs=t,xs}var Os,jp;function RE(){if(jp)return Os;jp=1;var e=AE(),t="Expected a function";function r(n,o){var i;if(typeof o!="function")throw new TypeError(t);return n=e(n),function(){return--n>0&&(i=o.apply(this,arguments)),n<=1&&(o=void 0),i}}return Os=r,Os}var Cs,Dp;function ME(){if(Dp)return Cs;Dp=1;var e=RE();function t(r){return e(2,r)}return Cs=t,Cs}var IE=ME();const LE=it(IE);var Ps,Gp;function qE(){if(Gp)return Ps;Gp=1;function e(t,r,n,o){var i=-1,a=t==null?0:t.length;for(o&&a&&(n=t[++i]);++i<a;)n=r(n,t[i],i,t);return n}return Ps=e,Ps}var As,Np;function jE(){if(Np)return As;Np=1;function e(t){return function(r){return t?.[r]}}return As=e,As}var Rs,Fp;function DE(){if(Fp)return Rs;Fp=1;var e=jE(),t={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},r=e(t);return Rs=r,Rs}var Ms,Hp;function Fg(){if(Hp)return Ms;Hp=1;function e(t,r){for(var n=-1,o=t==null?0:t.length,i=Array(o);++n<o;)i[n]=r(t[n],n,t);return i}return Ms=e,Ms}var Is,kp;function GE(){if(kp)return Is;kp=1;var e=gn(),t=Fg(),r=Wt(),n=Ng(),o=e?e.prototype:void 0,i=o?o.toString:void 0;function a(s){if(typeof s=="string")return s;if(r(s))return t(s,a)+"";if(n(s))return i?i.call(s):"";var u=s+"";return u=="0"&&1/s==-1/0?"-0":u}return Is=a,Is}var Ls,Vp;function Hg(){if(Vp)return Ls;Vp=1;var e=GE();function t(r){return r==null?"":e(r)}return Ls=t,Ls}var qs,Bp;function NE(){if(Bp)return qs;Bp=1;var e=DE(),t=Hg(),r=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,n="\\u0300-\\u036f",o="\\ufe20-\\ufe2f",i="\\u20d0-\\u20ff",a=n+o+i,s="["+a+"]",u=RegExp(s,"g");function c(l){return l=t(l),l&&l.replace(r,e).replace(u,"")}return qs=c,qs}var js,$p;function FE(){if($p)return js;$p=1;var e=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;function t(r){return r.match(e)||[]}return js=t,js}var Ds,Wp;function HE(){if(Wp)return Ds;Wp=1;var e=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;function t(r){return e.test(r)}return Ds=t,Ds}var Gs,Up;function kE(){if(Up)return Gs;Up=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",o=t+r+n,i="\\u2700-\\u27bf",a="a-z\\xdf-\\xf6\\xf8-\\xff",s="\\xac\\xb1\\xd7\\xf7",u="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",c="\\u2000-\\u206f",l=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",f="A-Z\\xc0-\\xd6\\xd8-\\xde",p="\\ufe0e\\ufe0f",v=s+u+c+l,y="['’]",m="["+v+"]",h="["+o+"]",b="\\d+",_="["+i+"]",O="["+a+"]",E="[^"+e+v+b+i+a+f+"]",M="\\ud83c[\\udffb-\\udfff]",L="(?:"+h+"|"+M+")",F="[^"+e+"]",N="(?:\\ud83c[\\udde6-\\uddff]){2}",j="[\\ud800-\\udbff][\\udc00-\\udfff]",D="["+f+"]",G="\\u200d",Q="(?:"+O+"|"+E+")",ce="(?:"+D+"|"+E+")",ne="(?:"+y+"(?:d|ll|m|re|s|t|ve))?",pe="(?:"+y+"(?:D|LL|M|RE|S|T|VE))?",te=L+"?",W="["+p+"]?",$="(?:"+G+"(?:"+[F,N,j].join("|")+")"+W+te+")*",oe="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",se="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",ye=W+te+$,he="(?:"+[_,N,j].join("|")+")"+ye,Fe=RegExp([D+"?"+O+"+"+ne+"(?="+[m,D,"$"].join("|")+")",ce+"+"+pe+"(?="+[m,D+Q,"$"].join("|")+")",D+"?"+Q+"+"+ne,D+"+"+pe,se,oe,b,he].join("|"),"g");function Qe(Ue){return Ue.match(Fe)||[]}return Gs=Qe,Gs}var Ns,Kp;function VE(){if(Kp)return Ns;Kp=1;var e=FE(),t=HE(),r=Hg(),n=kE();function o(i,a,s){return i=r(i),a=s?void 0:a,a===void 0?t(i)?n(i):e(i):i.match(a)||[]}return Ns=o,Ns}var Fs,Yp;function BE(){if(Yp)return Fs;Yp=1;var e=qE(),t=NE(),r=VE(),n="['’]",o=RegExp(n,"g");function i(a){return function(s){return e(r(t(s).replace(o,"")),a,"")}}return Fs=i,Fs}var Hs,zp;function $E(){if(zp)return Hs;zp=1;var e=BE(),t=e(function(r,n,o){return r+(o?"_":"")+n.toLowerCase()});return Hs=t,Hs}var WE=$E();const UE=it(WE);var ks,Xp;function KE(){if(Xp)return ks;Xp=1;var e=hc(),t="Expected a function";function r(n,o){if(typeof n!="function"||o!=null&&typeof o!="function")throw new TypeError(t);var i=function(){var a=arguments,s=o?o.apply(this,a):a[0],u=i.cache;if(u.has(s))return u.get(s);var c=n.apply(this,a);return i.cache=u.set(s,c)||u,c};return i.cache=new(r.Cache||e),i}return r.Cache=e,ks=r,ks}var YE=KE();const zE=it(YE);var XE="2.10.16";function fr(e){return Array.isArray(e)?e:[e]}var JE=typeof window.__zone_symbol__setTimeout=="function"?window.__zone_symbol__setTimeout:function(e){return Promise.resolve().then(e)},Vs=!1;function QE(e){Vs||(Vs=!0,JE(function(){e(),Vs=!1}))}var Bs=new WeakMap;function ZE(e){var t=e.prototype&&e.prototype.constructor===e&&Object.getOwnPropertyNames(e.prototype).length>1;if(t)return!0;if(Bs.has(e))return Bs.get(e);var r=t;if(!r){var n=e.toString(),o=/^function\b\s[A-Z].*/,i=/^class\b/;r=o.test(n)||i.test(n)}return Bs.set(e,r),r}var Jp=new WeakMap;function kg(e){if(Jp.has(e))return!0;var t=typeof e=="function"&&e instanceof Function;return t&&Jp.set(e,t),t}var Qp=new WeakMap;function e0(e,t){if(!e||!t)return!1;var r=Qp.get(e)||{};if(r[t])return r[t];var n=Object.getOwnPropertyDescriptor(e,t),o=!!(n&&n.configurable===!1&&(n.writable===!1||n.get&&!n.set));return r[t]=o,Qp.set(e,r),o}var $s=new WeakMap;function Vg(e){if($s.has(e))return $s.get(e);var t=e.name.indexOf("bound ")===0&&!e.hasOwnProperty("prototype");return $s.set(e,t),t}var t0=zE(function(){try{return new Function("const { a } = { a: 1 }")(),!0}catch{return!1}}),kr="qiankun-head";function r0(e,t){return function(r){var n;return r.indexOf("<head>")!==-1?n=r.replace("<head>","<".concat(kr,">")).replace("</head>","</".concat(kr,">")):n="<".concat(kr,"></").concat(kr,">").concat(r),'<div id="'.concat(Bg(e),'" data-name="').concat(e,'" data-version="').concat(XE,'" data-sandbox-cfg=').concat(JSON.stringify(t),">").concat(n,"</div>")}}function Bg(e){return"__qiankun_microapp_wrapper_for_".concat(UE(e),"__")}var ke=new Function("return this")(),Zp=new Function("return document")(),n0=LE(function(){return ke.hasOwnProperty("__app_instance_name_map__")||Object.defineProperty(ke,"__app_instance_name_map__",{enumerable:!1,configurable:!0,writable:!0,value:{}}),ke.__app_instance_name_map__}),o0=function(t){var r=n0();return t in r?(r[t]++,"".concat(t,"_").concat(r[t])):(ke.__app_instance_name_map__[t]=0,t)};function Ws(e){var t=e??{},r=t.bootstrap,n=t.mount,o=t.unmount;return Ft(r)&&Ft(n)&&Ft(o)}var $g=Ar(function e(){var t=this;Rr(this,e),this.promise=void 0,this.resolve=void 0,this.reject=void 0,this.promise=new Promise(function(r,n){t.resolve=r,t.reject=n})});function i0(e){return Ie(e)!=="object"||e.strictStyleIsolation?!1:!!e.experimentalStyleIsolation}function a0(e){return typeof e=="string"?document.querySelector(e):e}var Ac=null;function Ru(){return Ac}function s0(e){Ac=e}function u0(){Ac=null}var eh=new WeakMap;function Wg(e,t){if(kg(t)&&!Vg(t)&&!ZE(t)){var r=eh.get(t);if(r)return r;var n=Function.prototype.bind.call(t,e);if(Object.getOwnPropertyNames(t).forEach(function(s){n.hasOwnProperty(s)||Object.defineProperty(n,s,Object.getOwnPropertyDescriptor(t,s))}),t.hasOwnProperty("prototype")&&!n.hasOwnProperty("prototype")&&Object.defineProperty(n,"prototype",{value:t.prototype,enumerable:!1,writable:!0}),typeof t.toString=="function"){var o=t.hasOwnProperty("toString")&&!n.hasOwnProperty("toString"),i=n.toString===Function.prototype.toString;if(o||i){var a=Object.getOwnPropertyDescriptor(o?t:Function.prototype,"toString");Object.defineProperty(n,"toString",Object.assign({},a,a?.get?null:{value:function(){return t.toString()}}))}}return eh.set(t,n),n}return t}function c0(e,t){var r=Object.getOwnPropertyDescriptor(e,t);return r?r.configurable:!0}var l0=(function(){function e(t){var r=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:window;Rr(this,e),this.addedPropsMapInSandbox=new Map,this.modifiedPropsOriginalValueMapInSandbox=new Map,this.currentUpdatedPropsValueMap=new Map,this.name=void 0,this.proxy=void 0,this.globalContext=void 0,this.type=void 0,this.sandboxRunning=!0,this.latestSetProp=null,this.name=t,this.globalContext=n,this.type=ot.LegacyProxy;var o=this.addedPropsMapInSandbox,i=this.modifiedPropsOriginalValueMapInSandbox,a=this.currentUpdatedPropsValueMap,s=n,u=Object.create(null),c=function(p,v,y){var m=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0;return r.sandboxRunning&&(s.hasOwnProperty(p)?i.has(p)||i.set(p,y):o.set(p,v),a.set(p,v),m&&(s[p]=v),r.latestSetProp=p),!0},l=new Proxy(u,{set:function(p,v,y){var m=s[v];return c(v,y,m,!0)},get:function(p,v){if(v==="top"||v==="parent"||v==="window"||v==="self")return l;var y=s[v];return Wg(s,y)},has:function(p,v){return v in s},getOwnPropertyDescriptor:function(p,v){var y=Object.getOwnPropertyDescriptor(s,v);return y&&!y.configurable&&(y.configurable=!0),y},defineProperty:function(p,v,y){var m=s[v],h=Reflect.defineProperty(s,v,y),b=s[v];return c(v,b,m,!1),h}});this.proxy=l}return Ar(e,[{key:"setWindowProp",value:function(r,n,o){n===void 0&&o?delete this.globalContext[r]:c0(this.globalContext,r)&&Ie(r)!=="symbol"&&(Object.defineProperty(this.globalContext,r,{writable:!0,configurable:!0}),this.globalContext[r]=n)}},{key:"active",value:function(){var r=this;this.sandboxRunning||this.currentUpdatedPropsValueMap.forEach(function(n,o){return r.setWindowProp(o,n)}),this.sandboxRunning=!0}},{key:"inactive",value:function(){var r=this;this.modifiedPropsOriginalValueMapInSandbox.forEach(function(n,o){return r.setWindowProp(o,n)}),this.addedPropsMapInSandbox.forEach(function(n,o){return r.setWindowProp(o,void 0,!0)}),this.sandboxRunning=!1}},{key:"patchDocument",value:function(){}}]),e})(),zr;(function(e){e[e.STYLE=1]="STYLE",e[e.MEDIA=4]="MEDIA",e[e.SUPPORTS=12]="SUPPORTS",e[e.IMPORT=3]="IMPORT",e[e.FONT_FACE=5]="FONT_FACE",e[e.PAGE=6]="PAGE",e[e.KEYFRAMES=7]="KEYFRAMES",e[e.KEYFRAME=8]="KEYFRAME"})(zr||(zr={}));var Cn=function(t){return[].slice.call(t,0)},f0=HTMLBodyElement.prototype.appendChild,Ug=(function(){function e(){Rr(this,e),this.sheet=void 0,this.swapNode=void 0;var t=document.createElement("style");f0.call(document.body,t),this.swapNode=t,this.sheet=t.sheet,this.sheet.disabled=!0}return Ar(e,[{key:"process",value:function(r){var n=this,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";if(!(e.ModifiedTag in r)){if(r.textContent!==""){var i,a=document.createTextNode(r.textContent||"");this.swapNode.appendChild(a);var s=this.swapNode.sheet,u=Cn((i=s?.cssRules)!==null&&i!==void 0?i:[]),c=this.rewrite(u,o);r.textContent=c,this.swapNode.removeChild(a),r[e.ModifiedTag]=!0;return}var l=new MutationObserver(function(f){for(var p=0;p<f.length;p+=1){var v=f[p];if(e.ModifiedTag in r)return;if(v.type==="childList"){var y,m=r.sheet,h=Cn((y=m?.cssRules)!==null&&y!==void 0?y:[]),b=n.rewrite(h,o);r.textContent=b,r[e.ModifiedTag]=!0}}});l.observe(r,{childList:!0})}}},{key:"rewrite",value:function(r){var n=this,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",i="";return r.forEach(function(a){switch(a.type){case zr.STYLE:i+=n.ruleStyle(a,o);break;case zr.MEDIA:i+=n.ruleMedia(a,o);break;case zr.SUPPORTS:i+=n.ruleSupport(a,o);break;default:typeof a.cssText=="string"&&(i+="".concat(a.cssText));break}}),i}},{key:"ruleStyle",value:function(r,n){var o=/((?:[^\w\-.#]|^)(body|html|:root))/gm,i=/(html[^\w{[]+)/gm,a=r.selectorText.trim(),s="";if(typeof r.cssText=="string"&&(s=r.cssText),a==="html"||a==="body"||a===":root")return s.replace(o,n);if(i.test(r.selectorText)){var u=/(html[^\w{]+)(\+|~)/gm;u.test(r.selectorText)||(s=s.replace(i,""))}return s=s.replace(/^[\s\S]+{/,function(c){return c.replace(/(^|,\n?)([^,]+)/g,function(l,f,p){return o.test(l)?l.replace(o,function(v){var y=[",","("];return v&&y.includes(v[0])?"".concat(v[0]).concat(n):n}):"".concat(f).concat(n," ").concat(p.replace(/^ */,""))})}),s}},{key:"ruleMedia",value:function(r,n){var o=this.rewrite(Cn(r.cssRules),n);return"@media ".concat(r.conditionText||r.media.mediaText," {").concat(o,"}")}},{key:"ruleSupport",value:function(r,n){var o=this.rewrite(Cn(r.cssRules),n);return"@supports ".concat(r.conditionText||r.cssText.split("{")[0]," {").concat(o,"}")}}]),e})();Ug.ModifiedTag="Symbol(style-modified-qiankun)";var Us,Mu="data-qiankun",Iu=function(t,r,n){Us||(Us=new Ug),r.tagName==="LINK"&&console.warn("Feature: sandbox.experimentalStyleIsolation is not support for link element yet.");var o=t;if(o){var i=(o.tagName||"").toLowerCase();if(i&&r.tagName==="STYLE"){var a="".concat(i,"[").concat(Mu,'="').concat(n,'"]');Us.process(r,a)}}},Ks,th;function d0(){if(th)return Ks;th=1;var e="__lodash_hash_undefined__";function t(r){return this.__data__.set(r,e),this}return Ks=t,Ks}var Ys,rh;function p0(){if(rh)return Ys;rh=1;function e(t){return this.__data__.has(t)}return Ys=e,Ys}var zs,nh;function h0(){if(nh)return zs;nh=1;var e=hc(),t=d0(),r=p0();function n(o){var i=-1,a=o==null?0:o.length;for(this.__data__=new e;++i<a;)this.add(o[i])}return n.prototype.add=n.prototype.push=t,n.prototype.has=r,zs=n,zs}var Xs,oh;function v0(){if(oh)return Xs;oh=1;function e(t,r,n,o){for(var i=t.length,a=n+(o?1:-1);o?a--:++a<i;)if(r(t[a],a,t))return a;return-1}return Xs=e,Xs}var Js,ih;function g0(){if(ih)return Js;ih=1;function e(t){return t!==t}return Js=e,Js}var Qs,ah;function m0(){if(ah)return Qs;ah=1;function e(t,r,n){for(var o=n-1,i=t.length;++o<i;)if(t[o]===r)return o;return-1}return Qs=e,Qs}var Zs,sh;function y0(){if(sh)return Zs;sh=1;var e=v0(),t=g0(),r=m0();function n(o,i,a){return i===i?r(o,i,a):e(o,t,a)}return Zs=n,Zs}var eu,uh;function b0(){if(uh)return eu;uh=1;var e=y0();function t(r,n){var o=r==null?0:r.length;return!!o&&e(r,n,0)>-1}return eu=t,eu}var tu,ch;function _0(){if(ch)return tu;ch=1;function e(t,r,n){for(var o=-1,i=t==null?0:t.length;++o<i;)if(n(r,t[o]))return!0;return!1}return tu=e,tu}var ru,lh;function w0(){if(lh)return ru;lh=1;function e(t,r){return t.has(r)}return ru=e,ru}var nu,fh;function S0(){if(fh)return nu;fh=1;var e=h0(),t=b0(),r=_0(),n=Fg(),o=mo(),i=w0(),a=200;function s(u,c,l,f){var p=-1,v=t,y=!0,m=u.length,h=[],b=c.length;if(!m)return h;l&&(c=n(c,o(l))),f?(v=r,y=!1):c.length>=a&&(v=i,y=!1,c=new e(c));e:for(;++p<m;){var _=u[p],O=l==null?_:l(_);if(_=f||_!==0?_:0,y&&O===O){for(var E=b;E--;)if(c[E]===O)continue e;h.push(_)}else v(c,O,f)||h.push(_)}return h}return nu=s,nu}var ou,dh;function E0(){if(dh)return ou;dh=1;var e=S0(),t=Og(),r=_g(),n=t(function(o,i){return r(o)?e(o,i):[]});return ou=n,ou}var T0=E0();const Kg=it(T0);var x0=window.Proxy?["Array","ArrayBuffer","Boolean","constructor","DataView","Date","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","Error","escape","eval","EvalError","Float32Array","Float64Array","Function","hasOwnProperty","Infinity","Int16Array","Int32Array","Int8Array","isFinite","isNaN","isPrototypeOf","JSON","Map","Math","NaN","Number","Object","parseFloat","parseInt","Promise","propertyIsEnumerable","Proxy","RangeError","ReferenceError","Reflect","RegExp","Set","String","Symbol","SyntaxError","toLocaleString","toString","TypeError","Uint16Array","Uint32Array","Uint8Array","Uint8ClampedArray","undefined","unescape","URIError","valueOf","WeakMap","WeakSet"].filter(function(e){return e in window}):[],O0=["AbortController","AbortSignal","addEventListener","alert","AnalyserNode","Animation","AnimationEffectReadOnly","AnimationEffectTiming","AnimationEffectTimingReadOnly","AnimationEvent","AnimationPlaybackEvent","AnimationTimeline","applicationCache","ApplicationCache","ApplicationCacheErrorEvent","atob","Attr","Audio","AudioBuffer","AudioBufferSourceNode","AudioContext","AudioDestinationNode","AudioListener","AudioNode","AudioParam","AudioProcessingEvent","AudioScheduledSourceNode","AudioWorkletGlobalScope","AudioWorkletNode","AudioWorkletProcessor","BarProp","BaseAudioContext","BatteryManager","BeforeUnloadEvent","BiquadFilterNode","Blob","BlobEvent","blur","BroadcastChannel","btoa","BudgetService","ByteLengthQueuingStrategy","Cache","caches","CacheStorage","cancelAnimationFrame","cancelIdleCallback","CanvasCaptureMediaStreamTrack","CanvasGradient","CanvasPattern","CanvasRenderingContext2D","ChannelMergerNode","ChannelSplitterNode","CharacterData","clearInterval","clearTimeout","clientInformation","ClipboardEvent","ClipboardItem","close","closed","CloseEvent","Comment","CompositionEvent","CompressionStream","confirm","console","ConstantSourceNode","ConvolverNode","CountQueuingStrategy","createImageBitmap","Credential","CredentialsContainer","crypto","Crypto","CryptoKey","CSS","CSSConditionRule","CSSFontFaceRule","CSSGroupingRule","CSSImportRule","CSSKeyframeRule","CSSKeyframesRule","CSSMatrixComponent","CSSMediaRule","CSSNamespaceRule","CSSPageRule","CSSPerspective","CSSRotate","CSSRule","CSSRuleList","CSSScale","CSSSkew","CSSSkewX","CSSSkewY","CSSStyleDeclaration","CSSStyleRule","CSSStyleSheet","CSSSupportsRule","CSSTransformValue","CSSTranslate","CustomElementRegistry","customElements","CustomEvent","DataTransfer","DataTransferItem","DataTransferItemList","DecompressionStream","defaultstatus","defaultStatus","DelayNode","DeviceMotionEvent","DeviceOrientationEvent","devicePixelRatio","dispatchEvent","document","Document","DocumentFragment","DocumentType","DOMError","DOMException","DOMImplementation","DOMMatrix","DOMMatrixReadOnly","DOMParser","DOMPoint","DOMPointReadOnly","DOMQuad","DOMRect","DOMRectList","DOMRectReadOnly","DOMStringList","DOMStringMap","DOMTokenList","DragEvent","DynamicsCompressorNode","Element","ErrorEvent","event","Event","EventSource","EventTarget","external","fetch","File","FileList","FileReader","find","focus","FocusEvent","FontFace","FontFaceSetLoadEvent","FormData","FormDataEvent","frameElement","frames","GainNode","Gamepad","GamepadButton","GamepadEvent","getComputedStyle","getSelection","HashChangeEvent","Headers","history","History","HTMLAllCollection","HTMLAnchorElement","HTMLAreaElement","HTMLAudioElement","HTMLBaseElement","HTMLBodyElement","HTMLBRElement","HTMLButtonElement","HTMLCanvasElement","HTMLCollection","HTMLContentElement","HTMLDataElement","HTMLDataListElement","HTMLDetailsElement","HTMLDialogElement","HTMLDirectoryElement","HTMLDivElement","HTMLDListElement","HTMLDocument","HTMLElement","HTMLEmbedElement","HTMLFieldSetElement","HTMLFontElement","HTMLFormControlsCollection","HTMLFormElement","HTMLFrameElement","HTMLFrameSetElement","HTMLHeadElement","HTMLHeadingElement","HTMLHRElement","HTMLHtmlElement","HTMLIFrameElement","HTMLImageElement","HTMLInputElement","HTMLLabelElement","HTMLLegendElement","HTMLLIElement","HTMLLinkElement","HTMLMapElement","HTMLMarqueeElement","HTMLMediaElement","HTMLMenuElement","HTMLMetaElement","HTMLMeterElement","HTMLModElement","HTMLObjectElement","HTMLOListElement","HTMLOptGroupElement","HTMLOptionElement","HTMLOptionsCollection","HTMLOutputElement","HTMLParagraphElement","HTMLParamElement","HTMLPictureElement","HTMLPreElement","HTMLProgressElement","HTMLQuoteElement","HTMLScriptElement","HTMLSelectElement","HTMLShadowElement","HTMLSlotElement","HTMLSourceElement","HTMLSpanElement","HTMLStyleElement","HTMLTableCaptionElement","HTMLTableCellElement","HTMLTableColElement","HTMLTableElement","HTMLTableRowElement","HTMLTableSectionElement","HTMLTemplateElement","HTMLTextAreaElement","HTMLTimeElement","HTMLTitleElement","HTMLTrackElement","HTMLUListElement","HTMLUnknownElement","HTMLVideoElement","IDBCursor","IDBCursorWithValue","IDBDatabase","IDBFactory","IDBIndex","IDBKeyRange","IDBObjectStore","IDBOpenDBRequest","IDBRequest","IDBTransaction","IDBVersionChangeEvent","IdleDeadline","IIRFilterNode","Image","ImageBitmap","ImageBitmapRenderingContext","ImageCapture","ImageData","indexedDB","innerHeight","innerWidth","InputEvent","IntersectionObserver","IntersectionObserverEntry","Intl","isSecureContext","KeyboardEvent","KeyframeEffect","KeyframeEffectReadOnly","length","localStorage","location","Location","locationbar","matchMedia","MediaDeviceInfo","MediaDevices","MediaElementAudioSourceNode","MediaEncryptedEvent","MediaError","MediaKeyMessageEvent","MediaKeySession","MediaKeyStatusMap","MediaKeySystemAccess","MediaList","MediaMetadata","MediaQueryList","MediaQueryListEvent","MediaRecorder","MediaSettingsRange","MediaSource","MediaStream","MediaStreamAudioDestinationNode","MediaStreamAudioSourceNode","MediaStreamConstraints","MediaStreamEvent","MediaStreamTrack","MediaStreamTrackEvent","menubar","MessageChannel","MessageEvent","MessagePort","MIDIAccess","MIDIConnectionEvent","MIDIInput","MIDIInputMap","MIDIMessageEvent","MIDIOutput","MIDIOutputMap","MIDIPort","MimeType","MimeTypeArray","MouseEvent","moveBy","moveTo","MutationEvent","MutationObserver","MutationRecord","name","NamedNodeMap","NavigationPreloadManager","navigator","Navigator","NavigatorUAData","NetworkInformation","Node","NodeFilter","NodeIterator","NodeList","Notification","OfflineAudioCompletionEvent","OfflineAudioContext","offscreenBuffering","OffscreenCanvas","OffscreenCanvasRenderingContext2D","onabort","onafterprint","onanimationend","onanimationiteration","onanimationstart","onappinstalled","onauxclick","onbeforeinstallprompt","onbeforeprint","onbeforeunload","onblur","oncancel","oncanplay","oncanplaythrough","onchange","onclick","onclose","oncontextmenu","oncuechange","ondblclick","ondevicemotion","ondeviceorientation","ondeviceorientationabsolute","ondrag","ondragend","ondragenter","ondragleave","ondragover","ondragstart","ondrop","ondurationchange","onemptied","onended","onerror","onfocus","ongotpointercapture","onhashchange","oninput","oninvalid","onkeydown","onkeypress","onkeyup","onlanguagechange","onload","onloadeddata","onloadedmetadata","onloadstart","onlostpointercapture","onmessage","onmessageerror","onmousedown","onmouseenter","onmouseleave","onmousemove","onmouseout","onmouseover","onmouseup","onmousewheel","onoffline","ononline","onpagehide","onpageshow","onpause","onplay","onplaying","onpointercancel","onpointerdown","onpointerenter","onpointerleave","onpointermove","onpointerout","onpointerover","onpointerup","onpopstate","onprogress","onratechange","onrejectionhandled","onreset","onresize","onscroll","onsearch","onseeked","onseeking","onselect","onstalled","onstorage","onsubmit","onsuspend","ontimeupdate","ontoggle","ontransitionend","onunhandledrejection","onunload","onvolumechange","onwaiting","onwheel","open","openDatabase","opener","Option","origin","OscillatorNode","outerHeight","outerWidth","OverconstrainedError","PageTransitionEvent","pageXOffset","pageYOffset","PannerNode","parent","Path2D","PaymentAddress","PaymentRequest","PaymentRequestUpdateEvent","PaymentResponse","performance","Performance","PerformanceEntry","PerformanceLongTaskTiming","PerformanceMark","PerformanceMeasure","PerformanceNavigation","PerformanceNavigationTiming","PerformanceObserver","PerformanceObserverEntryList","PerformancePaintTiming","PerformanceResourceTiming","PerformanceTiming","PeriodicWave","Permissions","PermissionStatus","personalbar","PhotoCapabilities","Plugin","PluginArray","PointerEvent","PopStateEvent","postMessage","Presentation","PresentationAvailability","PresentationConnection","PresentationConnectionAvailableEvent","PresentationConnectionCloseEvent","PresentationConnectionList","PresentationReceiver","PresentationRequest","print","ProcessingInstruction","ProgressEvent","PromiseRejectionEvent","prompt","PushManager","PushSubscription","PushSubscriptionOptions","queueMicrotask","RadioNodeList","Range","ReadableByteStreamController","ReadableStream","ReadableStreamBYOBReader","ReadableStreamBYOBRequest","ReadableStreamDefaultController","ReadableStreamDefaultReader","registerProcessor","RemotePlayback","removeEventListener","reportError","Request","requestAnimationFrame","requestIdleCallback","resizeBy","ResizeObserver","ResizeObserverEntry","resizeTo","Response","RTCCertificate","RTCDataChannel","RTCDataChannelEvent","RTCDtlsTransport","RTCIceCandidate","RTCIceGatherer","RTCIceTransport","RTCPeerConnection","RTCPeerConnectionIceEvent","RTCRtpContributingSource","RTCRtpReceiver","RTCRtpSender","RTCSctpTransport","RTCSessionDescription","RTCStatsReport","RTCTrackEvent","screen","Screen","screenLeft","ScreenOrientation","screenTop","screenX","screenY","ScriptProcessorNode","scroll","scrollbars","scrollBy","scrollTo","scrollX","scrollY","SecurityPolicyViolationEvent","Selection","self","ServiceWorker","ServiceWorkerContainer","ServiceWorkerRegistration","sessionStorage","setInterval","setTimeout","ShadowRoot","SharedWorker","SourceBuffer","SourceBufferList","speechSynthesis","SpeechSynthesisEvent","SpeechSynthesisUtterance","StaticRange","status","statusbar","StereoPannerNode","stop","Storage","StorageEvent","StorageManager","structuredClone","styleMedia","StyleSheet","StyleSheetList","SubmitEvent","SubtleCrypto","SVGAElement","SVGAngle","SVGAnimatedAngle","SVGAnimatedBoolean","SVGAnimatedEnumeration","SVGAnimatedInteger","SVGAnimatedLength","SVGAnimatedLengthList","SVGAnimatedNumber","SVGAnimatedNumberList","SVGAnimatedPreserveAspectRatio","SVGAnimatedRect","SVGAnimatedString","SVGAnimatedTransformList","SVGAnimateElement","SVGAnimateMotionElement","SVGAnimateTransformElement","SVGAnimationElement","SVGCircleElement","SVGClipPathElement","SVGComponentTransferFunctionElement","SVGDefsElement","SVGDescElement","SVGDiscardElement","SVGElement","SVGEllipseElement","SVGFEBlendElement","SVGFEColorMatrixElement","SVGFEComponentTransferElement","SVGFECompositeElement","SVGFEConvolveMatrixElement","SVGFEDiffuseLightingElement","SVGFEDisplacementMapElement","SVGFEDistantLightElement","SVGFEDropShadowElement","SVGFEFloodElement","SVGFEFuncAElement","SVGFEFuncBElement","SVGFEFuncGElement","SVGFEFuncRElement","SVGFEGaussianBlurElement","SVGFEImageElement","SVGFEMergeElement","SVGFEMergeNodeElement","SVGFEMorphologyElement","SVGFEOffsetElement","SVGFEPointLightElement","SVGFESpecularLightingElement","SVGFESpotLightElement","SVGFETileElement","SVGFETurbulenceElement","SVGFilterElement","SVGForeignObjectElement","SVGGElement","SVGGeometryElement","SVGGradientElement","SVGGraphicsElement","SVGImageElement","SVGLength","SVGLengthList","SVGLinearGradientElement","SVGLineElement","SVGMarkerElement","SVGMaskElement","SVGMatrix","SVGMetadataElement","SVGMPathElement","SVGNumber","SVGNumberList","SVGPathElement","SVGPatternElement","SVGPoint","SVGPointList","SVGPolygonElement","SVGPolylineElement","SVGPreserveAspectRatio","SVGRadialGradientElement","SVGRect","SVGRectElement","SVGScriptElement","SVGSetElement","SVGStopElement","SVGStringList","SVGStyleElement","SVGSVGElement","SVGSwitchElement","SVGSymbolElement","SVGTextContentElement","SVGTextElement","SVGTextPathElement","SVGTextPositioningElement","SVGTitleElement","SVGTransform","SVGTransformList","SVGTSpanElement","SVGUnitTypes","SVGUseElement","SVGViewElement","TaskAttributionTiming","Text","TextDecoder","TextDecoderStream","TextEncoder","TextEncoderStream","TextEvent","TextMetrics","TextTrack","TextTrackCue","TextTrackCueList","TextTrackList","TimeRanges","ToggleEvent","toolbar","top","Touch","TouchEvent","TouchList","TrackEvent","TransformStream","TransformStreamDefaultController","TransitionEvent","TreeWalker","UIEvent","URL","URLSearchParams","ValidityState","visualViewport","VisualViewport","VTTCue","WaveShaperNode","WebAssembly","WebGL2RenderingContext","WebGLActiveInfo","WebGLBuffer","WebGLContextEvent","WebGLFramebuffer","WebGLProgram","WebGLQuery","WebGLRenderbuffer","WebGLRenderingContext","WebGLSampler","WebGLShader","WebGLShaderPrecisionFormat","WebGLSync","WebGLTexture","WebGLTransformFeedback","WebGLUniformLocation","WebGLVertexArrayObject","WebSocket","WheelEvent","window","Window","Worker","WritableStream","WritableStreamDefaultController","WritableStreamDefaultWriter","XMLDocument","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload","XMLSerializer","XPathEvaluator","XPathExpression","XPathResult","XSLTProcessor"];function C0(e){return e.filter(function(r){return r in this?!1:this[r]=!0},Object.create(null))}function Rc(e){return e.reduce(function(t,r){return t[r]=!0,t},Object.create(null))}var P0=Rc(O0.concat([]));function A0(e){return e in P0}var R0=Object.defineProperty,M0=window.__QIANKUN_DEVELOPMENT__?["__REACT_ERROR_OVERLAY_GLOBAL_HOOK__","event"]:[],ph=["System","__cjsWrapper"].concat(M0),Lu=!1,Yg=["document","top","parent","eval"],zg=["window","self","globalThis","hasOwnProperty"].concat([]),_o=Array.from(new Set(Kg.apply(void 0,[x0.concat(zg).concat("requestAnimationFrame")].concat(Yg)))),I0=Rc(_o),L0=Rc(Kg.apply(void 0,[_o].concat(pt(Yg.concat(zg))))),hh=new Map([["fetch",!0],["mockDomAPIInBlackList",!1]]);function q0(e,t){var r=new Map,n={};return Object.getOwnPropertyNames(e).filter(function(o){var i=Object.getOwnPropertyDescriptor(e,o);return!i?.configurable}).forEach(function(o){var i=Object.getOwnPropertyDescriptor(e,o);if(i){var a=Object.prototype.hasOwnProperty.call(i,"get");(o==="top"||o==="parent"||o==="self"||o==="window"||o==="document"&&t||Lu)&&(i.configurable=!0,a||(i.writable=!0)),a&&r.set(o,!0),R0(n,o,Object.freeze(i))}}),{fakeWindow:n,propertiesWithGetter:r}}var iu=0,j0=(function(){function e(t){var r=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:window,o=arguments.length>2?arguments[2]:void 0;Rr(this,e),this.updatedValueSet=new Set,this.document=document,this.name=void 0,this.type=void 0,this.proxy=void 0,this.sandboxRunning=!0,this.latestSetProp=null,this.globalWhitelistPrevDescriptor={},this.globalContext=void 0,this.name=t,this.globalContext=n,this.type=ot.Proxy;var i=this.updatedValueSet,a=o||{},s=a.speedy,u=q0(n,!!s),c=u.fakeWindow,l=u.propertiesWithGetter,f=new Map,p=new Proxy(c,{set:function(m,h,b){if(r.sandboxRunning){if(r.registerRunningApp(t,p),typeof h=="string"&&ph.indexOf(h)!==-1)r.globalWhitelistPrevDescriptor[h]=Object.getOwnPropertyDescriptor(n,h),n[h]=b;else if(!m.hasOwnProperty(h)&&n.hasOwnProperty(h)){var _=Object.getOwnPropertyDescriptor(n,h),O=_.writable,E=_.configurable,M=_.enumerable,L=_.set;(O||L)&&Object.defineProperty(m,h,{configurable:E,enumerable:M,writable:!0,value:b})}else m[h]=b;return i.add(h),r.latestSetProp=h,!0}return!0},get:function(m,h){if(r.registerRunningApp(t,p),h===Symbol.unscopables)return L0;if(h==="window"||h==="self"||h==="globalThis"||Lu)return p;if(h==="top"||h==="parent"||Lu)return n===n.parent?p:n[h];if(h==="hasOwnProperty")return v;if(h==="document")return r.document;if(h==="eval")return eval;if(h==="string"&&ph.indexOf(h)!==-1)return n[h];var b=l.has(h)?n:h in m?m:n,_=b[h];if(e0(b,h)||!A0(h)&&!hh.has(h))return _;var O=hh.get(h)?ke:n;return Wg(O,_)},has:function(m,h){return h in I0||h in m||h in n},getOwnPropertyDescriptor:function(m,h){if(m.hasOwnProperty(h)){var b=Object.getOwnPropertyDescriptor(m,h);return f.set(h,"target"),b}if(n.hasOwnProperty(h)){var _=Object.getOwnPropertyDescriptor(n,h);return f.set(h,"globalContext"),_&&!_.configurable&&(_.configurable=!0),_}},ownKeys:function(m){return C0(Reflect.ownKeys(n).concat(Reflect.ownKeys(m)))},defineProperty:function(m,h,b){var _=f.get(h);switch(_){case"globalContext":return Reflect.defineProperty(n,h,b);default:return Reflect.defineProperty(m,h,b)}},deleteProperty:function(m,h){return r.registerRunningApp(t,p),m.hasOwnProperty(h)&&(delete m[h],i.delete(h)),!0},getPrototypeOf:function(){return Reflect.getPrototypeOf(n)}});this.proxy=p,iu++;function v(y){return this!==p&&this!==null&&Ie(this)==="object"?Object.prototype.hasOwnProperty.call(this,y):c.hasOwnProperty(y)||n.hasOwnProperty(y)}}return Ar(e,[{key:"active",value:function(){this.sandboxRunning||iu++,this.sandboxRunning=!0}},{key:"inactive",value:function(){var r=this;--iu===0&&Object.keys(this.globalWhitelistPrevDescriptor).forEach(function(n){var o=r.globalWhitelistPrevDescriptor[n];o?Object.defineProperty(r.globalContext,n,o):delete r.globalContext[n]}),this.sandboxRunning=!1}},{key:"patchDocument",value:function(r){this.document=r}},{key:"registerRunningApp",value:function(r,n){if(this.sandboxRunning){var o=Ru();(!o||o.name!==r)&&s0({name:r,window:n}),QE(u0)}}}]),e})(),Mc="SCRIPT",Un="LINK",Ic="STYLE",Xg=Symbol("target"),Jg=Symbol("refNodeNo"),Zt=Symbol("qiankun-overwritten"),un=function(t){return t.querySelector(kr)};function D0(e){return!e.type||["text/javascript","module","application/javascript","text/ecmascript","application/ecmascript"].indexOf(e.type)!==-1}function Lc(e){return e?.toUpperCase()===Un||e?.toUpperCase()===Ic||e?.toUpperCase()===Mc}function Qg(e){var t,r;return!e.textContent&&(((t=e.sheet)===null||t===void 0?void 0:t.cssRules.length)||((r=am(e))===null||r===void 0?void 0:r.length))}var qu=new Map;function Gt(e,t,r){var n=qu.get(e)||{bootstrappingPatchCount:0,mountingPatchCount:0};switch(t){case"increase":n["".concat(r,"PatchCount")]+=1;break;case"decrease":n["".concat(r,"PatchCount")]>0&&(n["".concat(r,"PatchCount")]-=1);break}qu.set(e,n)}function Zg(){return Array.from(qu.entries()).every(function(e){var t=Sc(e,2),r=t[1],n=r.bootstrappingPatchCount,o=r.mountingPatchCount;return n===0&&o===0})}function em(e,t){return Object.defineProperties(e,{srcElement:{get:t},target:{get:t}}),e}function tm(e){var t=new CustomEvent("load"),r=em(t,function(){return e});Ft(e.onload)?e.onload(r):e.dispatchEvent(r)}function rm(e){var t=new CustomEvent("error"),r=em(t,function(){return e});Ft(e.onerror)?e.onerror(r):e.dispatchEvent(r)}function G0(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:fetch,n=document.createElement("style"),o=e.href;return n.dataset.qiankunHref=o,r(o).then(function(i){return i.text()}).then(function(i){n.appendChild(document.createTextNode(i)),t(n),tm(e)}).catch(function(){return rm(e)}),n}var vh=function(t,r,n){Object.defineProperty(t,r,{configurable:!0,enumerable:!1,writable:!0,value:n})},nm=new WeakMap,ju=new WeakMap,om=new WeakMap;function im(e){e.forEach(function(t){t instanceof HTMLStyleElement&&Qg(t)&&t.sheet&&nm.set(t,t.sheet.cssRules)})}function am(e){return nm.get(e)}function au(e){function t(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,o=r,i=e.rawDOMAppendOrInsertBefore,a=e.isInvokedByMicroApp,s=e.containerConfigGetter,u=e.target,c=u===void 0?"body":u;if(!Lc(o.tagName)||!a(o))return i.call(this,o,n);if(o.tagName){var l=s(o),f=l.appName,p=l.appWrapperGetter,v=l.proxy,y=l.strictGlobal,m=l.speedySandbox,h=l.dynamicStyleSheetElements,b=l.scopedCSS,_=l.excludeAssetFilter;switch(o.tagName){case Un:case Ic:{var O=r,E=O,M=E.href;if(_&&M&&_(M))return i.call(this,o,n);vh(O,Xg,c);var L=p();if(b){var F,N=((F=o.tagName)===null||F===void 0?void 0:F.toUpperCase())===Un&&o.rel==="stylesheet"&&o.href;if(N){var j,D=typeof wt.fetch=="function"?wt.fetch:(j=wt.fetch)===null||j===void 0?void 0:j.fn;O=G0(o,function(qe){return Iu(L,qe,f)},D),om.set(o,O)}else Iu(L,O,f)}var G=c==="head"?un(L):L,Q=G.contains(n)?n:null,ce;Q&&(ce=Array.from(G.childNodes).indexOf(Q));var ne=i.call(G,O,Q);return typeof ce=="number"&&ce!==-1&&vh(O,Jg,ce),h.push(O),ne}case Mc:{var pe=o,te=pe.src,W=pe.text;if(_&&te&&_(te)||!D0(o))return i.call(this,o,n);var $=p(),oe=c==="head"?un($):$,se=wt.fetch,ye=oe.contains(n)?n:null,he=m?_o:[];if(te){var Fe=!1;Wn(null,[te],v,{fetch:se,strictGlobal:y,scopedGlobalVariables:he,beforeExec:function(){var Ke=function(){var Ze=Object.getOwnPropertyDescriptor(document,"currentScript");return!Ze||Ze.configurable};Ke()&&(Object.defineProperty(document,"currentScript",{get:function(){return o},configurable:!0}),Fe=!0)},success:function(){tm(o),Fe&&delete document.currentScript,o=null},error:function(){rm(o),Fe&&delete document.currentScript,o=null}});var Qe=document.createComment("dynamic script ".concat(te," replaced by qiankun"));return ju.set(o,Qe),i.call(oe,Qe,ye)}Wn(null,["<script>".concat(W,"<\/script>")],v,{strictGlobal:y,scopedGlobalVariables:he});var Ue=document.createComment("dynamic inline script replaced by qiankun");return ju.set(o,Ue),i.call(oe,Ue,ye)}}}return i.call(this,o,n)}return t[Zt]=!0,t}function gh(e,t,r,n){function o(i){var a=i.tagName;if(!Lc(a)||!n(i))return e.call(this,i);try{var s,u=t(i),c=u.appWrapperGetter,l=u.dynamicStyleSheetElements;switch(a){case Ic:case Un:{s=om.get(i)||i;var f=l.indexOf(s);f!==-1&&l.splice(f,1);break}case Mc:{s=ju.get(i)||i;break}default:s=i}var p=c(),v=r==="head"?un(p):p;if(v.contains(s))return e.call(s.parentNode,s)}catch(y){console.warn(y)}return e.call(this,i)}return o[Zt]=!0,o}function sm(e,t){var r=HTMLHeadElement.prototype.appendChild,n=HTMLBodyElement.prototype.appendChild,o=HTMLHeadElement.prototype.insertBefore;r[Zt]!==!0&&n[Zt]!==!0&&o[Zt]!==!0&&(HTMLHeadElement.prototype.appendChild=au({rawDOMAppendOrInsertBefore:r,containerConfigGetter:t,isInvokedByMicroApp:e,target:"head"}),HTMLBodyElement.prototype.appendChild=au({rawDOMAppendOrInsertBefore:n,containerConfigGetter:t,isInvokedByMicroApp:e,target:"body"}),HTMLHeadElement.prototype.insertBefore=au({rawDOMAppendOrInsertBefore:o,containerConfigGetter:t,isInvokedByMicroApp:e,target:"head"}));var i=HTMLHeadElement.prototype.removeChild,a=HTMLBodyElement.prototype.removeChild;return i[Zt]!==!0&&a[Zt]!==!0&&(HTMLHeadElement.prototype.removeChild=gh(i,t,"head",e),HTMLBodyElement.prototype.removeChild=gh(a,t,"body",e)),function(){HTMLHeadElement.prototype.appendChild=r,HTMLHeadElement.prototype.removeChild=i,HTMLBodyElement.prototype.appendChild=n,HTMLBodyElement.prototype.removeChild=a,HTMLHeadElement.prototype.insertBefore=o}}function um(e,t){e.forEach(function(r){var n=t(r);if(n&&r instanceof HTMLStyleElement&&Qg(r)){var o=am(r);if(o)for(var i=0;i<o.length;i++){var a=o[i],s=r.sheet;s.insertRule(a.cssText,s.cssRules.length)}}})}function Kn(e,t,r){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1,i=arguments.length>5?arguments[5]:void 0,a=r.proxy,s=[],u=sm(function(){return rg(window.location).some(function(c){return c===e})},function(){return{appName:e,appWrapperGetter:t,proxy:a,strictGlobal:!1,speedySandbox:!1,scopedCSS:o,dynamicStyleSheetElements:s,excludeAssetFilter:i}});return n||Gt(e,"increase","bootstrapping"),n&&Gt(e,"increase","mounting"),function(){return n||Gt(e,"decrease","bootstrapping"),n&&Gt(e,"decrease","mounting"),Zg()&&u(),im(s),function(){um(s,function(f){var p=t();return p.contains(f)?!1:(document.head.appendChild.call(p,f),!0)}),n&&(s=[])}}}Object.defineProperty(ke,"__proxyAttachContainerConfigMap__",{enumerable:!1,writable:!0});Object.defineProperty(ke,"__currentLockingSandbox__",{enumerable:!1,writable:!0,configurable:!0});var N0=HTMLHeadElement.prototype.appendChild,F0=HTMLHeadElement.prototype.insertBefore;ke.__proxyAttachContainerConfigMap__=ke.__proxyAttachContainerConfigMap__||new WeakMap;var Yn=ke.__proxyAttachContainerConfigMap__,Du=new WeakMap,mh=new WeakMap,yt=new WeakMap;function H0(e){var t=e.sandbox,r=e.speedy,n=function(h,b){var _=Yn.get(b);_&&Du.set(h,_)};if(r){var o={},i=new Proxy(document,{set:function(h,b,_){switch(b){case"createElement":{o.createElement=_;break}case"querySelector":{o.querySelector=_;break}default:h[b]=_;break}return!0},get:function(h,b,_){switch(b){case"createElement":{var O=o.createElement||h.createElement;return function(){ke.__currentLockingSandbox__||(ke.__currentLockingSandbox__=t.name);for(var F=arguments.length,N=new Array(F),j=0;j<F;j++)N[j]=arguments[j];var D=O.call.apply(O,[h].concat(N));return ke.__currentLockingSandbox__===t.name&&(n(D,t.proxy),delete ke.__currentLockingSandbox__),D}}case"querySelector":{var E=o.querySelector||h.querySelector;return function(){for(var F=arguments.length,N=new Array(F),j=0;j<F;j++)N[j]=arguments[j];var D=N[0];switch(D){case"head":{var G=Yn.get(t.proxy);if(G){var Q=un(G.appWrapperGetter());return Q.appendChild=HTMLHeadElement.prototype.appendChild,Q.insertBefore=HTMLHeadElement.prototype.insertBefore,Q.removeChild=HTMLHeadElement.prototype.removeChild,Q}break}}return E.call.apply(E,[h].concat(N))}}}var M=h[b];return kg(M)&&!Vg(M)?function(){for(var F=arguments.length,N=new Array(F),j=0;j<F;j++)N[j]=arguments[j];return M.call.apply(M,[h].concat(pt(N.map(function(D){return D===_?h:D}))))}:M}});t.patchDocument(i);var a=MutationObserver.prototype.observe;if(!yt.has(a)){var s=function(h,b){var _=h instanceof Document?Zp:h;return a.call(this,_,b)};MutationObserver.prototype.observe=s,yt.set(a,s)}var u=Node.prototype.compareDocumentPosition;yt.has(u)||(Node.prototype.compareDocumentPosition=function(h){var b=h instanceof Document?Zp:h;return u.call(this,b)},yt.set(u,Node.prototype.compareDocumentPosition));var c=Object.getOwnPropertyDescriptor(Node.prototype,"parentNode");if(c&&!yt.has(c)){var l=c.get,f=c.configurable;if(l&&f){var p=Te(Te({},c),{},{get:function(){var h=l.call(this);if(h instanceof Document){var b,_=(b=Ru())===null||b===void 0?void 0:b.window;if(_)return _.document}return h}});Object.defineProperty(Node.prototype,"parentNode",p),yt.set(c,p)}}return function(){MutationObserver.prototype.observe=a,yt.delete(a),Node.prototype.compareDocumentPosition=u,yt.delete(u),c&&(Object.defineProperty(Node.prototype,"parentNode",c),yt.delete(c))}}var v=mh.get(document.createElement);if(!v){var y=document.createElement;Document.prototype.createElement=function(h,b){var _=y.call(this,h,b);if(Lc(h)){var O=Ru()||{},E=O.window;E&&n(_,E)}return _},document.hasOwnProperty("createElement")&&(document.createElement=Document.prototype.createElement),mh.set(Document.prototype.createElement,y)}return function(){v&&(Document.prototype.createElement=v,document.createElement=v)}}function cm(e,t,r){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1,i=arguments.length>5?arguments[5]:void 0,a=arguments.length>6&&arguments[6]!==void 0?arguments[6]:!1,s=r.proxy,u=Yn.get(s);u||(u={appName:e,proxy:s,appWrapperGetter:t,dynamicStyleSheetElements:[],strictGlobal:!0,speedySandbox:a,excludeAssetFilter:i,scopedCSS:o},Yn.set(s,u));var c=u,l=c.dynamicStyleSheetElements,f=sm(function(v){return Du.has(v)},function(v){return Du.get(v)}),p=H0({sandbox:r,speedy:a});return n||Gt(e,"increase","bootstrapping"),n&&Gt(e,"increase","mounting"),function(){return n||Gt(e,"decrease","bootstrapping"),n&&Gt(e,"decrease","mounting"),Zg()&&(f(),p()),im(l),function(){um(l,function(m){var h=t();if(!h.contains(m)){var b=m[Xg]==="head"?un(h):h,_=m[Jg];if(typeof _=="number"&&_!==-1){var O=b.childNodes[_]||null;return F0.call(b,m,O),!0}else return N0.call(b,m),!0}return!1})}}}function k0(){var e=function(o){return rn},t=[],r=[];return window.g_history&&Ft(window.g_history.listen)&&(e=window.g_history.listen.bind(window.g_history),window.g_history.listen=function(n){t.push(n);var o=e(n);return r.push(o),function(){o(),r.splice(r.indexOf(o),1),t.splice(t.indexOf(n),1)}}),function(){var o=rn;return t.length&&(o=function(){t.forEach(function(a){return window.g_history.listen(a)})}),r.forEach(function(i){return i()}),window.g_history&&Ft(window.g_history.listen)&&(window.g_history.listen=e),o}}var yh=window.setInterval,bh=window.clearInterval;function V0(e){var t=[];return e.clearInterval=function(r){return t=t.filter(function(n){return n!==r}),bh.call(window,r)},e.setInterval=function(r,n){for(var o=arguments.length,i=new Array(o>2?o-2:0),a=2;a<o;a++)i[a-2]=arguments[a];var s=yh.apply(void 0,[r,n].concat(i));return t=[].concat(pt(t),[s]),s},function(){return t.forEach(function(n){return e.clearInterval(n)}),e.setInterval=yh,e.clearInterval=bh,rn}}var _h=window.addEventListener,wh=window.removeEventListener;function B0(e){var t=new Map;return e.addEventListener=function(r,n,o){var i=t.get(r)||[];return t.set(r,[].concat(pt(i),[n])),_h.call(window,r,n,o)},e.removeEventListener=function(r,n,o){var i=t.get(r);return i&&i.length&&i.indexOf(n)!==-1&&i.splice(i.indexOf(n),1),wh.call(window,r,n,o)},function(){return t.forEach(function(n,o){return pt(n).forEach(function(i){return e.removeEventListener(o,i)})}),e.addEventListener=_h,e.removeEventListener=wh,rn}}function $0(e,t,r,n,o,i){var a,s=[function(){return V0(r.proxy)},function(){return B0(r.proxy)},function(){return k0()}],u=Et(Et(Et({},ot.LegacyProxy,[].concat(s,[function(){return Kn(e,t,r,!0,n,o)}])),ot.Proxy,[].concat(s,[function(){return cm(e,t,r,!0,n,o,i)}])),ot.Snapshot,[].concat(s,[function(){return Kn(e,t,r,!0,n,o)}]));return(a=u[r.type])===null||a===void 0?void 0:a.map(function(c){return c()})}function W0(e,t,r,n,o,i){var a,s=Et(Et(Et({},ot.LegacyProxy,[function(){return Kn(e,t,r,!1,n,o)}]),ot.Proxy,[function(){return cm(e,t,r,!1,n,o,i)}]),ot.Snapshot,[function(){return Kn(e,t,r,!1,n,o)}]);return(a=s[r.type])===null||a===void 0?void 0:a.map(function(u){return u()})}function Sh(e,t){for(var r in e)(e.hasOwnProperty(r)||r==="clearInterval")&&t(r)}var U0=(function(){function e(t){Rr(this,e),this.proxy=void 0,this.name=void 0,this.type=void 0,this.sandboxRunning=!0,this.windowSnapshot=void 0,this.modifyPropsMap={},this.name=t,this.proxy=window,this.type=ot.Snapshot}return Ar(e,[{key:"active",value:function(){var r=this;this.windowSnapshot={},Sh(window,function(n){r.windowSnapshot[n]=window[n]}),Object.keys(this.modifyPropsMap).forEach(function(n){window[n]=r.modifyPropsMap[n]}),this.sandboxRunning=!0}},{key:"inactive",value:function(){var r=this;this.modifyPropsMap={},Sh(window,function(n){window[n]!==r.windowSnapshot[n]&&(r.modifyPropsMap[n]=window[n],window[n]=r.windowSnapshot[n])}),this.sandboxRunning=!1}},{key:"patchDocument",value:function(){}}]),e})();function K0(e,t,r,n,o,i,a){var s;window.Proxy?s=n?new l0(e,i):new j0(e,i,{speedy:!!a}):s=new U0(e);var u=W0(e,t,s,r,o,a),c=[],l=[];return{instance:s,mount:function(){return ie(V.mark(function p(){var v,y;return V.wrap(function(h){for(;;)switch(h.prev=h.next){case 0:s.active(),v=l.slice(0,u.length),y=l.slice(u.length),v.length&&v.forEach(function(b){return b()}),c=$0(e,t,s,r,o,a),y.length&&y.forEach(function(b){return b()}),l=[];case 7:case"end":return h.stop()}},p)}))()},unmount:function(){return ie(V.mark(function p(){return V.wrap(function(y){for(;;)switch(y.prev=y.next){case 0:l=[].concat(pt(u),pt(c)).map(function(m){return m()}),s.inactive();case 2:case"end":return y.stop()}},p)}))()}}}var Y0=["singular","sandbox","excludeAssetFilter","globalContext"];function Gu(e,t){if(!e)throw t?new sn(t):new sn("element not existed!")}function jr(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:window;return e.length?e.reduce(function(n,o){return n.then(function(){return o(t,r)})},Promise.resolve()):Promise.resolve()}function Pn(e,t){return Nu.apply(this,arguments)}function Nu(){return Nu=ie(V.mark(function e(t,r){return V.wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return o.abrupt("return",typeof t=="function"?t(r):!!t);case 1:case"end":return o.stop()}},e)})),Nu.apply(this,arguments)}var lm=!!document.head.attachShadow||!!document.head.createShadowRoot;function Eh(e,t,r,n){var o=document.createElement("div");o.innerHTML=e;var i=o.firstChild;if(t)if(!lm)console.warn("[qiankun]: As current browser not support shadow dom, your strictStyleIsolation configuration will be ignored!");else{var a=i.innerHTML;i.innerHTML="";var s;i.attachShadow?s=i.attachShadow({mode:"open"}):s=i.createShadowRoot(),s.innerHTML=a}if(r){var u=i.getAttribute(Mu);u||i.setAttribute(Mu,n);var c=i.querySelectorAll("style")||[];cS(c,function(l){Iu(i,l,n)})}return i}function Th(e,t,r,n,o){return function(){if(t){if(r)throw new sn("strictStyleIsolation can not be used with legacy render!");if(n)throw new sn("experimentalStyleIsolation can not be used with legacy render!");var i=document.getElementById(Bg(e));return Gu(i,"Wrapper element for ".concat(e," is not existed!")),i}var a=o();return Gu(a,"Wrapper element for ".concat(e," is not existed!")),r&&lm?a.shadowRoot:a}}var z0=HTMLElement.prototype.appendChild,X0=HTMLElement.prototype.removeChild;function J0(e,t,r){var n=function(i,a){var s=i.element,u=i.loading,c=i.container;if(r)return r({loading:u,appContent:s?t:""});var l=a0(c);if(a!=="unmounted"){var f=(function(){switch(a){case"loading":case"mounting":return"Target container with ".concat(c," not existed while ").concat(e," ").concat(a,"!");case"mounted":return"Target container with ".concat(c," not existed after ").concat(e," ").concat(a,"!");default:return"Target container with ".concat(c," not existed while ").concat(e," rendering!")}})();Gu(l,f)}if(l&&!l.contains(s)){for(;l.firstChild;)X0.call(l,l.firstChild);s&&z0.call(l,s)}};return n}function Q0(e,t,r,n){if(Ws(e))return e;if(n){var o=r[n];if(Ws(o))return o}var i=r[t];if(Ws(i))return i;throw new sn("You need to export lifecycle functions in ".concat(t," entry"))}var Xt;function Z0(e){return Fu.apply(this,arguments)}function Fu(){return Fu=ie(V.mark(function e(t){var r,n,o,i,a,s,u,c,l,f,p,v,y,m,h,b,_,O,E,M,L,F,N,j,D,G,Q,ce,ne,pe,te,W,$,oe,se,ye,he,Fe,Qe,Ue,qe,Ke,at,Ze,st,Mr,d,g,S,T,x,w,q,R,I,C,H,P=arguments;return V.wrap(function(k){for(;;)switch(k.prev=k.next){case 0:return o=P.length>1&&P[1]!==void 0?P[1]:{},i=P.length>2?P[2]:void 0,a=t.entry,s=t.name,u=o0(s),c=o.singular,l=c===void 0?!1:c,f=o.sandbox,p=f===void 0?!0:f,v=o.excludeAssetFilter,y=o.globalContext,m=y===void 0?window:y,h=Vn(o,Y0),k.next=9,qg(a,h);case 9:return b=k.sent,_=b.template,O=b.execScripts,E=b.assetPublicPath,M=b.getExternalScripts,k.next=16,M();case 16:return k.next=18,Pn(l,t);case 18:if(!k.sent){k.next=21;break}return k.next=21,Xt&&Xt.promise;case 21:return L=r0(u,p)(_),F=Ie(p)==="object"&&!!p.strictStyleIsolation,N=i0(p),j=Eh(L,F,N,u),D="container"in t?t.container:void 0,G="render"in t?t.render:void 0,Q=J0(u,L,G),Q({element:j,loading:!0,container:D},"loading"),ce=Th(u,!!G,F,N,function(){return j}),ne=m,pe=function(){return Promise.resolve()},te=function(){return Promise.resolve()},W=Ie(p)==="object"&&!!p.loose,$=Ie(p)==="object"?p.speedy!==!1:!0,p&&(oe=K0(u,ce,N,W,v,ne,$),ne=oe.instance.proxy,pe=oe.mount,te=oe.unmount),se=Cg({},WS(ne,E),i,function(Y,Z){return cg(Y??[],Z??[])}),ye=se.beforeUnmount,he=ye===void 0?[]:ye,Fe=se.afterUnmount,Qe=Fe===void 0?[]:Fe,Ue=se.afterMount,qe=Ue===void 0?[]:Ue,Ke=se.beforeMount,at=Ke===void 0?[]:Ke,Ze=se.beforeLoad,st=Ze===void 0?[]:Ze,k.next=40,jr(fr(st),t,ne);case 40:return k.next=42,O(ne,p&&!W,{scopedGlobalVariables:$?_o:[]});case 42:return Mr=k.sent,d=Q0(Mr,s,ne,(r=oe)===null||r===void 0||(n=r.instance)===null||n===void 0?void 0:n.latestSetProp),g=d.bootstrap,S=d.mount,T=d.unmount,x=d.update,w=EE(u),q=w.onGlobalStateChange,R=w.setGlobalState,I=w.offGlobalStateChange,C=function(Z){return j=Z},H=function(){var Z=arguments.length>0&&arguments[0]!==void 0?arguments[0]:D,U,_e,we={name:u,bootstrap:g,mount:[ie(V.mark(function z(){return V.wrap(function(A){for(;;)switch(A.prev=A.next){case 0:case 1:case"end":return A.stop()}},z)})),ie(V.mark(function z(){return V.wrap(function(A){for(;;)switch(A.prev=A.next){case 0:return A.next=2,Pn(l,t);case 2:if(A.t0=A.sent,!A.t0){A.next=5;break}A.t0=Xt;case 5:if(!A.t0){A.next=7;break}return A.abrupt("return",Xt.promise);case 7:return A.abrupt("return",void 0);case 8:case"end":return A.stop()}},z)})),ie(V.mark(function z(){return V.wrap(function(A){for(;;)switch(A.prev=A.next){case 0:U=j,_e=Th(u,!!G,F,N,function(){return U});case 2:case"end":return A.stop()}},z)})),ie(V.mark(function z(){var re;return V.wrap(function(Ce){for(;;)switch(Ce.prev=Ce.next){case 0:re=Z!==D,(re||!U)&&(U=Eh(L,F,N,u),C(U)),Q({element:U,loading:!0,container:Z},"mounting");case 3:case"end":return Ce.stop()}},z)})),pe,ie(V.mark(function z(){return V.wrap(function(A){for(;;)switch(A.prev=A.next){case 0:return A.abrupt("return",jr(fr(at),t,ne));case 1:case"end":return A.stop()}},z)})),(function(){var z=ie(V.mark(function re(A){return V.wrap(function(ge){for(;;)switch(ge.prev=ge.next){case 0:return ge.abrupt("return",S(Te(Te({},A),{},{container:_e(),setGlobalState:R,onGlobalStateChange:q})));case 1:case"end":return ge.stop()}},re)}));return function(re){return z.apply(this,arguments)}})(),ie(V.mark(function z(){return V.wrap(function(A){for(;;)switch(A.prev=A.next){case 0:return A.abrupt("return",Q({element:U,loading:!1,container:Z},"mounted"));case 1:case"end":return A.stop()}},z)})),ie(V.mark(function z(){return V.wrap(function(A){for(;;)switch(A.prev=A.next){case 0:return A.abrupt("return",jr(fr(qe),t,ne));case 1:case"end":return A.stop()}},z)})),ie(V.mark(function z(){return V.wrap(function(A){for(;;)switch(A.prev=A.next){case 0:return A.next=2,Pn(l,t);case 2:if(!A.sent){A.next=4;break}Xt=new $g;case 4:case"end":return A.stop()}},z)})),ie(V.mark(function z(){return V.wrap(function(A){for(;;)switch(A.prev=A.next){case 0:case 1:case"end":return A.stop()}},z)}))],unmount:[ie(V.mark(function z(){return V.wrap(function(A){for(;;)switch(A.prev=A.next){case 0:return A.abrupt("return",jr(fr(he),t,ne));case 1:case"end":return A.stop()}},z)})),(function(){var z=ie(V.mark(function re(A){return V.wrap(function(ge){for(;;)switch(ge.prev=ge.next){case 0:return ge.abrupt("return",T(Te(Te({},A),{},{container:_e()})));case 1:case"end":return ge.stop()}},re)}));return function(re){return z.apply(this,arguments)}})(),te,ie(V.mark(function z(){return V.wrap(function(A){for(;;)switch(A.prev=A.next){case 0:return A.abrupt("return",jr(fr(Qe),t,ne));case 1:case"end":return A.stop()}},z)})),ie(V.mark(function z(){return V.wrap(function(A){for(;;)switch(A.prev=A.next){case 0:Q({element:null,loading:!1,container:Z},"unmounted"),I(u),U=null,C(U);case 4:case"end":return A.stop()}},z)})),ie(V.mark(function z(){return V.wrap(function(A){for(;;)switch(A.prev=A.next){case 0:return A.next=2,Pn(l,t);case 2:if(A.t0=A.sent,!A.t0){A.next=5;break}A.t0=Xt;case 5:if(!A.t0){A.next=7;break}Xt.resolve();case 7:case"end":return A.stop()}},z)}))]};return typeof x=="function"&&(we.update=x),we},k.abrupt("return",H);case 48:case"end":return k.stop()}},e)})),Fu.apply(this,arguments)}function xh(e,t){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})}var br;if(typeof window.requestIdleCallback<"u")br=window.requestIdleCallback;else if(typeof window.MessageChannel<"u"){var Oh=new MessageChannel,e1=Oh.port2,Ch=[];Oh.port1.onmessage=function(e){var t=e.data,r=Ch.shift();r&&xh(r,t.start)},br=function(t){Ch.push(t),e1.postMessage({start:Date.now()})}}else br=function(t){return setTimeout(xh,0,t,Date.now())};var t1=navigator.connection?navigator.connection.saveData||navigator.connection.type!=="wifi"&&navigator.connection.type!=="ethernet"&&/([23])g/.test(navigator.connection.effectiveType):!1;function fm(e,t){!navigator.onLine||t1||br(ie(V.mark(function r(){var n,o,i;return V.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.next=2,qg(e,t);case 2:n=s.sent,o=n.getExternalScripts,i=n.getExternalStyleSheets,br(i),br(o);case 7:case"end":return s.stop()}},r)})))}function su(e,t){window.addEventListener("single-spa:first-mount",function r(){var n=e.filter(function(o){return cc(o.name)===vt});n.forEach(function(o){var i=o.entry;return fm(i,t)}),window.removeEventListener("single-spa:first-mount",r)})}function Ph(e,t){e.forEach(function(r){var n=r.entry;return fm(n,t)})}function r1(e,t,r){var n=function(i){return e.filter(function(a){return i.includes(a.name)})};if(Array.isArray(t))su(n(t),r);else if(Ft(t))ie(V.mark(function o(){var i,a,s,u,c;return V.wrap(function(f){for(;;)switch(f.prev=f.next){case 0:return f.next=2,t(e);case 2:i=f.sent,a=i.criticalAppNames,s=a===void 0?[]:a,u=i.minorAppsName,c=u===void 0?[]:u,Ph(n(s),r),su(n(c),r);case 9:case"end":return f.stop()}},o)}))();else switch(t){case!0:su(e,r);break;case"all":Ph(e,r);break}}var n1=["name","activeRule","loader","props"],o1=["mount"],i1=["prefetch","urlRerouteOnly"],Ln=[],wt={},a1=!0,dm=new $g,s1=function(t){var r=t.sandbox,n=r===void 0?!0:r,o=t.singular;if(n){if(!window.Proxy)return console.warn("[qiankun] Missing window.Proxy, proxySandbox will degenerate into snapshotSandbox"),o===!1&&console.warn("[qiankun] Setting singular as false may cause unexpected behavior while your browser not support window.Proxy"),Te(Te({},t),{},{sandbox:Ie(n)==="object"?Te(Te({},n),{},{loose:!0}):{loose:!0}});if(!t0()&&(n===!0||Ie(n)==="object"&&n.speedy!==!1))return console.warn("[qiankun] Speedy mode will turn off as const destruct assignment not supported in current browser!"),Te(Te({},t),{},{sandbox:Ie(n)==="object"?Te(Te({},n),{},{speedy:!1}):{speedy:!1}})}return t};function u1(e,t){var r=e.filter(function(n){return!Ln.some(function(o){return o.name===n.name})});Ln=[].concat(pt(Ln),pt(r)),r.forEach(function(n){var o=n.name,i=n.activeRule,a=n.loader,s=a===void 0?rn:a,u=n.props,c=Vn(n,n1);tg({name:o,app:(function(){var l=ie(V.mark(function p(){var v,y,m;return V.wrap(function(b){for(;;)switch(b.prev=b.next){case 0:return s(!0),b.next=3,dm.promise;case 3:return b.next=5,Z0(Te({name:o,props:u},c),wt,t);case 5:return b.t0=b.sent,v=(0,b.t0)(),y=v.mount,m=Vn(v,o1),b.abrupt("return",Te({mount:[ie(V.mark(function _(){return V.wrap(function(E){for(;;)switch(E.prev=E.next){case 0:return E.abrupt("return",s(!0));case 1:case"end":return E.stop()}},_)}))].concat(pt(fr(y)),[ie(V.mark(function _(){return V.wrap(function(E){for(;;)switch(E.prev=E.next){case 0:return E.abrupt("return",s(!1));case 1:case"end":return E.stop()}},_)}))])},m));case 10:case"end":return b.stop()}},p)}));function f(){return l.apply(this,arguments)}return f})(),activeWhen:i,customProps:u})})}function c1(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};wt=Te({prefetch:!0,singular:!0,sandbox:!0},e);var t=wt,r=t.prefetch,n=t.urlRerouteOnly,o=n===void 0?a1:n,i=Vn(t,i1);r&&r1(Ln,r,i),wt=s1(wt),ag({urlRerouteOnly:o}),dm.resolve()}u1([{name:"react-playground",entry:"/subs/react-playground",container:"#__qiankun_container",activeRule:()=>"/subs/react-playground"}]);c1();const l1={class:"app"},f1={class:"card-body"},d1=We({__name:"App",setup(e){const t=jn([{title:"React Playground",desc:"在线编辑，在线预览",path:"/subs/react-playground"},{title:"乾坤通信",desc:"主应用与微应用之间的联系",path:"/qiankun-communication"}]),r=n=>{console.log("path======>",n,history),history.pushState({},"",n)};return(n,o)=>(Se(),Ye("div",l1,[be(ee(v_),{gutter:20},{default:De(()=>[be(ee(fl),{span:6},{default:De(()=>[be(ee(qo),{class:"card-common"},{header:De(()=>o[0]||(o[0]=[Pe("div",null,[Pe("span",null,"Copyer 项目集合")],-1)])),default:De(()=>[Pe("div",f1,[be(ee(w_),null,{default:De(()=>[(Se(!0),Ye(Be,null,_y(t.value,i=>(Se(),kt(ee(dl),{key:i.title,color:"#0bbd87",center:"",placement:"top"},{default:De(()=>[be(ee(qo),{class:"timeline-card",onClick:a=>r(i.path)},{default:De(()=>[Pe("h4",null,nr(i.title),1),Pe("p",null,nr(i.desc),1)]),_:2},1032,["onClick"])]),_:2},1024))),128)),be(ee(dl),{type:"primary",icon:ee(Zb)},{default:De(()=>o[1]||(o[1]=[Hn(" 敬请期待更多... ",-1)])),_:1,__:[1]},8,["icon"])]),_:1})])]),_:1})]),_:1}),be(ee(fl),{span:18},{default:De(()=>[be(ee(qo),{class:"card-common"},{header:De(()=>o[2]||(o[2]=[Pe("div",{style:{"text-align":"right"}},[Pe("span",null,"项目演示")],-1)])),default:De(()=>[o[3]||(o[3]=Pe("div",{class:"card-body"},[Pe("div",{id:"__qiankun_container"})],-1))]),_:1,__:[3]})]),_:1})]),_:1})]))}}),p1=(e,t)=>{const r=e.__vccOpts||e;for(const[n,o]of t)r[n]=o;return r},h1=p1(d1,[["__scopeId","data-v-d17d1741"]]);Ib(h1).mount("#app");
